"""
止损止盈管理模块
实现动态止损、移动止盈和风险控制
"""
import asyncio
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timezone, timedelta
from dataclasses import dataclass
from enum import Enum

from src.database.repositories import MarketDataRepository
from src.utils.logger import get_logger

logger = get_logger(__name__)


class StopLossType(Enum):
    """止损类型"""
    FIXED_PERCENTAGE = "fixed_percentage"
    FIXED_AMOUNT = "fixed_amount"
    VOLATILITY_BASED = "volatility_based"
    TECHNICAL_BASED = "technical_based"
    TIME_BASED = "time_based"
    TRAILING_STOP = "trailing_stop"


class TakeProfitType(Enum):
    """止盈类型"""
    FIXED_PERCENTAGE = "fixed_percentage"
    FIXED_AMOUNT = "fixed_amount"
    RISK_REWARD_RATIO = "risk_reward_ratio"
    TECHNICAL_BASED = "technical_based"
    TRAILING_PROFIT = "trailing_profit"
    PARTIAL_PROFIT = "partial_profit"


@dataclass
class StopLossConfig:
    """止损配置"""
    stop_type: StopLossType
    stop_percentage: float = 0.05  # 5%止损
    stop_amount: float = 0  # 固定金额止损
    volatility_multiplier: float = 2.0  # 波动率倍数
    lookback_period: int = 20  # 回看期
    trailing_percentage: float = 0.03  # 3%移动止损
    max_loss_per_trade: float = 0.02  # 单笔最大亏损2%
    time_stop_hours: int = 24  # 时间止损


@dataclass
class TakeProfitConfig:
    """止盈配置"""
    profit_type: TakeProfitType
    profit_percentage: float = 0.10  # 10%止盈
    profit_amount: float = 0  # 固定金额止盈
    risk_reward_ratio: float = 2.0  # 风险收益比
    trailing_percentage: float = 0.05  # 5%移动止盈
    partial_levels: List[Tuple[float, float]] = None  # 分批止盈 [(价格比例, 仓位比例)]


@dataclass
class StopOrder:
    """止损止盈订单"""
    id: str
    symbol: str
    position_id: str
    order_type: str  # stop_loss, take_profit
    trigger_price: float
    quantity: float
    created_at: datetime
    updated_at: datetime
    is_active: bool = True
    is_triggered: bool = False
    original_price: float = 0  # 开仓价格
    highest_price: float = 0  # 最高价格（用于移动止损）
    lowest_price: float = 0   # 最低价格（用于移动止盈）


class StopLossManager:
    """止损止盈管理器"""
    
    def __init__(self, stop_config: StopLossConfig, profit_config: TakeProfitConfig):
        self.stop_config = stop_config
        self.profit_config = profit_config
        self.market_data_repo = MarketDataRepository()
        
        # 活跃的止损止盈订单
        self.active_orders: Dict[str, StopOrder] = {}
        
        # 价格历史缓存
        self.price_cache: Dict[str, List[float]] = {}
        
        # 统计信息
        self.stats = {
            'total_stop_losses': 0,
            'total_take_profits': 0,
            'avg_stop_loss_pct': 0,
            'avg_take_profit_pct': 0,
            'stop_loss_accuracy': 0
        }
    
    async def create_stop_orders(self, symbol: str, position_id: str, 
                               entry_price: float, quantity: float,
                               position_side: str = 'long') -> List[StopOrder]:
        """创建止损止盈订单"""
        try:
            orders = []
            
            # 创建止损订单
            stop_loss_order = await self._create_stop_loss_order(
                symbol, position_id, entry_price, quantity, position_side
            )
            if stop_loss_order:
                orders.append(stop_loss_order)
                self.active_orders[stop_loss_order.id] = stop_loss_order
            
            # 创建止盈订单
            take_profit_orders = await self._create_take_profit_orders(
                symbol, position_id, entry_price, quantity, position_side
            )
            for order in take_profit_orders:
                orders.append(order)
                self.active_orders[order.id] = order
            
            logger.info(f"Created {len(orders)} stop orders for {symbol} position {position_id}")
            return orders
        
        except Exception as e:
            logger.error(f"Failed to create stop orders: {e}")
            return []
    
    async def _create_stop_loss_order(self, symbol: str, position_id: str,
                                    entry_price: float, quantity: float,
                                    position_side: str) -> Optional[StopOrder]:
        """创建止损订单"""
        try:
            if self.stop_config.stop_type == StopLossType.FIXED_PERCENTAGE:
                if position_side == 'long':
                    trigger_price = entry_price * (1 - self.stop_config.stop_percentage)
                else:
                    trigger_price = entry_price * (1 + self.stop_config.stop_percentage)
            
            elif self.stop_config.stop_type == StopLossType.FIXED_AMOUNT:
                if position_side == 'long':
                    trigger_price = entry_price - self.stop_config.stop_amount
                else:
                    trigger_price = entry_price + self.stop_config.stop_amount
            
            elif self.stop_config.stop_type == StopLossType.VOLATILITY_BASED:
                volatility = await self._calculate_volatility(symbol)
                stop_distance = entry_price * volatility * self.stop_config.volatility_multiplier
                
                if position_side == 'long':
                    trigger_price = entry_price - stop_distance
                else:
                    trigger_price = entry_price + stop_distance
            
            elif self.stop_config.stop_type == StopLossType.TECHNICAL_BASED:
                trigger_price = await self._calculate_technical_stop_loss(
                    symbol, entry_price, position_side
                )
            
            elif self.stop_config.stop_type == StopLossType.TRAILING_STOP:
                # 移动止损初始设置
                if position_side == 'long':
                    trigger_price = entry_price * (1 - self.stop_config.trailing_percentage)
                else:
                    trigger_price = entry_price * (1 + self.stop_config.trailing_percentage)
            
            else:
                # 默认固定百分比
                if position_side == 'long':
                    trigger_price = entry_price * (1 - self.stop_config.stop_percentage)
                else:
                    trigger_price = entry_price * (1 + self.stop_config.stop_percentage)
            
            order = StopOrder(
                id=f"sl_{position_id}_{int(datetime.now().timestamp())}",
                symbol=symbol,
                position_id=position_id,
                order_type='stop_loss',
                trigger_price=trigger_price,
                quantity=quantity,
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc),
                original_price=entry_price,
                highest_price=entry_price,
                lowest_price=entry_price
            )
            
            return order
        
        except Exception as e:
            logger.error(f"Failed to create stop loss order: {e}")
            return None
    
    async def _create_take_profit_orders(self, symbol: str, position_id: str,
                                       entry_price: float, quantity: float,
                                       position_side: str) -> List[StopOrder]:
        """创建止盈订单"""
        try:
            orders = []
            
            if self.profit_config.profit_type == TakeProfitType.PARTIAL_PROFIT:
                # 分批止盈
                partial_levels = self.profit_config.partial_levels or [
                    (0.05, 0.3),  # 5%涨幅时卖出30%
                    (0.10, 0.5),  # 10%涨幅时卖出50%
                    (0.20, 1.0)   # 20%涨幅时全部卖出
                ]
                
                for i, (price_pct, quantity_pct) in enumerate(partial_levels):
                    if position_side == 'long':
                        trigger_price = entry_price * (1 + price_pct)
                    else:
                        trigger_price = entry_price * (1 - price_pct)
                    
                    order_quantity = quantity * quantity_pct
                    
                    order = StopOrder(
                        id=f"tp_{position_id}_{i}_{int(datetime.now().timestamp())}",
                        symbol=symbol,
                        position_id=position_id,
                        order_type='take_profit',
                        trigger_price=trigger_price,
                        quantity=order_quantity,
                        created_at=datetime.now(timezone.utc),
                        updated_at=datetime.now(timezone.utc),
                        original_price=entry_price
                    )
                    orders.append(order)
            
            else:
                # 单一止盈订单
                if self.profit_config.profit_type == TakeProfitType.FIXED_PERCENTAGE:
                    if position_side == 'long':
                        trigger_price = entry_price * (1 + self.profit_config.profit_percentage)
                    else:
                        trigger_price = entry_price * (1 - self.profit_config.profit_percentage)
                
                elif self.profit_config.profit_type == TakeProfitType.RISK_REWARD_RATIO:
                    # 基于风险收益比
                    stop_distance = abs(entry_price - self.active_orders.get(f"sl_{position_id}", {}).get('trigger_price', entry_price))
                    profit_distance = stop_distance * self.profit_config.risk_reward_ratio
                    
                    if position_side == 'long':
                        trigger_price = entry_price + profit_distance
                    else:
                        trigger_price = entry_price - profit_distance
                
                elif self.profit_config.profit_type == TakeProfitType.TECHNICAL_BASED:
                    trigger_price = await self._calculate_technical_take_profit(
                        symbol, entry_price, position_side
                    )
                
                else:
                    # 默认固定百分比
                    if position_side == 'long':
                        trigger_price = entry_price * (1 + self.profit_config.profit_percentage)
                    else:
                        trigger_price = entry_price * (1 - self.profit_config.profit_percentage)
                
                order = StopOrder(
                    id=f"tp_{position_id}_{int(datetime.now().timestamp())}",
                    symbol=symbol,
                    position_id=position_id,
                    order_type='take_profit',
                    trigger_price=trigger_price,
                    quantity=quantity,
                    created_at=datetime.now(timezone.utc),
                    updated_at=datetime.now(timezone.utc),
                    original_price=entry_price
                )
                orders.append(order)
            
            return orders
        
        except Exception as e:
            logger.error(f"Failed to create take profit orders: {e}")
            return []
    
    async def update_stop_orders(self, current_prices: Dict[str, float]) -> List[Dict[str, Any]]:
        """更新止损止盈订单"""
        try:
            triggered_orders = []
            
            for order_id, order in list(self.active_orders.items()):
                if not order.is_active:
                    continue
                
                current_price = current_prices.get(order.symbol)
                if current_price is None:
                    continue
                
                # 更新价格历史
                order.highest_price = max(order.highest_price, current_price)
                order.lowest_price = min(order.lowest_price, current_price)
                
                # 检查移动止损
                if (order.order_type == 'stop_loss' and 
                    self.stop_config.stop_type == StopLossType.TRAILING_STOP):
                    await self._update_trailing_stop(order, current_price)
                
                # 检查移动止盈
                if (order.order_type == 'take_profit' and 
                    self.profit_config.profit_type == TakeProfitType.TRAILING_PROFIT):
                    await self._update_trailing_profit(order, current_price)
                
                # 检查触发条件
                if self._check_trigger_condition(order, current_price):
                    order.is_triggered = True
                    order.is_active = False
                    order.updated_at = datetime.now(timezone.utc)
                    
                    triggered_orders.append({
                        'order_id': order.id,
                        'symbol': order.symbol,
                        'position_id': order.position_id,
                        'order_type': order.order_type,
                        'trigger_price': order.trigger_price,
                        'current_price': current_price,
                        'quantity': order.quantity,
                        'pnl': self._calculate_pnl(order, current_price)
                    })
                    
                    # 更新统计
                    self._update_stats(order, current_price)
            
            return triggered_orders
        
        except Exception as e:
            logger.error(f"Failed to update stop orders: {e}")
            return []
    
    async def _update_trailing_stop(self, order: StopOrder, current_price: float) -> None:
        """更新移动止损"""
        try:
            # 假设是多头仓位
            if current_price > order.highest_price:
                # 价格创新高，更新移动止损
                new_stop_price = current_price * (1 - self.stop_config.trailing_percentage)
                
                if new_stop_price > order.trigger_price:
                    order.trigger_price = new_stop_price
                    order.updated_at = datetime.now(timezone.utc)
                    logger.debug(f"Updated trailing stop for {order.symbol}: {new_stop_price:.4f}")
        
        except Exception as e:
            logger.error(f"Failed to update trailing stop: {e}")
    
    async def _update_trailing_profit(self, order: StopOrder, current_price: float) -> None:
        """更新移动止盈"""
        try:
            # 假设是多头仓位
            if current_price < order.highest_price:
                # 价格从高点回落，更新移动止盈
                pullback_pct = (order.highest_price - current_price) / order.highest_price
                
                if pullback_pct >= self.profit_config.trailing_percentage:
                    order.trigger_price = current_price
                    order.updated_at = datetime.now(timezone.utc)
                    logger.debug(f"Updated trailing profit for {order.symbol}: {current_price:.4f}")
        
        except Exception as e:
            logger.error(f"Failed to update trailing profit: {e}")
    
    def _check_trigger_condition(self, order: StopOrder, current_price: float) -> bool:
        """检查触发条件"""
        try:
            if order.order_type == 'stop_loss':
                # 止损触发条件（假设多头）
                return current_price <= order.trigger_price
            
            elif order.order_type == 'take_profit':
                # 止盈触发条件（假设多头）
                return current_price >= order.trigger_price
            
            return False
        
        except Exception as e:
            logger.error(f"Failed to check trigger condition: {e}")
            return False
    
    def _calculate_pnl(self, order: StopOrder, current_price: float) -> float:
        """计算盈亏"""
        try:
            # 简化计算（假设多头）
            pnl_per_unit = current_price - order.original_price
            total_pnl = pnl_per_unit * order.quantity
            return total_pnl
        
        except Exception as e:
            logger.error(f"Failed to calculate PnL: {e}")
            return 0
    
    def _update_stats(self, order: StopOrder, trigger_price: float) -> None:
        """更新统计信息"""
        try:
            if order.order_type == 'stop_loss':
                self.stats['total_stop_losses'] += 1
                pnl_pct = (trigger_price - order.original_price) / order.original_price
                self.stats['avg_stop_loss_pct'] = (
                    (self.stats['avg_stop_loss_pct'] * (self.stats['total_stop_losses'] - 1) + pnl_pct) /
                    self.stats['total_stop_losses']
                )
            
            elif order.order_type == 'take_profit':
                self.stats['total_take_profits'] += 1
                pnl_pct = (trigger_price - order.original_price) / order.original_price
                self.stats['avg_take_profit_pct'] = (
                    (self.stats['avg_take_profit_pct'] * (self.stats['total_take_profits'] - 1) + pnl_pct) /
                    self.stats['total_take_profits']
                )
        
        except Exception as e:
            logger.error(f"Failed to update stats: {e}")
    
    async def _calculate_volatility(self, symbol: str) -> float:
        """计算波动率"""
        try:
            # 获取历史价格数据
            price_history = self.market_data_repo.get_price_history(
                symbol, days=self.stop_config.lookback_period
            )
            
            if not price_history:
                return 0.3  # 默认30%波动率
            
            prices = [p.price_usd for p in price_history]
            returns = pd.Series(prices).pct_change().dropna()
            volatility = returns.std() * np.sqrt(252)  # 年化波动率
            
            return volatility
        
        except Exception as e:
            logger.error(f"Failed to calculate volatility: {e}")
            return 0.3
    
    async def _calculate_technical_stop_loss(self, symbol: str, entry_price: float, 
                                           position_side: str) -> float:
        """计算技术止损位"""
        try:
            # 简化实现：基于支撑阻力位
            price_history = self.market_data_repo.get_price_history(symbol, days=20)
            
            if not price_history:
                # 回退到固定百分比
                if position_side == 'long':
                    return entry_price * (1 - self.stop_config.stop_percentage)
                else:
                    return entry_price * (1 + self.stop_config.stop_percentage)
            
            prices = [p.price_usd for p in price_history]
            
            if position_side == 'long':
                # 找最近的支撑位
                support_level = min(prices[-10:])  # 最近10个价格的最低点
                return min(support_level * 0.98, entry_price * (1 - self.stop_config.stop_percentage))
            else:
                # 找最近的阻力位
                resistance_level = max(prices[-10:])  # 最近10个价格的最高点
                return max(resistance_level * 1.02, entry_price * (1 + self.stop_config.stop_percentage))
        
        except Exception as e:
            logger.error(f"Failed to calculate technical stop loss: {e}")
            if position_side == 'long':
                return entry_price * (1 - self.stop_config.stop_percentage)
            else:
                return entry_price * (1 + self.stop_config.stop_percentage)
    
    async def _calculate_technical_take_profit(self, symbol: str, entry_price: float,
                                             position_side: str) -> float:
        """计算技术止盈位"""
        try:
            # 简化实现：基于阻力支撑位
            price_history = self.market_data_repo.get_price_history(symbol, days=20)
            
            if not price_history:
                # 回退到固定百分比
                if position_side == 'long':
                    return entry_price * (1 + self.profit_config.profit_percentage)
                else:
                    return entry_price * (1 - self.profit_config.profit_percentage)
            
            prices = [p.price_usd for p in price_history]
            
            if position_side == 'long':
                # 找最近的阻力位
                resistance_level = max(prices[-10:])
                return max(resistance_level * 0.98, entry_price * (1 + self.profit_config.profit_percentage))
            else:
                # 找最近的支撑位
                support_level = min(prices[-10:])
                return min(support_level * 1.02, entry_price * (1 - self.profit_config.profit_percentage))
        
        except Exception as e:
            logger.error(f"Failed to calculate technical take profit: {e}")
            if position_side == 'long':
                return entry_price * (1 + self.profit_config.profit_percentage)
            else:
                return entry_price * (1 - self.profit_config.profit_percentage)
    
    def cancel_stop_orders(self, position_id: str) -> List[str]:
        """取消指定仓位的止损止盈订单"""
        try:
            cancelled_orders = []
            
            for order_id, order in list(self.active_orders.items()):
                if order.position_id == position_id and order.is_active:
                    order.is_active = False
                    order.updated_at = datetime.now(timezone.utc)
                    cancelled_orders.append(order_id)
            
            logger.info(f"Cancelled {len(cancelled_orders)} stop orders for position {position_id}")
            return cancelled_orders
        
        except Exception as e:
            logger.error(f"Failed to cancel stop orders: {e}")
            return []
    
    def get_active_orders(self, symbol: str = None) -> List[StopOrder]:
        """获取活跃的止损止盈订单"""
        try:
            active_orders = [
                order for order in self.active_orders.values()
                if order.is_active and (symbol is None or order.symbol == symbol)
            ]
            return active_orders
        
        except Exception as e:
            logger.error(f"Failed to get active orders: {e}")
            return []
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取止损止盈统计"""
        try:
            total_orders = self.stats['total_stop_losses'] + self.stats['total_take_profits']
            
            return {
                'total_stop_losses': self.stats['total_stop_losses'],
                'total_take_profits': self.stats['total_take_profits'],
                'total_orders': total_orders,
                'avg_stop_loss_pct': self.stats['avg_stop_loss_pct'],
                'avg_take_profit_pct': self.stats['avg_take_profit_pct'],
                'stop_profit_ratio': (
                    self.stats['total_stop_losses'] / self.stats['total_take_profits']
                    if self.stats['total_take_profits'] > 0 else 0
                ),
                'active_orders_count': len([o for o in self.active_orders.values() if o.is_active]),
                'config': {
                    'stop_loss_type': self.stop_config.stop_type.value,
                    'stop_percentage': self.stop_config.stop_percentage,
                    'take_profit_type': self.profit_config.profit_type.value,
                    'profit_percentage': self.profit_config.profit_percentage
                }
            }
        
        except Exception as e:
            logger.error(f"Failed to get statistics: {e}")
            return {}
