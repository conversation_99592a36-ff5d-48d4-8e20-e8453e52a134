"""
技术分析指标计算模块
计算传统技术分析指标，如移动平均线、RSI、MACD等
"""
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timezone, timedelta
from dataclasses import dataclass

from src.database.repositories import MarketDataRepository
from src.utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class TechnicalIndicators:
    """技术指标数据"""
    symbol: str
    timestamp: datetime
    price: float
    
    # 趋势指标
    sma_20: float
    sma_50: float
    sma_200: float
    ema_12: float
    ema_26: float
    
    # 动量指标
    rsi_14: float
    macd: float
    macd_signal: float
    macd_histogram: float
    
    # 波动率指标
    bollinger_upper: float
    bollinger_middle: float
    bollinger_lower: float
    atr_14: float
    
    # 成交量指标
    volume_sma_20: float
    volume_ratio: float
    
    # 支撑阻力
    support_level: float
    resistance_level: float
    
    # 综合信号
    trend_signal: str  # bullish, bearish, neutral
    momentum_signal: str
    overall_signal: str


class TechnicalIndicatorCalculator:
    """技术指标计算器"""
    
    def __init__(self):
        self.market_data_repo = MarketDataRepository()
    
    async def calculate_all_indicators(self, symbol: str, days: int = 200) -> List[TechnicalIndicators]:
        """计算所有技术指标"""
        try:
            logger.info(f"Calculating technical indicators for {symbol} over {days} days")
            
            # 获取历史价格数据
            price_history = self.market_data_repo.get_price_history(symbol, days=days)
            
            if len(price_history) < 50:  # 需要足够的历史数据
                logger.warning(f"Insufficient price history for {symbol}")
                return []
            
            # 转换为DataFrame
            df = pd.DataFrame([
                {
                    'timestamp': p.timestamp,
                    'price': p.price_usd,
                    'volume': p.volume_24h or 0,
                    'high': p.price_usd * 1.02,  # 简化处理
                    'low': p.price_usd * 0.98,   # 简化处理
                    'close': p.price_usd
                }
                for p in price_history
            ])
            
            df = df.sort_values('timestamp').reset_index(drop=True)
            
            # 计算各种技术指标
            df = self._calculate_moving_averages(df)
            df = self._calculate_momentum_indicators(df)
            df = self._calculate_volatility_indicators(df)
            df = self._calculate_volume_indicators(df)
            df = self._calculate_support_resistance(df)
            df = self._generate_signals(df)
            
            # 转换为TechnicalIndicators对象
            indicators = []
            for _, row in df.iterrows():
                if pd.notna(row['sma_200']):  # 确保有足够数据计算所有指标
                    indicator = TechnicalIndicators(
                        symbol=symbol,
                        timestamp=row['timestamp'],
                        price=row['price'],
                        sma_20=row['sma_20'],
                        sma_50=row['sma_50'],
                        sma_200=row['sma_200'],
                        ema_12=row['ema_12'],
                        ema_26=row['ema_26'],
                        rsi_14=row['rsi_14'],
                        macd=row['macd'],
                        macd_signal=row['macd_signal'],
                        macd_histogram=row['macd_histogram'],
                        bollinger_upper=row['bollinger_upper'],
                        bollinger_middle=row['bollinger_middle'],
                        bollinger_lower=row['bollinger_lower'],
                        atr_14=row['atr_14'],
                        volume_sma_20=row['volume_sma_20'],
                        volume_ratio=row['volume_ratio'],
                        support_level=row['support_level'],
                        resistance_level=row['resistance_level'],
                        trend_signal=row['trend_signal'],
                        momentum_signal=row['momentum_signal'],
                        overall_signal=row['overall_signal']
                    )
                    indicators.append(indicator)
            
            logger.info(f"Calculated technical indicators for {len(indicators)} periods")
            return indicators
        
        except Exception as e:
            logger.error(f"Failed to calculate technical indicators: {e}")
            raise
    
    def _calculate_moving_averages(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算移动平均线"""
        try:
            # 简单移动平均线
            df['sma_20'] = df['price'].rolling(window=20).mean()
            df['sma_50'] = df['price'].rolling(window=50).mean()
            df['sma_200'] = df['price'].rolling(window=200).mean()
            
            # 指数移动平均线
            df['ema_12'] = df['price'].ewm(span=12).mean()
            df['ema_26'] = df['price'].ewm(span=26).mean()
            
            return df
        
        except Exception as e:
            logger.error(f"Failed to calculate moving averages: {e}")
            return df
    
    def _calculate_momentum_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算动量指标"""
        try:
            # RSI
            df['rsi_14'] = self._calculate_rsi(df['price'], 14)
            
            # MACD
            df['macd'] = df['ema_12'] - df['ema_26']
            df['macd_signal'] = df['macd'].ewm(span=9).mean()
            df['macd_histogram'] = df['macd'] - df['macd_signal']
            
            return df
        
        except Exception as e:
            logger.error(f"Failed to calculate momentum indicators: {e}")
            return df
    
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """计算RSI指标"""
        try:
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            
            return rsi
        
        except Exception as e:
            logger.error(f"Failed to calculate RSI: {e}")
            return pd.Series([50] * len(prices))
    
    def _calculate_volatility_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算波动率指标"""
        try:
            # 布林带
            df['bollinger_middle'] = df['price'].rolling(window=20).mean()
            bollinger_std = df['price'].rolling(window=20).std()
            df['bollinger_upper'] = df['bollinger_middle'] + (bollinger_std * 2)
            df['bollinger_lower'] = df['bollinger_middle'] - (bollinger_std * 2)
            
            # ATR (平均真实范围)
            df['tr'] = np.maximum(
                df['high'] - df['low'],
                np.maximum(
                    abs(df['high'] - df['close'].shift(1)),
                    abs(df['low'] - df['close'].shift(1))
                )
            )
            df['atr_14'] = df['tr'].rolling(window=14).mean()
            
            return df
        
        except Exception as e:
            logger.error(f"Failed to calculate volatility indicators: {e}")
            return df
    
    def _calculate_volume_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算成交量指标"""
        try:
            # 成交量移动平均
            df['volume_sma_20'] = df['volume'].rolling(window=20).mean()
            
            # 成交量比率
            df['volume_ratio'] = df['volume'] / df['volume_sma_20']
            df['volume_ratio'] = df['volume_ratio'].fillna(1.0)
            
            return df
        
        except Exception as e:
            logger.error(f"Failed to calculate volume indicators: {e}")
            return df
    
    def _calculate_support_resistance(self, df: pd.DataFrame, window: int = 20) -> pd.DataFrame:
        """计算支撑阻力位"""
        try:
            # 简化的支撑阻力计算
            df['support_level'] = df['low'].rolling(window=window).min()
            df['resistance_level'] = df['high'].rolling(window=window).max()
            
            return df
        
        except Exception as e:
            logger.error(f"Failed to calculate support resistance: {e}")
            return df
    
    def _generate_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        """生成交易信号"""
        try:
            # 趋势信号
            df['trend_signal'] = 'neutral'
            
            # 基于移动平均线的趋势判断
            bullish_trend = (
                (df['price'] > df['sma_20']) &
                (df['sma_20'] > df['sma_50']) &
                (df['sma_50'] > df['sma_200'])
            )
            
            bearish_trend = (
                (df['price'] < df['sma_20']) &
                (df['sma_20'] < df['sma_50']) &
                (df['sma_50'] < df['sma_200'])
            )
            
            df.loc[bullish_trend, 'trend_signal'] = 'bullish'
            df.loc[bearish_trend, 'trend_signal'] = 'bearish'
            
            # 动量信号
            df['momentum_signal'] = 'neutral'
            
            # 基于RSI和MACD的动量判断
            bullish_momentum = (
                (df['rsi_14'] > 30) & (df['rsi_14'] < 70) &
                (df['macd'] > df['macd_signal']) &
                (df['macd_histogram'] > 0)
            )
            
            bearish_momentum = (
                (df['rsi_14'] > 30) & (df['rsi_14'] < 70) &
                (df['macd'] < df['macd_signal']) &
                (df['macd_histogram'] < 0)
            )
            
            df.loc[bullish_momentum, 'momentum_signal'] = 'bullish'
            df.loc[bearish_momentum, 'momentum_signal'] = 'bearish'
            
            # 综合信号
            df['overall_signal'] = 'neutral'
            
            # 趋势和动量都看涨
            strong_bullish = (
                (df['trend_signal'] == 'bullish') &
                (df['momentum_signal'] == 'bullish')
            )
            
            # 趋势和动量都看跌
            strong_bearish = (
                (df['trend_signal'] == 'bearish') &
                (df['momentum_signal'] == 'bearish')
            )
            
            # 趋势或动量看涨
            weak_bullish = (
                ((df['trend_signal'] == 'bullish') & (df['momentum_signal'] == 'neutral')) |
                ((df['trend_signal'] == 'neutral') & (df['momentum_signal'] == 'bullish'))
            )
            
            # 趋势或动量看跌
            weak_bearish = (
                ((df['trend_signal'] == 'bearish') & (df['momentum_signal'] == 'neutral')) |
                ((df['trend_signal'] == 'neutral') & (df['momentum_signal'] == 'bearish'))
            )
            
            df.loc[strong_bullish, 'overall_signal'] = 'strong_bullish'
            df.loc[strong_bearish, 'overall_signal'] = 'strong_bearish'
            df.loc[weak_bullish, 'overall_signal'] = 'weak_bullish'
            df.loc[weak_bearish, 'overall_signal'] = 'weak_bearish'
            
            return df
        
        except Exception as e:
            logger.error(f"Failed to generate signals: {e}")
            return df
    
    async def get_current_signals(self, symbol: str) -> Dict[str, Any]:
        """获取当前技术信号"""
        try:
            indicators = await self.calculate_all_indicators(symbol, days=50)
            
            if not indicators:
                return {}
            
            latest = indicators[-1]
            
            return {
                'symbol': symbol,
                'timestamp': latest.timestamp,
                'price': latest.price,
                'trend_signal': latest.trend_signal,
                'momentum_signal': latest.momentum_signal,
                'overall_signal': latest.overall_signal,
                'rsi': latest.rsi_14,
                'macd_signal': 'bullish' if latest.macd > latest.macd_signal else 'bearish',
                'bollinger_position': self._get_bollinger_position(latest),
                'volume_signal': 'high' if latest.volume_ratio > 1.5 else 'normal',
                'support_level': latest.support_level,
                'resistance_level': latest.resistance_level
            }
        
        except Exception as e:
            logger.error(f"Failed to get current signals: {e}")
            return {}
    
    def _get_bollinger_position(self, indicator: TechnicalIndicators) -> str:
        """获取布林带位置"""
        try:
            price = indicator.price
            upper = indicator.bollinger_upper
            lower = indicator.bollinger_lower
            middle = indicator.bollinger_middle
            
            if price > upper:
                return 'above_upper'
            elif price < lower:
                return 'below_lower'
            elif price > middle:
                return 'above_middle'
            else:
                return 'below_middle'
        
        except Exception as e:
            logger.error(f"Failed to get bollinger position: {e}")
            return 'unknown'
    
    async def detect_patterns(self, symbol: str, days: int = 50) -> List[Dict[str, Any]]:
        """检测技术形态"""
        try:
            indicators = await self.calculate_all_indicators(symbol, days=days)
            
            if len(indicators) < 20:
                return []
            
            patterns = []
            
            # 检测金叉死叉
            for i in range(1, len(indicators)):
                current = indicators[i]
                previous = indicators[i-1]
                
                # MACD金叉
                if (current.macd > current.macd_signal and 
                    previous.macd <= previous.macd_signal):
                    patterns.append({
                        'type': 'macd_golden_cross',
                        'timestamp': current.timestamp,
                        'price': current.price,
                        'signal': 'bullish'
                    })
                
                # MACD死叉
                if (current.macd < current.macd_signal and 
                    previous.macd >= previous.macd_signal):
                    patterns.append({
                        'type': 'macd_death_cross',
                        'timestamp': current.timestamp,
                        'price': current.price,
                        'signal': 'bearish'
                    })
                
                # RSI超买超卖
                if current.rsi_14 > 70 and previous.rsi_14 <= 70:
                    patterns.append({
                        'type': 'rsi_overbought',
                        'timestamp': current.timestamp,
                        'price': current.price,
                        'signal': 'bearish'
                    })
                
                if current.rsi_14 < 30 and previous.rsi_14 >= 30:
                    patterns.append({
                        'type': 'rsi_oversold',
                        'timestamp': current.timestamp,
                        'price': current.price,
                        'signal': 'bullish'
                    })
            
            return patterns
        
        except Exception as e:
            logger.error(f"Failed to detect patterns: {e}")
            return []
    
    async def calculate_pivot_points(self, symbol: str) -> Dict[str, float]:
        """计算枢轴点"""
        try:
            # 获取最近的价格数据
            price_history = self.market_data_repo.get_price_history(symbol, days=2)
            
            if len(price_history) < 1:
                return {}
            
            latest = price_history[-1]
            high = latest.price_usd * 1.02  # 简化处理
            low = latest.price_usd * 0.98
            close = latest.price_usd
            
            # 计算枢轴点
            pivot = (high + low + close) / 3
            
            # 支撑位
            s1 = (2 * pivot) - high
            s2 = pivot - (high - low)
            s3 = low - 2 * (high - pivot)
            
            # 阻力位
            r1 = (2 * pivot) - low
            r2 = pivot + (high - low)
            r3 = high + 2 * (pivot - low)
            
            return {
                'pivot': pivot,
                'support_1': s1,
                'support_2': s2,
                'support_3': s3,
                'resistance_1': r1,
                'resistance_2': r2,
                'resistance_3': r3
            }
        
        except Exception as e:
            logger.error(f"Failed to calculate pivot points: {e}")
            return {}
