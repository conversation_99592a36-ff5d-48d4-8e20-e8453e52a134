"""
预警系统
处理和发送各种类型的预警通知
"""
import asyncio
import json
from typing import Dict, List, Optional, Any
from datetime import datetime, timezone, timedelta
from dataclasses import dataclass
from enum import Enum

from .whale_monitor import WhaleTransaction, AlertLevel
from src.database.repositories import WhaleAlertRepository
from src.utils.logger import get_logger
from config.settings import NOTIFICATION_CONFIG

logger = get_logger(__name__)


class NotificationChannel(Enum):
    """通知渠道"""
    EMAIL = "email"
    TELEGRAM = "telegram"
    DISCORD = "discord"
    WEBHOOK = "webhook"
    SMS = "sms"


@dataclass
class AlertRule:
    """预警规则"""
    name: str
    description: str
    conditions: Dict[str, Any]
    channels: List[NotificationChannel]
    enabled: bool = True
    cooldown_minutes: int = 60  # 冷却时间，避免重复通知


@dataclass
class NotificationMessage:
    """通知消息"""
    title: str
    content: str
    alert_level: AlertLevel
    channel: NotificationChannel
    metadata: Dict[str, Any]
    timestamp: datetime


class AlertSystem:
    """预警系统"""
    
    def __init__(self):
        self.whale_alert_repo = WhaleAlertRepository()
        
        # 预警规则
        self.alert_rules = self._initialize_alert_rules()
        
        # 通知历史（用于冷却时间控制）
        self.notification_history = {}
    
    def _initialize_alert_rules(self) -> List[AlertRule]:
        """初始化预警规则"""
        return [
            AlertRule(
                name="critical_whale_movement",
                description="关键巨鲸活动预警",
                conditions={
                    "alert_level": "critical",
                    "min_amount_usd": 50000000
                },
                channels=[NotificationChannel.TELEGRAM, NotificationChannel.EMAIL],
                cooldown_minutes=30
            ),
            AlertRule(
                name="large_exchange_flow",
                description="大额交易所资金流动",
                conditions={
                    "alert_type": ["exchange_inflow", "exchange_outflow"],
                    "min_amount_usd": 10000000
                },
                channels=[NotificationChannel.TELEGRAM],
                cooldown_minutes=60
            ),
            AlertRule(
                name="dormant_whale_activation",
                description="休眠巨鲸激活",
                conditions={
                    "alert_type": "dormant_activation",
                    "min_amount_usd": 1000000
                },
                channels=[NotificationChannel.TELEGRAM, NotificationChannel.DISCORD],
                cooldown_minutes=120
            ),
            AlertRule(
                name="smart_money_follow",
                description="Smart Money跟随提醒",
                conditions={
                    "alert_type": "smart_money_activity",
                    "confidence": 0.8
                },
                channels=[NotificationChannel.TELEGRAM],
                cooldown_minutes=30
            )
        ]
    
    async def process_whale_alerts(self, hours: int = 1) -> List[NotificationMessage]:
        """处理巨鲸预警"""
        try:
            logger.info(f"Processing whale alerts for the last {hours} hours")
            
            # 获取未处理的预警
            unprocessed_alerts = self.whale_alert_repo.get_unprocessed_alerts(limit=100)
            
            notifications = []
            
            for alert in unprocessed_alerts:
                try:
                    # 检查是否符合预警规则
                    matching_rules = self._match_alert_rules(alert)
                    
                    for rule in matching_rules:
                        # 检查冷却时间
                        if self._is_in_cooldown(rule, alert):
                            continue
                        
                        # 生成通知消息
                        for channel in rule.channels:
                            message = self._create_notification_message(alert, rule, channel)
                            if message:
                                notifications.append(message)
                                
                                # 发送通知
                                await self._send_notification(message)
                                
                                # 记录通知历史
                                self._record_notification(rule, alert)
                    
                    # 标记预警为已处理
                    self.whale_alert_repo.mark_alert_processed(alert.id, notification_sent=True)
                
                except Exception as e:
                    logger.error(f"Failed to process alert {alert.id}: {e}")
                    continue
            
            logger.info(f"Processed {len(unprocessed_alerts)} alerts, sent {len(notifications)} notifications")
            return notifications
        
        except Exception as e:
            logger.error(f"Failed to process whale alerts: {e}")
            return []
    
    def _match_alert_rules(self, alert: Any) -> List[AlertRule]:
        """匹配预警规则"""
        matching_rules = []
        
        for rule in self.alert_rules:
            if not rule.enabled:
                continue
            
            conditions = rule.conditions
            match = True
            
            # 检查预警级别
            if 'alert_level' in conditions:
                if alert.alert_level != conditions['alert_level']:
                    match = False
            
            # 检查金额阈值
            if 'min_amount_usd' in conditions:
                if alert.amount_usd < conditions['min_amount_usd']:
                    match = False
            
            # 检查预警类型
            if 'alert_type' in conditions:
                alert_types = conditions['alert_type']
                if isinstance(alert_types, str):
                    alert_types = [alert_types]
                if alert.alert_type not in alert_types:
                    match = False
            
            # 检查置信度（如果有）
            if 'confidence' in conditions:
                alert_confidence = getattr(alert, 'confidence', 0)
                if alert_confidence < conditions['confidence']:
                    match = False
            
            if match:
                matching_rules.append(rule)
        
        return matching_rules
    
    def _is_in_cooldown(self, rule: AlertRule, alert: Any) -> bool:
        """检查是否在冷却时间内"""
        key = f"{rule.name}_{alert.alert_type}_{alert.from_address}"
        
        if key in self.notification_history:
            last_notification = self.notification_history[key]
            cooldown_end = last_notification + timedelta(minutes=rule.cooldown_minutes)
            
            if datetime.now(timezone.utc) < cooldown_end:
                return True
        
        return False
    
    def _record_notification(self, rule: AlertRule, alert: Any) -> None:
        """记录通知历史"""
        key = f"{rule.name}_{alert.alert_type}_{alert.from_address}"
        self.notification_history[key] = datetime.now(timezone.utc)
    
    def _create_notification_message(self, alert: Any, rule: AlertRule, 
                                   channel: NotificationChannel) -> Optional[NotificationMessage]:
        """创建通知消息"""
        try:
            # 根据预警类型生成标题和内容
            if alert.alert_type == 'exchange_outflow':
                title = f"🐋 大额交易所流出预警"
                content = self._format_exchange_outflow_message(alert)
            elif alert.alert_type == 'exchange_inflow':
                title = f"🏦 大额交易所流入预警"
                content = self._format_exchange_inflow_message(alert)
            elif alert.alert_type == 'large_transfer':
                title = f"💰 超大额转账预警"
                content = self._format_large_transfer_message(alert)
            elif alert.alert_type == 'dormant_activation':
                title = f"😴 休眠巨鲸激活预警"
                content = self._format_dormant_activation_message(alert)
            else:
                title = f"🚨 巨鲸活动预警"
                content = self._format_generic_whale_message(alert)
            
            # 根据渠道调整格式
            if channel == NotificationChannel.TELEGRAM:
                content = self._format_for_telegram(content)
            elif channel == NotificationChannel.DISCORD:
                content = self._format_for_discord(content)
            
            return NotificationMessage(
                title=title,
                content=content,
                alert_level=AlertLevel(alert.alert_level),
                channel=channel,
                metadata={
                    'alert_id': alert.id,
                    'tx_hash': alert.tx_hash,
                    'rule_name': rule.name
                },
                timestamp=datetime.now(timezone.utc)
            )
        
        except Exception as e:
            logger.error(f"Failed to create notification message: {e}")
            return None
    
    def _format_exchange_outflow_message(self, alert: Any) -> str:
        """格式化交易所流出消息"""
        return f"""
💸 **交易所大额流出**

**金额**: ${alert.amount_usd:,.0f} {alert.token_symbol}
**交易所**: {alert.exchange_name or '未知'}
**目标地址**: `{alert.to_address[:10]}...{alert.to_address[-6:]}`
**交易哈希**: `{alert.tx_hash[:10]}...{alert.tx_hash[-6:]}`
**预警级别**: {alert.alert_level.upper()}

⚠️ 大量资金流出交易所可能表明：
• 机构或巨鲸准备长期持有
• 可能减少市场抛售压力
• 需要关注后续价格走势
        """.strip()
    
    def _format_exchange_inflow_message(self, alert: Any) -> str:
        """格式化交易所流入消息"""
        return f"""
🏦 **交易所大额流入**

**金额**: ${alert.amount_usd:,.0f} {alert.token_symbol}
**交易所**: {alert.exchange_name or '未知'}
**来源地址**: `{alert.from_address[:10]}...{alert.from_address[-6:]}`
**交易哈希**: `{alert.tx_hash[:10]}...{alert.tx_hash[-6:]}`
**预警级别**: {alert.alert_level.upper()}

⚠️ 大量资金流入交易所可能表明：
• 准备出售或交易
• 可能增加市场抛售压力
• 建议密切关注价格变化
        """.strip()
    
    def _format_large_transfer_message(self, alert: Any) -> str:
        """格式化大额转账消息"""
        return f"""
💰 **超大额转账**

**金额**: ${alert.amount_usd:,.0f} {alert.token_symbol}
**从**: `{alert.from_address[:10]}...{alert.from_address[-6:]}`
**到**: `{alert.to_address[:10]}...{alert.to_address[-6:]}`
**交易哈希**: `{alert.tx_hash[:10]}...{alert.tx_hash[-6:]}`
**预警级别**: {alert.alert_level.upper()}

🔍 建议进一步分析：
• 查看地址历史活动
• 确认是否为已知机构
• 关注市场反应
        """.strip()
    
    def _format_dormant_activation_message(self, alert: Any) -> str:
        """格式化休眠激活消息"""
        return f"""
😴 **休眠巨鲸激活**

**金额**: ${alert.amount_usd:,.0f} {alert.token_symbol}
**地址**: `{alert.from_address[:10]}...{alert.from_address[-6:]}`
**交易哈希**: `{alert.tx_hash[:10]}...{alert.tx_hash[-6:]}`
**预警级别**: {alert.alert_level.upper()}

🚨 **高度关注**：
• 长期休眠地址突然激活
• 可能是重大市场信号
• 建议立即分析地址历史
        """.strip()
    
    def _format_generic_whale_message(self, alert: Any) -> str:
        """格式化通用巨鲸消息"""
        return f"""
🐋 **巨鲸活动检测**

**类型**: {alert.alert_type}
**金额**: ${alert.amount_usd:,.0f} {alert.token_symbol}
**从**: `{alert.from_address[:10]}...{alert.from_address[-6:]}`
**到**: `{alert.to_address[:10]}...{alert.to_address[-6:]}`
**交易哈希**: `{alert.tx_hash[:10]}...{alert.tx_hash[-6:]}`
**预警级别**: {alert.alert_level.upper()}
        """.strip()
    
    def _format_for_telegram(self, content: str) -> str:
        """为Telegram格式化消息"""
        # Telegram支持Markdown格式
        return content
    
    def _format_for_discord(self, content: str) -> str:
        """为Discord格式化消息"""
        # Discord也支持Markdown格式
        return content
    
    async def _send_notification(self, message: NotificationMessage) -> bool:
        """发送通知"""
        try:
            if message.channel == NotificationChannel.TELEGRAM:
                return await self._send_telegram_notification(message)
            elif message.channel == NotificationChannel.DISCORD:
                return await self._send_discord_notification(message)
            elif message.channel == NotificationChannel.EMAIL:
                return await self._send_email_notification(message)
            elif message.channel == NotificationChannel.WEBHOOK:
                return await self._send_webhook_notification(message)
            else:
                logger.warning(f"Unsupported notification channel: {message.channel}")
                return False
        
        except Exception as e:
            logger.error(f"Failed to send notification via {message.channel}: {e}")
            return False
    
    async def _send_telegram_notification(self, message: NotificationMessage) -> bool:
        """发送Telegram通知"""
        try:
            # 这里应该实现实际的Telegram API调用
            # 现在只是记录日志
            logger.info(f"[TELEGRAM] {message.title}")
            logger.info(f"Content: {message.content}")
            
            # 模拟发送成功
            return True
        
        except Exception as e:
            logger.error(f"Failed to send Telegram notification: {e}")
            return False
    
    async def _send_discord_notification(self, message: NotificationMessage) -> bool:
        """发送Discord通知"""
        try:
            # 这里应该实现实际的Discord Webhook调用
            logger.info(f"[DISCORD] {message.title}")
            logger.info(f"Content: {message.content}")
            
            return True
        
        except Exception as e:
            logger.error(f"Failed to send Discord notification: {e}")
            return False
    
    async def _send_email_notification(self, message: NotificationMessage) -> bool:
        """发送邮件通知"""
        try:
            # 这里应该实现实际的邮件发送
            logger.info(f"[EMAIL] {message.title}")
            logger.info(f"Content: {message.content}")
            
            return True
        
        except Exception as e:
            logger.error(f"Failed to send email notification: {e}")
            return False
    
    async def _send_webhook_notification(self, message: NotificationMessage) -> bool:
        """发送Webhook通知"""
        try:
            # 这里应该实现实际的Webhook调用
            webhook_data = {
                'title': message.title,
                'content': message.content,
                'alert_level': message.alert_level.value,
                'timestamp': message.timestamp.isoformat(),
                'metadata': message.metadata
            }
            
            logger.info(f"[WEBHOOK] {json.dumps(webhook_data, indent=2)}")
            
            return True
        
        except Exception as e:
            logger.error(f"Failed to send webhook notification: {e}")
            return False
    
    async def get_alert_statistics(self, days: int = 7) -> Dict[str, Any]:
        """获取预警统计"""
        try:
            since = datetime.now(timezone.utc) - timedelta(days=days)
            
            # 获取最近的预警
            recent_alerts = self.whale_alert_repo.get_recent_alerts(
                hours=days * 24, 
                limit=1000
            )
            
            # 统计分析
            stats = {
                'total_alerts': len(recent_alerts),
                'by_level': {},
                'by_type': {},
                'by_day': {},
                'total_amount_usd': 0,
                'avg_amount_usd': 0,
                'processed_alerts': 0,
                'notification_sent': 0
            }
            
            for alert in recent_alerts:
                # 按级别统计
                level = alert.alert_level
                stats['by_level'][level] = stats['by_level'].get(level, 0) + 1
                
                # 按类型统计
                alert_type = alert.alert_type
                stats['by_type'][alert_type] = stats['by_type'].get(alert_type, 0) + 1
                
                # 按日期统计
                date_key = alert.created_at.strftime('%Y-%m-%d')
                stats['by_day'][date_key] = stats['by_day'].get(date_key, 0) + 1
                
                # 金额统计
                stats['total_amount_usd'] += alert.amount_usd
                
                # 处理状态统计
                if alert.is_processed:
                    stats['processed_alerts'] += 1
                if alert.notification_sent:
                    stats['notification_sent'] += 1
            
            # 计算平均值
            if stats['total_alerts'] > 0:
                stats['avg_amount_usd'] = stats['total_amount_usd'] / stats['total_alerts']
            
            return stats
        
        except Exception as e:
            logger.error(f"Failed to get alert statistics: {e}")
            return {}
