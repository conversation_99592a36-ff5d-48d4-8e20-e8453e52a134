"""
真实数据收集器 - 使用WebSocket和免费API获取实时数据
支持多个免费数据源，确保数据的真实性和实时性
"""
import asyncio
import json
import time
from datetime import datetime, timezone
from typing import Dict, List, Optional, Callable
import logging

# 尝试导入可选依赖
try:
    import websockets
    WEBSOCKETS_AVAILABLE = True
except ImportError:
    WEBSOCKETS_AVAILABLE = False
    print("⚠️ websockets库未安装，WebSocket功能将不可用")

try:
    import requests
    REQUESTS_AVAILABLE = True
except ImportError:
    REQUESTS_AVAILABLE = False
    print("❌ requests库未安装，API功能将不可用")

# 简单日志记录
class SimpleLogger:
    def info(self, msg): print(f"ℹ️ {msg}")
    def error(self, msg): print(f"❌ {msg}")
    def warning(self, msg): print(f"⚠️ {msg}")

logger = SimpleLogger()

class RealDataCollector:
    """真实数据收集器"""
    
    def __init__(self):
        self.websocket_connections = {}
        self.data_callbacks = {}
        self.is_running = False
        
        # 免费API端点
        self.api_endpoints = {
            'coingecko': 'https://api.coingecko.com/api/v3',
            'binance': 'https://api.binance.com/api/v3',
            'coinbase': 'https://api.exchange.coinbase.com',
            'kraken': 'https://api.kraken.com/0/public',
            'blockchain_info': 'https://blockchain.info',
            'blockchair': 'https://api.blockchair.com/bitcoin'
        }
        
        # WebSocket端点
        self.websocket_endpoints = {
            'binance': 'wss://stream.binance.com:9443/ws',
            'coinbase': 'wss://ws-feed.exchange.coinbase.com',
            'kraken': 'wss://ws.kraken.com',
            'blockchain_ws': 'wss://ws.blockchain.info/inv'
        }
    
    async def get_real_btc_price(self) -> Optional[Dict]:
        """获取真实BTC价格 - 多源验证"""
        prices = {}
        
        try:
            # CoinGecko API
            logger.info("获取CoinGecko价格数据...")
            cg_url = f"{self.api_endpoints['coingecko']}/simple/price"
            cg_params = {
                'ids': 'bitcoin',
                'vs_currencies': 'usd',
                'include_24hr_change': 'true',
                'include_24hr_vol': 'true',
                'include_market_cap': 'true',
                'include_last_updated_at': 'true'
            }
            
            response = requests.get(cg_url, params=cg_params, timeout=10)
            if response.status_code == 200:
                data = response.json()['bitcoin']
                prices['coingecko'] = {
                    'price': data['usd'],
                    'change_24h': data.get('usd_24h_change', 0),
                    'volume_24h': data.get('usd_24h_vol', 0),
                    'market_cap': data.get('usd_market_cap', 0),
                    'timestamp': data.get('last_updated_at', time.time())
                }
                logger.info(f"CoinGecko价格: ${data['usd']:,.2f}")
            
        except Exception as e:
            logger.error(f"CoinGecko API错误: {e}")
        
        try:
            # Binance API
            logger.info("获取Binance价格数据...")
            binance_url = f"{self.api_endpoints['binance']}/ticker/24hr"
            binance_params = {'symbol': 'BTCUSDT'}
            
            response = requests.get(binance_url, params=binance_params, timeout=10)
            if response.status_code == 200:
                data = response.json()
                prices['binance'] = {
                    'price': float(data['lastPrice']),
                    'change_24h': float(data['priceChangePercent']),
                    'volume_24h': float(data['volume']) * float(data['lastPrice']),
                    'high_24h': float(data['highPrice']),
                    'low_24h': float(data['lowPrice']),
                    'timestamp': time.time()
                }
                logger.info(f"Binance价格: ${float(data['lastPrice']):,.2f}")
            
        except Exception as e:
            logger.error(f"Binance API错误: {e}")
        
        try:
            # Coinbase API
            logger.info("获取Coinbase价格数据...")
            coinbase_url = f"{self.api_endpoints['coinbase']}/products/BTC-USD/ticker"
            
            response = requests.get(coinbase_url, timeout=10)
            if response.status_code == 200:
                data = response.json()
                prices['coinbase'] = {
                    'price': float(data['price']),
                    'volume_24h': float(data['volume']) * float(data['price']),
                    'timestamp': time.time()
                }
                logger.info(f"Coinbase价格: ${float(data['price']):,.2f}")
            
        except Exception as e:
            logger.error(f"Coinbase API错误: {e}")
        
        # 计算平均价格和验证
        if prices:
            price_values = [p['price'] for p in prices.values()]
            avg_price = sum(price_values) / len(price_values)
            
            # 检查价格差异（如果差异超过1%则警告）
            max_diff = max(abs(p - avg_price) / avg_price for p in price_values)
            if max_diff > 0.01:
                logger.warning(f"价格源之间差异较大: {max_diff:.2%}")
            
            # 返回综合数据
            primary_source = 'coingecko' if 'coingecko' in prices else list(prices.keys())[0]
            result = prices[primary_source].copy()
            result['avg_price'] = avg_price
            result['sources'] = list(prices.keys())
            result['price_sources'] = prices
            
            return result
        
        return None
    
    async def get_blockchain_metrics(self) -> Optional[Dict]:
        """获取区块链基础指标"""
        try:
            logger.info("获取区块链基础数据...")
            
            # Blockchain.info API
            stats_url = f"{self.api_endpoints['blockchain_info']}/stats"
            response = requests.get(stats_url, timeout=15)
            
            if response.status_code == 200:
                data = response.json()
                
                metrics = {
                    'total_bitcoins': data.get('totalbc', 0) / 1e8,  # 转换为BTC
                    'market_price_usd': data.get('market_price_usd', 0),
                    'hash_rate': data.get('hash_rate', 0),
                    'difficulty': data.get('difficulty', 0),
                    'minutes_between_blocks': data.get('minutes_between_blocks', 0),
                    'n_btc_mined': data.get('n_btc_mined', 0) / 1e8,
                    'n_tx': data.get('n_tx', 0),
                    'n_blocks_mined': data.get('n_blocks_mined', 0),
                    'total_fees_btc': data.get('total_fees_btc', 0) / 1e8,
                    'trade_volume_btc': data.get('trade_volume_btc', 0) / 1e8,
                    'trade_volume_usd': data.get('trade_volume_usd', 0),
                    'timestamp': time.time()
                }
                
                logger.info(f"区块链数据获取成功 - 哈希率: {metrics['hash_rate']:.2e}")
                return metrics
            
        except Exception as e:
            logger.error(f"获取区块链数据错误: {e}")
        
        return None
    
    async def get_mempool_data(self) -> Optional[Dict]:
        """获取内存池数据"""
        try:
            logger.info("获取内存池数据...")
            
            # Blockchair API
            mempool_url = f"{self.api_endpoints['blockchair']}/mempool/stats"
            response = requests.get(mempool_url, timeout=10)
            
            if response.status_code == 200:
                data = response.json()['data']
                
                mempool_info = {
                    'mempool_transactions': data.get('mempool_transactions', 0),
                    'mempool_size': data.get('mempool_size', 0),
                    'mempool_tps': data.get('mempool_tps', 0),
                    'average_transaction_fee_usd': data.get('average_transaction_fee_usd', 0),
                    'median_transaction_fee_usd': data.get('median_transaction_fee_usd', 0),
                    'timestamp': time.time()
                }
                
                logger.info(f"内存池交易数: {mempool_info['mempool_transactions']}")
                return mempool_info
            
        except Exception as e:
            logger.error(f"获取内存池数据错误: {e}")
        
        return None
    
    async def start_websocket_price_stream(self, callback: Callable):
        """启动WebSocket价格流"""
        try:
            logger.info("启动Binance WebSocket价格流...")
            
            # Binance WebSocket
            stream = "btcusdt@ticker"
            uri = f"{self.websocket_endpoints['binance']}/{stream}"
            
            async with websockets.connect(uri) as websocket:
                self.websocket_connections['binance_price'] = websocket
                logger.info("Binance WebSocket连接成功")
                
                while self.is_running:
                    try:
                        message = await asyncio.wait_for(websocket.recv(), timeout=30)
                        data = json.loads(message)
                        
                        # 解析Binance ticker数据
                        price_data = {
                            'symbol': data['s'],
                            'price': float(data['c']),
                            'change_24h': float(data['P']),
                            'volume_24h': float(data['v']),
                            'high_24h': float(data['h']),
                            'low_24h': float(data['l']),
                            'timestamp': int(data['E']) / 1000,
                            'source': 'binance_ws'
                        }
                        
                        await callback(price_data)
                        
                    except asyncio.TimeoutError:
                        logger.warning("WebSocket超时，发送ping...")
                        await websocket.ping()
                    except Exception as e:
                        logger.error(f"WebSocket数据处理错误: {e}")
                        break
                        
        except Exception as e:
            logger.error(f"WebSocket连接错误: {e}")
    
    async def start_blockchain_websocket(self, callback: Callable):
        """启动区块链WebSocket监听"""
        try:
            logger.info("启动Blockchain.info WebSocket...")
            
            uri = self.websocket_endpoints['blockchain_ws']
            
            async with websockets.connect(uri) as websocket:
                self.websocket_connections['blockchain'] = websocket
                
                # 订阅新交易
                subscribe_msg = {"op": "unconfirmed_sub"}
                await websocket.send(json.dumps(subscribe_msg))
                
                # 订阅新区块
                block_sub_msg = {"op": "blocks_sub"}
                await websocket.send(json.dumps(block_sub_msg))
                
                logger.info("Blockchain WebSocket订阅成功")
                
                while self.is_running:
                    try:
                        message = await asyncio.wait_for(websocket.recv(), timeout=60)
                        data = json.loads(message)
                        
                        if data.get('op') == 'utx':  # 新交易
                            tx_data = {
                                'type': 'transaction',
                                'hash': data['x']['hash'],
                                'size': data['x']['size'],
                                'fee': data['x']['fee'],
                                'inputs': len(data['x']['inputs']),
                                'outputs': len(data['x']['out']),
                                'timestamp': time.time()
                            }
                            await callback(tx_data)
                            
                        elif data.get('op') == 'block':  # 新区块
                            block_data = {
                                'type': 'block',
                                'hash': data['x']['hash'],
                                'height': data['x']['height'],
                                'time': data['x']['time'],
                                'n_tx': data['x']['nTx'],
                                'size': data['x']['size'],
                                'timestamp': time.time()
                            }
                            await callback(block_data)
                            
                    except asyncio.TimeoutError:
                        logger.warning("区块链WebSocket超时")
                        await websocket.ping()
                    except Exception as e:
                        logger.error(f"区块链WebSocket错误: {e}")
                        break
                        
        except Exception as e:
            logger.error(f"区块链WebSocket连接错误: {e}")
    
    async def get_exchange_data(self) -> Dict:
        """获取多个交易所的数据"""
        exchange_data = {}
        
        try:
            # Kraken API
            logger.info("获取Kraken数据...")
            kraken_url = f"{self.api_endpoints['kraken']}/Ticker"
            kraken_params = {'pair': 'XBTUSD'}
            
            response = requests.get(kraken_url, params=kraken_params, timeout=10)
            if response.status_code == 200:
                data = response.json()
                if 'result' in data and 'XXBTZUSD' in data['result']:
                    ticker = data['result']['XXBTZUSD']
                    exchange_data['kraken'] = {
                        'price': float(ticker['c'][0]),
                        'volume_24h': float(ticker['v'][1]),
                        'high_24h': float(ticker['h'][1]),
                        'low_24h': float(ticker['l'][1]),
                        'timestamp': time.time()
                    }
                    logger.info(f"Kraken价格: ${float(ticker['c'][0]):,.2f}")
            
        except Exception as e:
            logger.error(f"Kraken API错误: {e}")
        
        return exchange_data
    
    async def start_data_collection(self, price_callback: Callable, blockchain_callback: Callable):
        """启动完整的数据收集"""
        self.is_running = True
        logger.info("启动真实数据收集系统...")
        
        # 启动WebSocket连接
        tasks = [
            asyncio.create_task(self.start_websocket_price_stream(price_callback)),
            asyncio.create_task(self.start_blockchain_websocket(blockchain_callback))
        ]
        
        # 定期获取其他数据
        async def periodic_data_collection():
            while self.is_running:
                try:
                    # 每30秒获取一次完整数据
                    price_data = await self.get_real_btc_price()
                    if price_data:
                        await price_callback(price_data)
                    
                    # 每5分钟获取一次区块链指标
                    if int(time.time()) % 300 == 0:
                        blockchain_metrics = await self.get_blockchain_metrics()
                        if blockchain_metrics:
                            await blockchain_callback(blockchain_metrics)
                        
                        mempool_data = await self.get_mempool_data()
                        if mempool_data:
                            await blockchain_callback(mempool_data)
                    
                    await asyncio.sleep(30)
                    
                except Exception as e:
                    logger.error(f"定期数据收集错误: {e}")
                    await asyncio.sleep(60)
        
        tasks.append(asyncio.create_task(periodic_data_collection()))
        
        try:
            await asyncio.gather(*tasks)
        except Exception as e:
            logger.error(f"数据收集任务错误: {e}")
        finally:
            self.is_running = False
    
    async def stop_data_collection(self):
        """停止数据收集"""
        logger.info("停止数据收集...")
        self.is_running = False
        
        # 关闭WebSocket连接
        for name, ws in self.websocket_connections.items():
            try:
                await ws.close()
                logger.info(f"关闭{name} WebSocket连接")
            except Exception as e:
                logger.error(f"关闭{name} WebSocket错误: {e}")
        
        self.websocket_connections.clear()

# 测试函数
async def test_real_data_collector():
    """测试真实数据收集器"""
    collector = RealDataCollector()
    
    async def price_callback(data):
        print(f"💰 价格更新: ${data.get('price', 0):,.2f} ({data.get('source', 'unknown')})")
    
    async def blockchain_callback(data):
        if data.get('type') == 'transaction':
            print(f"📝 新交易: {data['hash'][:16]}... (费用: {data['fee']} sat)")
        elif data.get('type') == 'block':
            print(f"🧱 新区块: #{data['height']} ({data['n_tx']} 交易)")
        else:
            print(f"⛓️ 区块链数据: {data}")
    
    try:
        # 先测试API数据获取
        print("🔍 测试API数据获取...")
        price_data = await collector.get_real_btc_price()
        if price_data:
            print(f"✅ 价格数据: ${price_data['price']:,.2f}")
            print(f"📊 数据源: {price_data['sources']}")
        
        blockchain_data = await collector.get_blockchain_metrics()
        if blockchain_data:
            print(f"✅ 区块链数据: 哈希率 {blockchain_data['hash_rate']:.2e}")
        
        # 启动实时数据流（运行10秒测试）
        print("\n🚀 启动实时数据流测试...")
        collection_task = asyncio.create_task(
            collector.start_data_collection(price_callback, blockchain_callback)
        )
        
        await asyncio.sleep(10)  # 运行10秒
        await collector.stop_data_collection()
        
    except Exception as e:
        print(f"❌ 测试错误: {e}")

if __name__ == "__main__":
    asyncio.run(test_real_data_collector())
