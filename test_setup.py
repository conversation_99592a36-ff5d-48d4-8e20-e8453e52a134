"""
测试脚本 - 验证项目基础设施
在没有外部依赖的情况下测试项目结构和基础功能
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_project_structure():
    """测试项目结构"""
    print("Testing project structure...")
    
    required_dirs = [
        'config',
        'src',
        'src/data',
        'src/database',
        'src/analysis',
        'src/monitoring',
        'src/backtesting',
        'src/risk_management',
        'src/utils',
        'tests',
        'data',
        'logs',
        'notebooks',
        'web'
    ]
    
    missing_dirs = []
    for dir_path in required_dirs:
        if not Path(dir_path).exists():
            missing_dirs.append(dir_path)
    
    if missing_dirs:
        print(f"❌ Missing directories: {missing_dirs}")
        return False
    else:
        print("✅ All required directories exist")
        return True


def test_config_files():
    """测试配置文件"""
    print("Testing configuration files...")
    
    required_files = [
        'config/__init__.py',
        'config/settings.py',
        'config/api_keys.py.template',
        '.env.template',
        'requirements.txt'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ Missing files: {missing_files}")
        return False
    else:
        print("✅ All required configuration files exist")
        return True


def test_imports():
    """测试模块导入"""
    print("Testing module imports...")
    
    try:
        # 测试配置导入
        from config.settings import DATABASE_CONFIG, API_CONFIG
        print("✅ Configuration import successful")
        
        # 测试工具模块导入
        from src.utils.logger import get_logger
        from src.utils.helpers import is_valid_address, wei_to_ether
        print("✅ Utils modules import successful")
        
        # 测试数据库模型导入
        from src.database.models import Address, Transaction, Token
        print("✅ Database models import successful")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error during import: {e}")
        return False


def test_logger():
    """测试日志功能"""
    print("Testing logger functionality...")
    
    try:
        from src.utils.logger import get_logger
        
        logger = get_logger("test")
        logger.info("Test log message")
        print("✅ Logger functionality working")
        return True
        
    except Exception as e:
        print(f"❌ Logger test failed: {e}")
        return False


def test_helper_functions():
    """测试辅助函数"""
    print("Testing helper functions...")
    
    try:
        from src.utils.helpers import (
            is_valid_address, wei_to_ether, ether_to_wei,
            format_large_number, calculate_percentage_change
        )
        
        # 测试地址验证
        assert is_valid_address("******************************************") == True
        assert is_valid_address("invalid_address") == False
        
        # 测试单位转换
        assert wei_to_ether(1000000000000000000) == 1.0
        assert ether_to_wei(1.0) == 1000000000000000000
        
        # 测试数字格式化
        assert format_large_number(1500000) == "1.50M"
        assert format_large_number(2500000000) == "2.50B"
        
        # 测试百分比计算
        assert calculate_percentage_change(100, 150) == 50.0
        
        print("✅ Helper functions working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Helper functions test failed: {e}")
        return False


def test_database_models():
    """测试数据库模型"""
    print("Testing database models...")
    
    try:
        from src.database.models import Address, Transaction, Token, Base
        
        # 检查模型是否正确定义
        assert hasattr(Address, '__tablename__')
        assert hasattr(Transaction, '__tablename__')
        assert hasattr(Token, '__tablename__')
        
        # 检查基础字段
        assert hasattr(Address, 'address')
        assert hasattr(Transaction, 'tx_hash')
        assert hasattr(Token, 'symbol')
        
        print("✅ Database models defined correctly")
        return True
        
    except Exception as e:
        print(f"❌ Database models test failed: {e}")
        return False


def test_configuration():
    """测试配置"""
    print("Testing configuration...")
    
    try:
        from config.settings import (
            DATABASE_CONFIG, API_CONFIG, BLOCKCHAIN_CONFIG,
            MONITORING_CONFIG, TRADING_CONFIG
        )
        
        # 检查配置结构
        assert 'host' in DATABASE_CONFIG
        assert 'etherscan' in API_CONFIG
        assert 'ethereum' in BLOCKCHAIN_CONFIG
        assert 'whale_threshold' in MONITORING_CONFIG
        assert 'risk_management' in TRADING_CONFIG
        
        print("✅ Configuration structure is correct")
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 Starting project setup tests...\n")
    
    tests = [
        ("Project Structure", test_project_structure),
        ("Configuration Files", test_config_files),
        ("Module Imports", test_imports),
        ("Logger", test_logger),
        ("Helper Functions", test_helper_functions),
        ("Database Models", test_database_models),
        ("Configuration", test_configuration),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            if test_func():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            failed += 1
    
    print(f"\n{'='*50}")
    print(f"Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All tests passed! Project setup is correct.")
        return True
    else:
        print("❌ Some tests failed. Please check the issues above.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
