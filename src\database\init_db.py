"""
数据库初始化脚本
创建数据表和初始数据
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.database.database import db_manager
from src.database.models import Address, Token
from src.utils.logger import get_logger

logger = get_logger(__name__)


def create_tables():
    """创建所有数据表"""
    try:
        logger.info("Creating database tables...")
        db_manager.create_tables()
        logger.info("Database tables created successfully")
    except Exception as e:
        logger.error(f"Failed to create tables: {e}")
        raise


def insert_initial_data():
    """插入初始数据"""
    try:
        logger.info("Inserting initial data...")
        
        # 插入一些知名地址
        known_addresses = [
            {
                'address': '******************************************',
                'label': '<PERSON><PERSON> Buterin',
                'address_type': 'wallet',
                'is_smart_money': True,
                'tags': ['founder', 'ethereum', 'vitalik']
            },
            {
                'address': '******************************************',
                'label': 'Binance Hot Wallet',
                'address_type': 'exchange',
                'is_whale': True,
                'tags': ['exchange', 'binance', 'hot_wallet']
            },
            {
                'address': '******************************************',
                'label': 'Binance Hot Wallet 2',
                'address_type': 'exchange',
                'is_whale': True,
                'tags': ['exchange', 'binance', 'hot_wallet']
            },
            {
                'address': '******************************************',
                'label': 'FTX Hot Wallet',
                'address_type': 'exchange',
                'is_whale': True,
                'tags': ['exchange', 'ftx', 'hot_wallet']
            },
            {
                'address': '******************************************',
                'label': 'Crypto.com Hot Wallet',
                'address_type': 'exchange',
                'is_whale': True,
                'tags': ['exchange', 'crypto.com', 'hot_wallet']
            }
        ]
        
        with db_manager.get_session() as session:
            for addr_data in known_addresses:
                # 检查地址是否已存在
                existing = session.query(Address).filter(
                    Address.address == addr_data['address']
                ).first()
                
                if not existing:
                    address = Address(**addr_data)
                    session.add(address)
                    logger.info(f"Added address: {addr_data['label']}")
        
        # 插入一些主要代币
        major_tokens = [
            {
                'contract_address': '******************************************',  # ETH (虚拟地址)
                'symbol': 'ETH',
                'name': 'Ethereum',
                'decimals': 18,
                'is_verified': True,
                'tags': ['native', 'layer1']
            },
            {
                'contract_address': '******************************************',
                'symbol': 'USDT',
                'name': 'Tether USD',
                'decimals': 6,
                'is_verified': True,
                'tags': ['stablecoin', 'tether']
            },
            {
                'contract_address': '******************************************',
                'symbol': 'USDC',
                'name': 'USD Coin',
                'decimals': 6,
                'is_verified': True,
                'tags': ['stablecoin', 'centre']
            },
            {
                'contract_address': '******************************************',
                'symbol': 'WBTC',
                'name': 'Wrapped Bitcoin',
                'decimals': 8,
                'is_verified': True,
                'tags': ['wrapped', 'bitcoin']
            },
            {
                'contract_address': '******************************************',
                'symbol': 'UNI',
                'name': 'Uniswap',
                'decimals': 18,
                'is_verified': True,
                'tags': ['defi', 'dex', 'governance']
            }
        ]
        
        with db_manager.get_session() as session:
            for token_data in major_tokens:
                # 检查代币是否已存在
                existing = session.query(Token).filter(
                    Token.contract_address == token_data['contract_address']
                ).first()
                
                if not existing:
                    token = Token(**token_data)
                    session.add(token)
                    logger.info(f"Added token: {token_data['symbol']}")
        
        logger.info("Initial data inserted successfully")
        
    except Exception as e:
        logger.error(f"Failed to insert initial data: {e}")
        raise


def check_database_connection():
    """检查数据库连接"""
    try:
        logger.info("Checking database connection...")
        health = db_manager.health_check()
        
        if health['database']:
            logger.info("Database connection: OK")
        else:
            logger.error("Database connection: FAILED")
            return False
        
        if health['redis']:
            logger.info("Redis connection: OK")
        else:
            logger.warning("Redis connection: FAILED (optional)")
        
        return True
        
    except Exception as e:
        logger.error(f"Database connection check failed: {e}")
        return False


def main():
    """主函数"""
    try:
        logger.info("Starting database initialization...")
        
        # 检查数据库连接
        if not check_database_connection():
            logger.error("Database connection failed, aborting initialization")
            sys.exit(1)
        
        # 创建数据表
        create_tables()
        
        # 插入初始数据
        insert_initial_data()
        
        logger.info("Database initialization completed successfully!")
        
    except Exception as e:
        logger.error(f"Database initialization failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
