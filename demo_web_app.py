"""
Web应用演示脚本
启动和演示链上数据分析平台的Web界面
"""
import asyncio
import sys
import time
import webbrowser
from pathlib import Path
import subprocess
import requests
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.utils.logger import get_logger

logger = get_logger(__name__)


def check_dependencies():
    """检查依赖项"""
    print("🔍 检查依赖项...")
    
    required_packages = [
        'fastapi',
        'uvicorn',
        'websockets',
        'jinja2',
        'python-multipart'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"  ✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"  ❌ {package}")
    
    if missing_packages:
        print(f"\n⚠️ 缺少依赖项: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ 所有依赖项已安装")
    return True


def start_web_server():
    """启动Web服务器"""
    print("\n🚀 启动Web服务器...")
    
    try:
        # 启动uvicorn服务器
        cmd = [
            sys.executable, "-m", "uvicorn",
            "src.web.app:app",
            "--host", "0.0.0.0",
            "--port", "8000",
            "--reload"
        ]
        
        print(f"执行命令: {' '.join(cmd)}")
        
        # 在后台启动服务器
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            cwd=project_root
        )
        
        return process
    
    except Exception as e:
        print(f"❌ 启动Web服务器失败: {e}")
        return None


def wait_for_server(url="http://localhost:8000", timeout=30):
    """等待服务器启动"""
    print(f"⏳ 等待服务器启动 ({url})...")
    
    start_time = time.time()
    
    while time.time() - start_time < timeout:
        try:
            response = requests.get(f"{url}/health", timeout=5)
            if response.status_code == 200:
                print("✅ 服务器已启动")
                return True
        except requests.exceptions.RequestException:
            pass
        
        time.sleep(1)
        print(".", end="", flush=True)
    
    print(f"\n❌ 服务器启动超时 ({timeout}秒)")
    return False


def test_api_endpoints():
    """测试API端点"""
    print("\n🧪 测试API端点...")
    
    base_url = "http://localhost:8000"
    
    endpoints = [
        ("/health", "健康检查"),
        ("/api/status", "系统状态"),
        ("/api/dashboard/overview", "仪表板概览"),
        ("/api/dashboard/market-metrics", "市场指标"),
        ("/api/analysis/smart-money/BTC", "Smart Money分析"),
        ("/api/trading/strategies", "交易策略"),
        ("/api/monitoring/whale-activity", "巨鲸活动"),
        ("/api/settings/user-preferences", "用户设置")
    ]
    
    results = []
    
    for endpoint, description in endpoints:
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=10)
            
            if response.status_code == 200:
                print(f"  ✅ {description}: {endpoint}")
                results.append((endpoint, True, response.status_code))
            else:
                print(f"  ⚠️ {description}: {endpoint} (状态码: {response.status_code})")
                results.append((endpoint, False, response.status_code))
        
        except requests.exceptions.RequestException as e:
            print(f"  ❌ {description}: {endpoint} (错误: {e})")
            results.append((endpoint, False, str(e)))
    
    # 统计结果
    successful = len([r for r in results if r[1]])
    total = len(results)
    
    print(f"\n📊 API测试结果: {successful}/{total} 个端点正常")
    
    return results


def demonstrate_features():
    """演示功能特性"""
    print("\n🎯 功能特性演示")
    print("=" * 50)
    
    features = [
        {
            'name': '实时仪表板',
            'url': 'http://localhost:8000/dashboard',
            'description': '显示市场概览、价格走势和实时事件'
        },
        {
            'name': 'Smart Money分析',
            'url': 'http://localhost:8000/api/analysis/smart-money/BTC',
            'description': '分析聪明资金的交易行为和信号'
        },
        {
            'name': '巨鲸监控',
            'url': 'http://localhost:8000/api/monitoring/whale-activity',
            'description': '监控大额交易和巨鲸活动'
        },
        {
            'name': '技术分析',
            'url': 'http://localhost:8000/api/analysis/technical/BTC',
            'description': '提供技术指标和交易信号'
        },
        {
            'name': '风险评估',
            'url': 'http://localhost:8000/api/trading/risk-assessment/BTC',
            'description': '评估资产和投资组合风险'
        },
        {
            'name': '回测系统',
            'url': 'http://localhost:8000/api/trading/strategies',
            'description': '策略回测和性能分析'
        }
    ]
    
    for i, feature in enumerate(features, 1):
        print(f"{i}. {feature['name']}")
        print(f"   描述: {feature['description']}")
        print(f"   URL: {feature['url']}")
        print()
    
    return features


def open_browser_demo():
    """打开浏览器演示"""
    print("\n🌐 打开浏览器演示...")
    
    urls = [
        ('主页', 'http://localhost:8000/'),
        ('仪表板', 'http://localhost:8000/dashboard'),
        ('API文档', 'http://localhost:8000/docs')
    ]
    
    for name, url in urls:
        print(f"  📱 {name}: {url}")
    
    try:
        # 打开主页
        webbrowser.open('http://localhost:8000/')
        print("✅ 浏览器已打开")
        return True
    
    except Exception as e:
        print(f"❌ 打开浏览器失败: {e}")
        print("请手动访问: http://localhost:8000/")
        return False


def show_websocket_demo():
    """展示WebSocket演示"""
    print("\n🔌 WebSocket实时数据演示")
    print("=" * 30)
    
    websocket_info = """
WebSocket连接地址: ws://localhost:8000/ws/realtime

支持的消息类型:
- subscribe: 订阅数据频道
- unsubscribe: 取消订阅
- ping: 心跳检测

支持的数据频道:
- prices: 价格更新
- whale_alerts: 巨鲸预警
- smart_money: Smart Money信号
- technical_signals: 技术信号
- risk_alerts: 风险预警

示例JavaScript代码:
```javascript
const ws = new WebSocket('ws://localhost:8000/ws/realtime');

ws.onopen = function(event) {
    console.log('WebSocket连接已建立');
    
    // 订阅价格数据
    ws.send(JSON.stringify({
        type: 'subscribe',
        channels: ['prices', 'whale_alerts']
    }));
};

ws.onmessage = function(event) {
    const data = JSON.parse(event.data);
    console.log('收到数据:', data);
};
```
"""
    
    print(websocket_info)


def show_api_examples():
    """显示API使用示例"""
    print("\n📚 API使用示例")
    print("=" * 30)
    
    examples = [
        {
            'title': '获取市场概览',
            'method': 'GET',
            'url': '/api/dashboard/overview',
            'description': '获取主要加密货币价格和市场统计'
        },
        {
            'title': 'Smart Money分析',
            'method': 'GET',
            'url': '/api/analysis/smart-money/BTC?days=7',
            'description': '获取BTC的Smart Money分析数据'
        },
        {
            'title': '运行回测',
            'method': 'POST',
            'url': '/api/trading/backtest',
            'description': '运行交易策略回测',
            'body': {
                'strategy': {'name': 'ma_crossover', 'params': {'fast_period': 10, 'slow_period': 20}},
                'symbols': ['BTC'],
                'start_date': '2023-01-01T00:00:00Z',
                'end_date': '2023-12-31T23:59:59Z',
                'initial_capital': 100000
            }
        },
        {
            'title': '获取巨鲸活动',
            'method': 'GET',
            'url': '/api/monitoring/whale-activity?hours=24&min_amount=1000000',
            'description': '获取24小时内的巨鲸活动'
        }
    ]
    
    for example in examples:
        print(f"📋 {example['title']}")
        print(f"   方法: {example['method']}")
        print(f"   URL: {example['url']}")
        print(f"   描述: {example['description']}")
        
        if 'body' in example:
            print(f"   请求体: {example['body']}")
        
        print()


def main():
    """主函数"""
    print("🚀 OnChain Analytics Platform Web应用演示")
    print("=" * 60)
    print(f"启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. 检查依赖项
    if not check_dependencies():
        return
    
    # 2. 启动Web服务器
    server_process = start_web_server()
    
    if not server_process:
        print("❌ 无法启动Web服务器")
        return
    
    try:
        # 3. 等待服务器启动
        if not wait_for_server():
            print("❌ 服务器启动失败")
            return
        
        # 4. 测试API端点
        test_results = test_api_endpoints()
        
        # 5. 演示功能特性
        features = demonstrate_features()
        
        # 6. 显示API示例
        show_api_examples()
        
        # 7. WebSocket演示
        show_websocket_demo()
        
        # 8. 打开浏览器
        open_browser_demo()
        
        print("\n🎉 Web应用演示启动完成!")
        print("=" * 60)
        print("\n💡 使用说明:")
        print("- 访问 http://localhost:8000/ 查看主页")
        print("- 访问 http://localhost:8000/dashboard 查看仪表板")
        print("- 访问 http://localhost:8000/docs 查看API文档")
        print("- 按 Ctrl+C 停止服务器")
        
        # 保持服务器运行
        print("\n⏳ 服务器正在运行，按 Ctrl+C 停止...")
        
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n👋 正在停止服务器...")
    
    finally:
        # 清理服务器进程
        if server_process:
            server_process.terminate()
            try:
                server_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                server_process.kill()
            
            print("✅ 服务器已停止")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 演示被用户中断")
    except Exception as e:
        print(f"\n💥 演示过程中发生错误: {e}")
        logger.error("Web app demo failed", exc_info=True)
