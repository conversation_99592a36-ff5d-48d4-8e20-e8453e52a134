"""
区块链API接口模块
提供与各种区块链浏览器和节点的交互功能
"""
import asyncio
import time
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timezone
import aiohttp
import requests
from tenacity import retry, stop_after_attempt, wait_exponential

from config.settings import API_CONFIG, BLOCKCHAIN_CONFIG
from src.utils.logger import get_logger, log_api_call
from src.utils.helpers import retry_on_failure, rate_limit, is_valid_address

logger = get_logger(__name__)


class BaseBlockchainAPI:
    """区块链API基类"""
    
    def __init__(self, api_key: str = None, base_url: str = None, rate_limit_per_second: float = 5):
        self.api_key = api_key
        self.base_url = base_url
        self.rate_limit_per_second = rate_limit_per_second
        self.session = None
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    @retry_on_failure(max_attempts=3, delay=1.0)
    async def _make_request(self, endpoint: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """发送API请求"""
        if not self.session:
            raise RuntimeError("Session not initialized. Use async context manager.")
        
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        
        # 添加API密钥到参数
        if params is None:
            params = {}
        if self.api_key:
            params['apikey'] = self.api_key
        
        try:
            # 应用速率限制
            await asyncio.sleep(1.0 / self.rate_limit_per_second)
            
            async with self.session.get(url, params=params) as response:
                response.raise_for_status()
                data = await response.json()
                
                # 检查API响应状态
                if isinstance(data, dict) and data.get('status') == '0':
                    error_msg = data.get('message', 'Unknown API error')
                    logger.error(f"API error: {error_msg}")
                    raise Exception(f"API error: {error_msg}")
                
                return data
                
        except aiohttp.ClientError as e:
            logger.error(f"HTTP error for {url}: {e}")
            raise
        except Exception as e:
            logger.error(f"Request error for {url}: {e}")
            raise


class EtherscanAPI(BaseBlockchainAPI):
    """Etherscan API客户端"""
    
    def __init__(self):
        config = API_CONFIG['etherscan']
        super().__init__(
            api_key=config['api_key'],
            base_url=config['base_url'],
            rate_limit_per_second=config['rate_limit']
        )
    
    @log_api_call("Etherscan", "get_balance")
    async def get_balance(self, address: str) -> Dict[str, Any]:
        """获取地址余额"""
        if not is_valid_address(address):
            raise ValueError(f"Invalid address: {address}")
        
        params = {
            'module': 'account',
            'action': 'balance',
            'address': address,
            'tag': 'latest'
        }
        
        response = await self._make_request('api', params)
        balance_wei = int(response['result'])
        balance_eth = balance_wei / 10**18
        
        return {
            'address': address,
            'balance_wei': balance_wei,
            'balance_eth': balance_eth,
            'timestamp': datetime.now(timezone.utc)
        }
    
    @log_api_call("Etherscan", "get_transactions")
    async def get_transactions(self, address: str, start_block: int = 0, 
                             end_block: int = ********, page: int = 1, 
                             offset: int = 100) -> List[Dict[str, Any]]:
        """获取地址交易历史"""
        if not is_valid_address(address):
            raise ValueError(f"Invalid address: {address}")
        
        params = {
            'module': 'account',
            'action': 'txlist',
            'address': address,
            'startblock': start_block,
            'endblock': end_block,
            'page': page,
            'offset': offset,
            'sort': 'desc'
        }
        
        response = await self._make_request('api', params)
        transactions = response.get('result', [])
        
        # 处理交易数据
        processed_txs = []
        for tx in transactions:
            processed_tx = {
                'hash': tx['hash'],
                'block_number': int(tx['blockNumber']),
                'timestamp': datetime.fromtimestamp(int(tx['timeStamp']), timezone.utc),
                'from_address': tx['from'].lower(),
                'to_address': tx['to'].lower() if tx['to'] else None,
                'value_wei': int(tx['value']),
                'value_eth': int(tx['value']) / 10**18,
                'gas_used': int(tx['gasUsed']),
                'gas_price': int(tx['gasPrice']),
                'gas_fee_eth': (int(tx['gasUsed']) * int(tx['gasPrice'])) / 10**18,
                'is_error': tx['isError'] == '1',
                'contract_address': tx.get('contractAddress'),
                'input_data': tx.get('input', '')
            }
            processed_txs.append(processed_tx)
        
        return processed_txs

    @log_api_call("Etherscan", "get_token_transfers")
    async def get_token_transfers(self, address: str, contract_address: str = None,
                                start_block: int = 0, end_block: int = ********,
                                page: int = 1, offset: int = 100) -> List[Dict[str, Any]]:
        """获取代币转账记录"""
        if not is_valid_address(address):
            raise ValueError(f"Invalid address: {address}")

        params = {
            'module': 'account',
            'action': 'tokentx',
            'address': address,
            'startblock': start_block,
            'endblock': end_block,
            'page': page,
            'offset': offset,
            'sort': 'desc'
        }

        if contract_address and is_valid_address(contract_address):
            params['contractaddress'] = contract_address

        response = await self._make_request('api', params)
        transfers = response.get('result', [])

        # 处理代币转账数据
        processed_transfers = []
        for transfer in transfers:
            processed_transfer = {
                'hash': transfer['hash'],
                'block_number': int(transfer['blockNumber']),
                'timestamp': datetime.fromtimestamp(int(transfer['timeStamp']), timezone.utc),
                'from_address': transfer['from'].lower(),
                'to_address': transfer['to'].lower(),
                'contract_address': transfer['contractAddress'].lower(),
                'token_name': transfer['tokenName'],
                'token_symbol': transfer['tokenSymbol'],
                'token_decimal': int(transfer['tokenDecimal']),
                'value_raw': int(transfer['value']),
                'value_formatted': int(transfer['value']) / (10 ** int(transfer['tokenDecimal'])),
                'gas_used': int(transfer['gasUsed']),
                'gas_price': int(transfer['gasPrice'])
            }
            processed_transfers.append(processed_transfer)

        return processed_transfers

    @log_api_call("Etherscan", "get_contract_abi")
    async def get_contract_abi(self, contract_address: str) -> Dict[str, Any]:
        """获取合约ABI"""
        if not is_valid_address(contract_address):
            raise ValueError(f"Invalid contract address: {contract_address}")

        params = {
            'module': 'contract',
            'action': 'getabi',
            'address': contract_address
        }

        response = await self._make_request('api', params)
        return {
            'contract_address': contract_address,
            'abi': response['result'],
            'timestamp': datetime.now(timezone.utc)
        }

    @log_api_call("Etherscan", "get_block_info")
    async def get_block_info(self, block_number: Union[int, str] = 'latest') -> Dict[str, Any]:
        """获取区块信息"""
        params = {
            'module': 'proxy',
            'action': 'eth_getBlockByNumber',
            'tag': str(block_number) if isinstance(block_number, int) else block_number,
            'boolean': 'true'
        }

        response = await self._make_request('api', params)
        block_data = response['result']

        return {
            'block_number': int(block_data['number'], 16),
            'timestamp': datetime.fromtimestamp(int(block_data['timestamp'], 16), timezone.utc),
            'hash': block_data['hash'],
            'parent_hash': block_data['parentHash'],
            'gas_limit': int(block_data['gasLimit'], 16),
            'gas_used': int(block_data['gasUsed'], 16),
            'transaction_count': len(block_data.get('transactions', [])),
            'miner': block_data['miner'].lower(),
            'difficulty': int(block_data['difficulty'], 16),
            'total_difficulty': int(block_data['totalDifficulty'], 16)
        }


class MoralisAPI(BaseBlockchainAPI):
    """Moralis API客户端"""

    def __init__(self):
        config = API_CONFIG['moralis']
        super().__init__(
            api_key=config['api_key'],
            base_url=config['base_url'],
            rate_limit_per_second=config['rate_limit']
        )

    async def _make_request(self, endpoint: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """重写请求方法以适配Moralis API"""
        if not self.session:
            raise RuntimeError("Session not initialized. Use async context manager.")

        url = f"{self.base_url}/{endpoint.lstrip('/')}"

        headers = {
            'X-API-Key': self.api_key,
            'Content-Type': 'application/json'
        }

        try:
            # 应用速率限制
            await asyncio.sleep(1.0 / self.rate_limit_per_second)

            async with self.session.get(url, params=params, headers=headers) as response:
                response.raise_for_status()
                return await response.json()

        except aiohttp.ClientError as e:
            logger.error(f"HTTP error for {url}: {e}")
            raise
        except Exception as e:
            logger.error(f"Request error for {url}: {e}")
            raise

    @log_api_call("Moralis", "get_native_balance")
    async def get_native_balance(self, address: str, chain: str = 'eth') -> Dict[str, Any]:
        """获取原生代币余额"""
        if not is_valid_address(address):
            raise ValueError(f"Invalid address: {address}")

        endpoint = f"{address}/balance"
        params = {'chain': chain}

        response = await self._make_request(endpoint, params)

        return {
            'address': address,
            'chain': chain,
            'balance_wei': int(response['balance']),
            'balance_eth': int(response['balance']) / 10**18,
            'timestamp': datetime.now(timezone.utc)
        }
