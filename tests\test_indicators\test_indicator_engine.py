"""
技术指标计算引擎测试
"""
import pytest
import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timezone, timedelta
from unittest.mock import Mock, patch, MagicMock

from src.indicators.onchain_indicators import OnChainIndicatorCalculator, OnChainMetrics
from src.indicators.technical_indicators import TechnicalIndicatorCalculator, TechnicalIndicators
from src.indicators.indicator_engine import IndicatorEngine, CompositeSignal


class TestOnChainIndicatorCalculator:
    """链上指标计算器测试"""
    
    @pytest.mark.asyncio
    async def test_calculator_initialization(self):
        """测试计算器初始化"""
        with patch('src.indicators.onchain_indicators.DataService'):
            async with OnChainIndicatorCalculator() as calculator:
                assert calculator.transaction_repo is not None
                assert calculator.market_data_repo is not None
                assert calculator.onchain_metrics_repo is not None
    
    @pytest.mark.asyncio
    async def test_calculate_active_addresses(self):
        """测试活跃地址计算"""
        calculator = OnChainIndicatorCalculator()
        
        mock_transactions = [
            Mock(from_address='0x123...', to_address='0x456...'),
            Mock(from_address='0x456...', to_address='0x789...'),
            Mock(from_address='0x123...', to_address='0xabc...')
        ]
        
        active_count = await calculator._calculate_active_addresses(mock_transactions)
        
        # 应该有4个唯一地址
        assert active_count == 4
    
    @pytest.mark.asyncio
    async def test_calculate_mvrv_ratio(self):
        """测试MVRV比率计算"""
        calculator = OnChainIndicatorCalculator()
        
        # 模拟价格历史
        mock_prices = [
            Mock(price_usd=100),
            Mock(price_usd=110),
            Mock(price_usd=120)
        ]
        
        with patch.object(calculator.market_data_repo, 'get_price_history', return_value=mock_prices):
            mvrv = await calculator._calculate_mvrv_ratio('BTC', 130, datetime.now().date())
            
            # MVRV应该大于1（当前价格高于平均价格）
            assert mvrv > 1.0
    
    @pytest.mark.asyncio
    async def test_calculate_nvt_ratio(self):
        """测试NVT比率计算"""
        calculator = OnChainIndicatorCalculator()
        
        nvt = await calculator._calculate_nvt_ratio(50000, 1000000000, 10000000)
        
        # NVT = 市值 / 交易量
        expected_nvt = 1000000000 / 10000000
        assert nvt == expected_nvt
    
    def test_calculate_holder_distribution(self):
        """测试持有者分布计算"""
        calculator = OnChainIndicatorCalculator()
        
        mock_transactions = [Mock() for _ in range(100)]
        
        distribution = asyncio.run(calculator._calculate_holder_distribution(mock_transactions))
        
        assert 'whales' in distribution
        assert 'large_holders' in distribution
        assert 'medium_holders' in distribution
        assert 'small_holders' in distribution
        
        # 分布总和应该为1
        total = sum(distribution.values())
        assert abs(total - 1.0) < 0.01


class TestTechnicalIndicatorCalculator:
    """技术指标计算器测试"""
    
    def test_calculator_initialization(self):
        """测试计算器初始化"""
        calculator = TechnicalIndicatorCalculator()
        assert calculator.market_data_repo is not None
    
    def test_calculate_rsi(self):
        """测试RSI计算"""
        calculator = TechnicalIndicatorCalculator()
        
        # 创建测试价格序列
        prices = pd.Series([100, 102, 101, 103, 105, 104, 106, 108, 107, 109])
        
        rsi = calculator._calculate_rsi(prices, period=5)
        
        # RSI应该在0-100之间
        assert all(0 <= val <= 100 for val in rsi.dropna())
    
    def test_calculate_moving_averages(self):
        """测试移动平均线计算"""
        calculator = TechnicalIndicatorCalculator()
        
        # 创建测试数据
        df = pd.DataFrame({
            'price': [100, 102, 101, 103, 105, 104, 106, 108, 107, 109] * 10
        })
        
        df = calculator._calculate_moving_averages(df)
        
        assert 'sma_20' in df.columns
        assert 'sma_50' in df.columns
        assert 'ema_12' in df.columns
        assert 'ema_26' in df.columns
        
        # 移动平均线应该平滑价格
        assert df['sma_20'].iloc[-1] is not None
    
    def test_generate_signals(self):
        """测试信号生成"""
        calculator = TechnicalIndicatorCalculator()
        
        # 创建测试数据
        df = pd.DataFrame({
            'price': [100] * 50,
            'sma_20': [98] * 50,
            'sma_50': [96] * 50,
            'sma_200': [94] * 50,
            'rsi_14': [60] * 50,
            'macd': [1] * 50,
            'macd_signal': [0.5] * 50,
            'macd_histogram': [0.5] * 50,
            'high': [102] * 50,
            'low': [98] * 50,
            'close': [100] * 50,
            'volume': [1000] * 50
        })
        
        df = calculator._generate_signals(df)
        
        assert 'trend_signal' in df.columns
        assert 'momentum_signal' in df.columns
        assert 'overall_signal' in df.columns
        
        # 信号应该是有效值
        valid_signals = ['bullish', 'bearish', 'neutral']
        assert all(signal in valid_signals for signal in df['trend_signal'].dropna())
    
    @pytest.mark.asyncio
    async def test_get_current_signals(self):
        """测试获取当前信号"""
        calculator = TechnicalIndicatorCalculator()
        
        # 模拟指标数据
        mock_indicator = TechnicalIndicators(
            symbol='BTC',
            timestamp=datetime.now(timezone.utc),
            price=50000,
            sma_20=49000,
            sma_50=48000,
            sma_200=47000,
            ema_12=49500,
            ema_26=48500,
            rsi_14=65,
            macd=100,
            macd_signal=80,
            macd_histogram=20,
            bollinger_upper=52000,
            bollinger_middle=50000,
            bollinger_lower=48000,
            atr_14=1000,
            volume_sma_20=1000000,
            volume_ratio=1.2,
            support_level=48500,
            resistance_level=51500,
            trend_signal='bullish',
            momentum_signal='bullish',
            overall_signal='strong_bullish'
        )
        
        with patch.object(calculator, 'calculate_all_indicators', return_value=[mock_indicator]):
            signals = await calculator.get_current_signals('BTC')
            
            assert signals['symbol'] == 'BTC'
            assert signals['trend_signal'] == 'bullish'
            assert signals['overall_signal'] == 'strong_bullish'
            assert 'rsi' in signals
            assert 'support_level' in signals


class TestIndicatorEngine:
    """指标分析引擎测试"""
    
    @pytest.mark.asyncio
    async def test_engine_initialization(self):
        """测试引擎初始化"""
        with patch('src.indicators.indicator_engine.OnChainIndicatorCalculator'):
            async with IndicatorEngine() as engine:
                assert engine.onchain_calculator is not None
                assert engine.technical_calculator is not None
                assert engine.signal_repo is not None
    
    def test_analyze_technical_indicators(self):
        """测试技术指标分析"""
        engine = IndicatorEngine()
        
        # 创建模拟技术指标
        mock_indicators = [
            TechnicalIndicators(
                symbol='BTC',
                timestamp=datetime.now(timezone.utc),
                price=50000,
                sma_20=49000, sma_50=48000, sma_200=47000,
                ema_12=49500, ema_26=48500,
                rsi_14=65,
                macd=100, macd_signal=80, macd_histogram=20,
                bollinger_upper=52000, bollinger_middle=50000, bollinger_lower=48000,
                atr_14=1000,
                volume_sma_20=1000000, volume_ratio=1.2,
                support_level=48500, resistance_level=51500,
                trend_signal='bullish',
                momentum_signal='bullish',
                overall_signal='strong_bullish'
            )
        ]
        
        analysis = engine._analyze_technical_indicators(mock_indicators)
        
        assert 'signal' in analysis
        assert 'strength' in analysis
        assert 'data' in analysis
        assert analysis['signal'] in ['bullish', 'bearish', 'neutral']
        assert 0 <= analysis['strength'] <= 1
    
    def test_analyze_onchain_indicators(self):
        """测试链上指标分析"""
        engine = IndicatorEngine()
        
        # 创建模拟链上指标
        mock_indicators = [
            OnChainMetrics(
                symbol='BTC',
                date=datetime.now(timezone.utc),
                price_usd=50000,
                market_cap=1000000000,
                active_addresses=100000,
                new_addresses=1000,
                transaction_count=300000,
                transaction_volume=5000000000,
                mvrv_ratio=1.5,
                nvt_ratio=80,
                rvt_ratio=60,
                holder_distribution={'whales': 0.01, 'large_holders': 0.04},
                whale_concentration=0.15,
                exchange_flow_ratio=0.2,
                dormant_circulation=0.3,
                fear_greed_index=65,
                social_sentiment=0.6
            )
        ]
        
        analysis = engine._analyze_onchain_indicators(mock_indicators)
        
        assert 'signal' in analysis
        assert 'strength' in analysis
        assert 'data' in analysis
        assert analysis['signal'] in ['bullish', 'bearish', 'neutral']
        assert 0 <= analysis['strength'] <= 1
    
    def test_combine_signals(self):
        """测试信号合并"""
        engine = IndicatorEngine()
        
        technical_analysis = {
            'signal': 'bullish',
            'strength': 0.8
        }
        
        onchain_analysis = {
            'signal': 'bullish',
            'strength': 0.6
        }
        
        signal, strength, confidence = engine._combine_signals(technical_analysis, onchain_analysis)
        
        assert signal in ['bullish', 'bearish', 'neutral']
        assert 0 <= strength <= 1
        assert 0 <= confidence <= 1
        
        # 当两个信号一致时，置信度应该较高
        assert confidence > 0.6
    
    def test_generate_recommendation(self):
        """测试建议生成"""
        engine = IndicatorEngine()
        
        technical_analysis = {'data': {'rsi': 65}}
        onchain_analysis = {'data': {'mvrv_ratio': 1.5}}
        
        recommendation, risk_level, time_horizon = engine._generate_recommendation(
            'bullish', 0.8, 0.9, technical_analysis, onchain_analysis
        )
        
        valid_recommendations = ['strong_buy', 'buy', 'hold', 'sell', 'strong_sell']
        valid_risk_levels = ['low', 'medium', 'high']
        valid_time_horizons = ['short_term', 'medium_term', 'long_term']
        
        assert recommendation in valid_recommendations
        assert risk_level in valid_risk_levels
        assert time_horizon in valid_time_horizons
    
    @pytest.mark.asyncio
    async def test_batch_analyze(self):
        """测试批量分析"""
        with patch('src.indicators.indicator_engine.OnChainIndicatorCalculator'):
            engine = IndicatorEngine()
            
            # 模拟单个分析结果
            mock_signal = CompositeSignal(
                symbol='BTC',
                timestamp=datetime.now(timezone.utc),
                technical_signal='bullish',
                technical_strength=0.8,
                onchain_signal='bullish',
                onchain_strength=0.6,
                composite_signal='bullish',
                composite_strength=0.7,
                confidence=0.8,
                technical_data={},
                onchain_data={},
                recommendation='buy',
                risk_level='medium',
                time_horizon='medium_term'
            )
            
            with patch.object(engine, 'analyze_symbol', return_value=mock_signal):
                results = await engine.batch_analyze(['BTC', 'ETH'])
                
                assert len(results) == 2
                assert all(isinstance(signal, CompositeSignal) for signal in results)


class TestIntegration:
    """集成测试"""
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_full_indicator_workflow(self):
        """测试完整指标工作流程"""
        # 这个测试需要真实的数据库连接，通常在开发环境中跳过
        pytest.skip("Integration test requires real database connection")
        
        async with IndicatorEngine() as engine:
            # 分析单个代币
            signal = await engine.analyze_symbol('BTC', days=30)
            assert isinstance(signal, CompositeSignal)
            
            # 批量分析
            signals = await engine.batch_analyze(['BTC', 'ETH'], days=30)
            assert len(signals) >= 0
            
            # 市场概览
            overview = await engine.get_market_overview(['BTC', 'ETH'])
            assert 'market_sentiment' in overview


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v", "-m", "not integration"])
