# 安装指南

## 系统要求

- Python 3.8 或更高版本
- PostgreSQL 12 或更高版本
- Redis 6 或更高版本
- Git

## 安装步骤

### 1. 安装Python

#### Windows
1. 访问 [Python官网](https://www.python.org/downloads/)
2. 下载最新版本的Python 3.8+
3. 运行安装程序，确保勾选"Add Python to PATH"
4. 验证安装：
   ```bash
   python --version
   pip --version
   ```

#### macOS
```bash
# 使用Homebrew安装
brew install python@3.9

# 或者下载官方安装包
# https://www.python.org/downloads/macos/
```

#### Linux (Ubuntu/Debian)
```bash
sudo apt update
sudo apt install python3 python3-pip python3-venv
```

### 2. 安装PostgreSQL

#### Windows
1. 访问 [PostgreSQL官网](https://www.postgresql.org/download/windows/)
2. 下载并安装PostgreSQL
3. 记住设置的密码

#### macOS
```bash
brew install postgresql
brew services start postgresql
```

#### Linux
```bash
sudo apt install postgresql postgresql-contrib
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

### 3. 安装Redis

#### Windows
1. 访问 [Redis官网](https://redis.io/download)
2. 下载Windows版本或使用WSL

#### macOS
```bash
brew install redis
brew services start redis
```

#### Linux
```bash
sudo apt install redis-server
sudo systemctl start redis-server
sudo systemctl enable redis-server
```

### 4. 项目设置

1. **克隆项目**（如果从Git仓库）
   ```bash
   git clone <repository-url>
   cd 链上数据方向
   ```

2. **创建虚拟环境**
   ```bash
   python -m venv venv
   
   # Windows
   venv\Scripts\activate
   
   # macOS/Linux
   source venv/bin/activate
   ```

3. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

4. **配置环境变量**
   ```bash
   # 复制环境变量模板
   cp .env.template .env
   
   # 编辑.env文件，填入真实的配置值
   ```

5. **配置API密钥**
   ```bash
   # 复制API密钥模板
   cp config/api_keys.py.template config/api_keys.py
   
   # 编辑config/api_keys.py文件，填入真实的API密钥
   ```

6. **设置数据库**
   ```bash
   # 连接到PostgreSQL
   psql -U postgres
   
   # 创建数据库
   CREATE DATABASE onchain_analysis;
   CREATE USER onchain_user WITH PASSWORD 'your_password';
   GRANT ALL PRIVILEGES ON DATABASE onchain_analysis TO onchain_user;
   \q
   ```

7. **初始化数据库表**
   ```bash
   python -m src.database.init_db
   ```

8. **测试安装**
   ```bash
   python -m pytest tests/
   ```

## API密钥获取

### Etherscan
1. 访问 [Etherscan.io](https://etherscan.io/)
2. 注册账户并登录
3. 前往 API Keys 页面创建新的API密钥

### Moralis
1. 访问 [Moralis.io](https://moralis.io/)
2. 注册账户并创建新项目
3. 获取Web3 API密钥

### CoinGecko
1. 访问 [CoinGecko](https://www.coingecko.com/en/api)
2. 注册Pro账户获取API密钥

### Dune Analytics
1. 访问 [Dune Analytics](https://dune.com/)
2. 注册账户并获取API密钥

### Infura
1. 访问 [Infura.io](https://infura.io/)
2. 注册账户并创建新项目
3. 获取项目ID

## 运行项目

### 开发模式
```bash
# 激活虚拟环境
source venv/bin/activate  # Linux/macOS
# 或
venv\Scripts\activate     # Windows

# 启动Web应用
python web/app.py

# 启动数据收集服务
python -m src.data.collector

# 启动监控服务
python -m src.monitoring.monitor
```

### 生产模式
```bash
# 使用Gunicorn启动Web应用
gunicorn -w 4 -b 0.0.0.0:5000 web.app:app

# 使用Supervisor管理后台服务
sudo supervisorctl start onchain_analysis
```

## 故障排除

### 常见问题

1. **Python模块导入错误**
   - 确保虚拟环境已激活
   - 检查PYTHONPATH设置

2. **数据库连接错误**
   - 检查PostgreSQL服务是否运行
   - 验证数据库配置信息

3. **API调用失败**
   - 检查API密钥是否正确
   - 验证网络连接
   - 检查API调用频率限制

4. **Redis连接错误**
   - 确保Redis服务运行
   - 检查Redis配置

### 日志查看
```bash
# 查看应用日志
tail -f logs/app.log

# 查看错误日志
tail -f logs/error.log
```

## 开发环境设置

### IDE推荐
- PyCharm Professional
- Visual Studio Code
- Jupyter Lab

### 代码质量工具
```bash
# 安装开发工具
pip install black flake8 mypy pytest-cov

# 代码格式化
black src/

# 代码检查
flake8 src/

# 类型检查
mypy src/
```

## 部署指南

### Docker部署
```bash
# 构建镜像
docker build -t onchain-analysis .

# 运行容器
docker-compose up -d
```

### 云服务部署
- AWS EC2 + RDS + ElastiCache
- Google Cloud Platform
- Azure

详细部署文档请参考 `docs/deployment.md`
