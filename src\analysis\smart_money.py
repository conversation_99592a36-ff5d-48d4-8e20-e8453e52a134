"""
Smart Money分析模块
识别、追踪和分析Smart Money地址的行为模式
"""
import asyncio
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timezone, timedelta
from dataclasses import dataclass
from collections import defaultdict
import numpy as np

from src.database.repositories import AddressRepository, TransactionRepository, SmartMoneyActivityRepository
from src.database.data_service import DataService
from src.utils.logger import get_logger
from config.settings import TRADING_CONFIG

logger = get_logger(__name__)


@dataclass
class SmartMoneyMetrics:
    """Smart Money指标"""
    address: str
    total_transactions: int
    total_volume_usd: float
    win_rate: float
    avg_holding_period_hours: float
    roi_percentage: float
    sharpe_ratio: float
    max_drawdown: float
    profit_factor: float
    active_days: int
    unique_tokens: int
    avg_transaction_size: float
    success_score: float


@dataclass
class TokenActivity:
    """代币活动记录"""
    token_address: str
    token_symbol: str
    buy_transactions: List[Dict[str, Any]]
    sell_transactions: List[Dict[str, Any]]
    current_position: float
    realized_pnl: float
    unrealized_pnl: float
    holding_period_hours: float
    entry_price: float
    current_price: float


class SmartMoneyAnalyzer:
    """Smart Money分析器"""
    
    def __init__(self):
        self.address_repo = AddressRepository()
        self.transaction_repo = TransactionRepository()
        self.smart_money_repo = SmartMoneyActivityRepository()
        self.data_service = None
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.data_service = await DataService().__aenter__()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.data_service:
            await self.data_service.__aexit__(exc_type, exc_val, exc_tb)
    
    async def identify_smart_money_addresses(self, min_transactions: int = 50,
                                           min_volume_usd: float = 100000,
                                           min_roi: float = 200.0,
                                           min_win_rate: float = 0.6) -> List[SmartMoneyMetrics]:
        """识别Smart Money地址"""
        try:
            logger.info("Starting Smart Money address identification")
            
            # 获取活跃地址
            active_addresses = await self._get_active_addresses(
                min_transactions=min_transactions,
                min_volume_usd=min_volume_usd
            )
            
            smart_money_candidates = []
            
            for address in active_addresses:
                try:
                    # 分析地址性能
                    metrics = await self.analyze_address_performance(address)
                    
                    # 应用Smart Money筛选条件
                    if (metrics.roi_percentage >= min_roi and 
                        metrics.win_rate >= min_win_rate and
                        metrics.success_score >= 0.7):
                        
                        smart_money_candidates.append(metrics)
                        
                        # 更新数据库中的Smart Money标记
                        await self._update_smart_money_status(address, True, metrics)
                        
                        logger.info(f"Identified Smart Money: {address} (ROI: {metrics.roi_percentage:.2f}%)")
                
                except Exception as e:
                    logger.error(f"Failed to analyze address {address}: {e}")
                    continue
            
            # 按成功分数排序
            smart_money_candidates.sort(key=lambda x: x.success_score, reverse=True)
            
            logger.info(f"Identified {len(smart_money_candidates)} Smart Money addresses")
            return smart_money_candidates
        
        except Exception as e:
            logger.error(f"Failed to identify Smart Money addresses: {e}")
            raise
    
    async def analyze_address_performance(self, address: str) -> SmartMoneyMetrics:
        """分析地址性能指标"""
        try:
            logger.debug(f"Analyzing performance for address: {address}")
            
            # 获取地址的所有交易
            transactions = await self.data_service.get_address_transactions(address, limit=1000)
            token_transfers = await self.data_service.get_token_transfers(address, limit=1000)
            
            # 分析代币活动
            token_activities = await self._analyze_token_activities(address, token_transfers)
            
            # 计算基本指标
            total_transactions = len(transactions) + len(token_transfers)
            total_volume_usd = sum(tx.get('value_usd', 0) for tx in transactions)
            
            # 计算盈利指标
            win_rate, roi_percentage, profit_factor = self._calculate_profitability_metrics(token_activities)
            
            # 计算风险指标
            sharpe_ratio, max_drawdown = self._calculate_risk_metrics(token_activities)
            
            # 计算活跃度指标
            active_days, avg_holding_period = self._calculate_activity_metrics(transactions, token_transfers)
            
            # 计算多样化指标
            unique_tokens = len(set(activity.token_address for activity in token_activities))
            
            # 计算平均交易规模
            avg_transaction_size = total_volume_usd / total_transactions if total_transactions > 0 else 0
            
            # 计算综合成功分数
            success_score = self._calculate_success_score(
                win_rate, roi_percentage, sharpe_ratio, max_drawdown, 
                profit_factor, unique_tokens, active_days
            )
            
            return SmartMoneyMetrics(
                address=address,
                total_transactions=total_transactions,
                total_volume_usd=total_volume_usd,
                win_rate=win_rate,
                avg_holding_period_hours=avg_holding_period,
                roi_percentage=roi_percentage,
                sharpe_ratio=sharpe_ratio,
                max_drawdown=max_drawdown,
                profit_factor=profit_factor,
                active_days=active_days,
                unique_tokens=unique_tokens,
                avg_transaction_size=avg_transaction_size,
                success_score=success_score
            )
        
        except Exception as e:
            logger.error(f"Failed to analyze address performance for {address}: {e}")
            raise
    
    async def _get_active_addresses(self, min_transactions: int, min_volume_usd: float) -> List[str]:
        """获取活跃地址列表"""
        try:
            # 从数据库获取符合条件的地址
            sql = """
                SELECT DISTINCT from_address as address
                FROM transactions 
                WHERE block_timestamp >= :since_date
                GROUP BY from_address
                HAVING COUNT(*) >= :min_transactions
                   AND SUM(value_usd) >= :min_volume_usd
                UNION
                SELECT DISTINCT to_address as address
                FROM transactions 
                WHERE block_timestamp >= :since_date
                  AND to_address IS NOT NULL
                GROUP BY to_address
                HAVING COUNT(*) >= :min_transactions
                LIMIT 1000
            """
            
            since_date = datetime.now(timezone.utc) - timedelta(days=90)  # 过去90天
            
            from src.database.database import db_manager
            results = db_manager.execute_raw_sql(sql, {
                'since_date': since_date,
                'min_transactions': min_transactions,
                'min_volume_usd': min_volume_usd
            })
            
            return [result['address'] for result in results]
        
        except Exception as e:
            logger.error(f"Failed to get active addresses: {e}")
            return []
    
    async def _analyze_token_activities(self, address: str, token_transfers: List[Dict[str, Any]]) -> List[TokenActivity]:
        """分析代币活动"""
        try:
            # 按代币分组交易
            token_groups = defaultdict(lambda: {'buys': [], 'sells': []})
            
            for transfer in token_transfers:
                token_address = transfer['contract_address']
                
                # 判断是买入还是卖出
                if transfer['to_address'].lower() == address.lower():
                    token_groups[token_address]['buys'].append(transfer)
                else:
                    token_groups[token_address]['sells'].append(transfer)
            
            activities = []
            
            for token_address, transactions in token_groups.items():
                if not transactions['buys'] and not transactions['sells']:
                    continue
                
                # 获取代币信息
                token_symbol = transactions['buys'][0]['token_symbol'] if transactions['buys'] else \
                              transactions['sells'][0]['token_symbol']
                
                # 计算持仓和盈亏
                activity = await self._calculate_token_pnl(
                    token_address, token_symbol, transactions['buys'], transactions['sells']
                )
                
                if activity:
                    activities.append(activity)
            
            return activities
        
        except Exception as e:
            logger.error(f"Failed to analyze token activities: {e}")
            return []
    
    async def _calculate_token_pnl(self, token_address: str, token_symbol: str,
                                 buy_txs: List[Dict[str, Any]], 
                                 sell_txs: List[Dict[str, Any]]) -> Optional[TokenActivity]:
        """计算代币盈亏"""
        try:
            if not buy_txs:
                return None
            
            # 计算买入成本和数量
            total_bought = sum(tx['value_formatted'] for tx in buy_txs)
            total_buy_cost = sum(tx.get('value_usd', 0) for tx in buy_txs)
            avg_buy_price = total_buy_cost / total_bought if total_bought > 0 else 0
            
            # 计算卖出收入和数量
            total_sold = sum(tx['value_formatted'] for tx in sell_txs)
            total_sell_revenue = sum(tx.get('value_usd', 0) for tx in sell_txs)
            
            # 计算当前持仓
            current_position = total_bought - total_sold
            
            # 计算已实现盈亏
            realized_pnl = total_sell_revenue - (total_sold * avg_buy_price)
            
            # 获取当前价格计算未实现盈亏
            current_price = await self._get_current_token_price(token_address)
            unrealized_pnl = current_position * (current_price - avg_buy_price) if current_price else 0
            
            # 计算持有期
            if buy_txs and sell_txs:
                first_buy = min(buy_txs, key=lambda x: x['timestamp'])
                last_sell = max(sell_txs, key=lambda x: x['timestamp'])
                holding_period = (last_sell['timestamp'] - first_buy['timestamp']).total_seconds() / 3600
            else:
                holding_period = 0
            
            return TokenActivity(
                token_address=token_address,
                token_symbol=token_symbol,
                buy_transactions=buy_txs,
                sell_transactions=sell_txs,
                current_position=current_position,
                realized_pnl=realized_pnl,
                unrealized_pnl=unrealized_pnl,
                holding_period_hours=holding_period,
                entry_price=avg_buy_price,
                current_price=current_price or 0
            )
        
        except Exception as e:
            logger.error(f"Failed to calculate token PnL for {token_symbol}: {e}")
            return None
    
    async def _get_current_token_price(self, token_address: str) -> float:
        """获取代币当前价格"""
        try:
            # 这里应该调用价格API获取当前价格
            # 暂时返回模拟价格
            return 1.0
        except Exception as e:
            logger.error(f"Failed to get current price for token {token_address}: {e}")
            return 0.0

    def _calculate_profitability_metrics(self, token_activities: List[TokenActivity]) -> Tuple[float, float, float]:
        """计算盈利指标"""
        try:
            if not token_activities:
                return 0.0, 0.0, 0.0

            # 计算胜率
            profitable_trades = sum(1 for activity in token_activities
                                  if activity.realized_pnl + activity.unrealized_pnl > 0)
            total_trades = len(token_activities)
            win_rate = profitable_trades / total_trades if total_trades > 0 else 0

            # 计算总ROI
            total_invested = sum(len(activity.buy_transactions) * activity.entry_price
                               for activity in token_activities)
            total_pnl = sum(activity.realized_pnl + activity.unrealized_pnl
                          for activity in token_activities)
            roi_percentage = (total_pnl / total_invested * 100) if total_invested > 0 else 0

            # 计算盈利因子
            total_profits = sum(max(0, activity.realized_pnl + activity.unrealized_pnl)
                              for activity in token_activities)
            total_losses = sum(min(0, activity.realized_pnl + activity.unrealized_pnl)
                             for activity in token_activities)
            profit_factor = abs(total_profits / total_losses) if total_losses < 0 else float('inf')

            return win_rate, roi_percentage, profit_factor

        except Exception as e:
            logger.error(f"Failed to calculate profitability metrics: {e}")
            return 0.0, 0.0, 0.0

    def _calculate_risk_metrics(self, token_activities: List[TokenActivity]) -> Tuple[float, float]:
        """计算风险指标"""
        try:
            if not token_activities:
                return 0.0, 0.0

            # 计算收益序列
            returns = []
            for activity in token_activities:
                if activity.entry_price > 0:
                    return_pct = (activity.realized_pnl + activity.unrealized_pnl) / \
                               (len(activity.buy_transactions) * activity.entry_price)
                    returns.append(return_pct)

            if not returns:
                return 0.0, 0.0

            returns = np.array(returns)

            # 计算夏普比率
            mean_return = np.mean(returns)
            std_return = np.std(returns)
            sharpe_ratio = mean_return / std_return if std_return > 0 else 0

            # 计算最大回撤
            cumulative_returns = np.cumprod(1 + returns)
            running_max = np.maximum.accumulate(cumulative_returns)
            drawdowns = (cumulative_returns - running_max) / running_max
            max_drawdown = abs(np.min(drawdowns)) if len(drawdowns) > 0 else 0

            return sharpe_ratio, max_drawdown

        except Exception as e:
            logger.error(f"Failed to calculate risk metrics: {e}")
            return 0.0, 0.0

    def _calculate_activity_metrics(self, transactions: List[Dict[str, Any]],
                                  token_transfers: List[Dict[str, Any]]) -> Tuple[int, float]:
        """计算活跃度指标"""
        try:
            all_transactions = transactions + token_transfers

            if not all_transactions:
                return 0, 0.0

            # 计算活跃天数
            timestamps = [tx['timestamp'] for tx in all_transactions]
            first_tx = min(timestamps)
            last_tx = max(timestamps)
            active_days = (last_tx - first_tx).days + 1

            # 计算平均持有期（基于代币转账）
            holding_periods = []

            # 按代币分组计算持有期
            token_groups = defaultdict(list)
            for transfer in token_transfers:
                token_groups[transfer['contract_address']].append(transfer)

            for token_address, transfers in token_groups.items():
                # 按时间排序
                transfers.sort(key=lambda x: x['timestamp'])

                # 计算买入卖出配对的持有期
                buys = [t for t in transfers if t['to_address'].lower() == transfers[0]['to_address'].lower()]
                sells = [t for t in transfers if t['from_address'].lower() == transfers[0]['to_address'].lower()]

                for buy in buys:
                    # 找到对应的卖出交易
                    matching_sells = [s for s in sells if s['timestamp'] > buy['timestamp']]
                    if matching_sells:
                        sell = min(matching_sells, key=lambda x: x['timestamp'])
                        holding_period = (sell['timestamp'] - buy['timestamp']).total_seconds() / 3600
                        holding_periods.append(holding_period)

            avg_holding_period = np.mean(holding_periods) if holding_periods else 0

            return active_days, avg_holding_period

        except Exception as e:
            logger.error(f"Failed to calculate activity metrics: {e}")
            return 0, 0.0

    def _calculate_success_score(self, win_rate: float, roi_percentage: float,
                               sharpe_ratio: float, max_drawdown: float,
                               profit_factor: float, unique_tokens: int,
                               active_days: int) -> float:
        """计算综合成功分数"""
        try:
            # 标准化各项指标到0-1范围
            normalized_win_rate = min(win_rate, 1.0)
            normalized_roi = min(roi_percentage / 1000, 1.0)  # ROI超过1000%得满分
            normalized_sharpe = min(max(sharpe_ratio, 0) / 3, 1.0)  # 夏普比率3以上得满分
            normalized_drawdown = max(0, 1 - max_drawdown)  # 回撤越小分数越高
            normalized_profit_factor = min(profit_factor / 5, 1.0)  # 盈利因子5以上得满分
            normalized_diversity = min(unique_tokens / 20, 1.0)  # 20个以上代币得满分
            normalized_activity = min(active_days / 365, 1.0)  # 活跃一年以上得满分

            # 加权计算综合分数
            weights = {
                'win_rate': 0.25,
                'roi': 0.25,
                'sharpe': 0.15,
                'drawdown': 0.15,
                'profit_factor': 0.10,
                'diversity': 0.05,
                'activity': 0.05
            }

            success_score = (
                normalized_win_rate * weights['win_rate'] +
                normalized_roi * weights['roi'] +
                normalized_sharpe * weights['sharpe'] +
                normalized_drawdown * weights['drawdown'] +
                normalized_profit_factor * weights['profit_factor'] +
                normalized_diversity * weights['diversity'] +
                normalized_activity * weights['activity']
            )

            return success_score

        except Exception as e:
            logger.error(f"Failed to calculate success score: {e}")
            return 0.0

    async def _update_smart_money_status(self, address: str, is_smart_money: bool,
                                       metrics: SmartMoneyMetrics) -> None:
        """更新Smart Money状态"""
        try:
            stats = {
                'is_smart_money': is_smart_money,
                'total_transactions': metrics.total_transactions,
                'total_volume_usd': metrics.total_volume_usd,
                'success_rate': metrics.win_rate,
                'roi_percentage': metrics.roi_percentage,
                'avg_holding_period': metrics.avg_holding_period_hours,
                'risk_score': 1.0 - metrics.max_drawdown,  # 风险分数：1减去最大回撤
                'last_active': datetime.now(timezone.utc)
            }

            self.address_repo.update_address_stats(address, stats)
            logger.debug(f"Updated Smart Money status for {address}")

        except Exception as e:
            logger.error(f"Failed to update Smart Money status for {address}: {e}")

    async def track_smart_money_activities(self, smart_money_addresses: List[str],
                                         hours: int = 24) -> List[Dict[str, Any]]:
        """追踪Smart Money活动"""
        try:
            logger.info(f"Tracking activities for {len(smart_money_addresses)} Smart Money addresses")

            activities = []
            since = datetime.now(timezone.utc) - timedelta(hours=hours)

            for address in smart_money_addresses:
                try:
                    # 获取最近的代币转账
                    recent_transfers = await self.data_service.get_token_transfers(
                        address, limit=50
                    )

                    # 过滤最近的活动
                    recent_activities = [
                        transfer for transfer in recent_transfers
                        if transfer['timestamp'] >= since
                    ]

                    for transfer in recent_activities:
                        # 判断是买入还是卖出
                        action_type = 'buy' if transfer['to_address'].lower() == address.lower() else 'sell'

                        # 获取当前价格计算USD价值
                        current_price = await self._get_current_token_price(transfer['contract_address'])
                        usd_amount = transfer['value_formatted'] * current_price

                        activity = {
                            'address': address,
                            'tx_hash': transfer['hash'],
                            'action_type': action_type,
                            'token_address': transfer['contract_address'],
                            'token_symbol': transfer['token_symbol'],
                            'token_amount': transfer['value_formatted'],
                            'usd_amount': usd_amount,
                            'price_at_action': current_price,  # 简化处理，实际应该获取历史价格
                            'timestamp': transfer['timestamp'],
                            'confidence_score': 0.8  # 基于地址的Smart Money评分
                        }

                        activities.append(activity)

                        # 存储到数据库
                        await self._store_smart_money_activity(activity)

                except Exception as e:
                    logger.error(f"Failed to track activities for address {address}: {e}")
                    continue

            # 按时间排序
            activities.sort(key=lambda x: x['timestamp'], reverse=True)

            logger.info(f"Tracked {len(activities)} Smart Money activities")
            return activities

        except Exception as e:
            logger.error(f"Failed to track Smart Money activities: {e}")
            raise

    async def _store_smart_money_activity(self, activity: Dict[str, Any]) -> None:
        """存储Smart Money活动"""
        try:
            activity_data = {
                'address': activity['address'],
                'tx_hash': activity['tx_hash'],
                'action_type': activity['action_type'],
                'token_address': activity['token_address'],
                'token_amount': activity['token_amount'],
                'usd_amount': activity['usd_amount'],
                'price_at_action': activity['price_at_action'],
                'confidence_score': activity['confidence_score']
            }

            self.smart_money_repo.create(**activity_data)
            logger.debug(f"Stored Smart Money activity: {activity['tx_hash']}")

        except Exception as e:
            logger.error(f"Failed to store Smart Money activity: {e}")
