"""
Smart Money跟随策略
实现基于Smart Money行为的交易策略
"""
import asyncio
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timezone, timedelta
from dataclasses import dataclass
from enum import Enum

from .smart_money_signals import SmartMoneySignalGenerator, SmartMoneySignal
from src.database.repositories import AddressRepository, SmartMoneyActivityRepository
from src.utils.logger import get_logger
from config.settings import TRADING_CONFIG

logger = get_logger(__name__)


class FollowStrategy(Enum):
    """跟随策略类型"""
    IMMEDIATE = "immediate"  # 立即跟随
    DELAYED = "delayed"      # 延迟跟随
    CONSENSUS = "consensus"  # 共识跟随
    SELECTIVE = "selective"  # 选择性跟随


@dataclass
class FollowRule:
    """跟随规则"""
    min_smart_money_count: int = 3  # 最少Smart Money地址数量
    min_total_volume: float = 100000  # 最小总交易量（USD）
    min_confidence: float = 0.7  # 最小置信度
    max_follow_amount: float = 10000  # 最大跟随金额（USD）
    follow_ratio: float = 0.01  # 跟随比例（相对于Smart Money交易量）
    time_window_hours: int = 4  # 时间窗口（小时）
    stop_loss_pct: float = 0.05  # 止损百分比
    take_profit_pct: float = 0.15  # 止盈百分比


@dataclass
class FollowPosition:
    """跟随仓位"""
    token_symbol: str
    action: str  # buy, sell
    amount_usd: float
    entry_price: float
    entry_time: datetime
    stop_loss: float
    take_profit: float
    source_signal: SmartMoneySignal
    status: str  # active, closed, stopped
    current_pnl: float = 0.0


class SmartMoneyFollowStrategy:
    """Smart Money跟随策略"""
    
    def __init__(self, strategy_type: FollowStrategy = FollowStrategy.CONSENSUS,
                 follow_rules: FollowRule = None):
        self.strategy_type = strategy_type
        self.follow_rules = follow_rules or FollowRule()
        self.signal_generator = None
        self.address_repo = AddressRepository()
        self.smart_money_repo = SmartMoneyActivityRepository()
        self.active_positions: List[FollowPosition] = []
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.signal_generator = await SmartMoneySignalGenerator().__aenter__()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.signal_generator:
            await self.signal_generator.__aexit__(exc_type, exc_val, exc_tb)
    
    async def execute_strategy(self, hours: int = 4) -> List[Dict[str, Any]]:
        """执行跟随策略"""
        try:
            logger.info(f"Executing Smart Money follow strategy ({self.strategy_type.value})")
            
            # 生成Smart Money信号
            signals = await self.signal_generator.generate_signals(hours=hours)
            
            if not signals:
                logger.info("No Smart Money signals found")
                return []
            
            # 根据策略类型过滤和处理信号
            filtered_signals = await self._filter_signals_by_strategy(signals)
            
            # 生成跟随决策
            follow_decisions = []
            
            for signal in filtered_signals:
                try:
                    decision = await self._evaluate_follow_decision(signal)
                    if decision:
                        follow_decisions.append(decision)
                        logger.info(f"Generated follow decision: {decision['action']} {decision['token_symbol']} "
                                  f"(amount: ${decision['amount_usd']:.2f})")
                
                except Exception as e:
                    logger.error(f"Failed to evaluate follow decision for {signal.symbol}: {e}")
                    continue
            
            # 执行跟随决策
            executed_positions = []
            for decision in follow_decisions:
                try:
                    position = await self._execute_follow_decision(decision)
                    if position:
                        executed_positions.append(position)
                        self.active_positions.append(position)
                
                except Exception as e:
                    logger.error(f"Failed to execute follow decision: {e}")
                    continue
            
            logger.info(f"Executed {len(executed_positions)} follow positions")
            return [self._position_to_dict(pos) for pos in executed_positions]
        
        except Exception as e:
            logger.error(f"Failed to execute Smart Money follow strategy: {e}")
            raise
    
    async def _filter_signals_by_strategy(self, signals: List[SmartMoneySignal]) -> List[SmartMoneySignal]:
        """根据策略类型过滤信号"""
        try:
            filtered_signals = []
            
            for signal in signals:
                # 基本过滤条件
                if (signal.confidence < self.follow_rules.min_confidence or
                    len(signal.source_addresses) < self.follow_rules.min_smart_money_count):
                    continue
                
                # 检查交易量
                supporting_data = signal.supporting_data
                total_volume = supporting_data.get('buy_volume_usd', 0) + supporting_data.get('sell_volume_usd', 0)
                if total_volume < self.follow_rules.min_total_volume:
                    continue
                
                # 根据策略类型应用特定过滤
                if self.strategy_type == FollowStrategy.IMMEDIATE:
                    # 立即跟随：只要满足基本条件就跟随
                    filtered_signals.append(signal)
                
                elif self.strategy_type == FollowStrategy.DELAYED:
                    # 延迟跟随：等待一段时间后再跟随
                    signal_age = (datetime.now(timezone.utc) - signal.generated_at).total_seconds() / 3600
                    if signal_age >= 1:  # 至少等待1小时
                        filtered_signals.append(signal)
                
                elif self.strategy_type == FollowStrategy.CONSENSUS:
                    # 共识跟随：需要多个Smart Money地址一致行动
                    if len(signal.source_addresses) >= 5:  # 至少5个地址
                        filtered_signals.append(signal)
                
                elif self.strategy_type == FollowStrategy.SELECTIVE:
                    # 选择性跟随：只跟随顶级Smart Money
                    top_addresses = await self._get_top_smart_money_addresses()
                    if any(addr in top_addresses for addr in signal.source_addresses):
                        filtered_signals.append(signal)
            
            logger.info(f"Filtered {len(filtered_signals)} signals from {len(signals)} total signals")
            return filtered_signals
        
        except Exception as e:
            logger.error(f"Failed to filter signals: {e}")
            return []
    
    async def _get_top_smart_money_addresses(self, limit: int = 50) -> List[str]:
        """获取顶级Smart Money地址"""
        try:
            top_addresses = self.address_repo.get_smart_money_addresses(limit=limit)
            return [addr.address for addr in top_addresses]
        except Exception as e:
            logger.error(f"Failed to get top Smart Money addresses: {e}")
            return []
    
    async def _evaluate_follow_decision(self, signal: SmartMoneySignal) -> Optional[Dict[str, Any]]:
        """评估跟随决策"""
        try:
            # 检查是否已有该代币的活跃仓位
            existing_position = next((pos for pos in self.active_positions 
                                   if pos.token_symbol == signal.symbol and pos.status == 'active'), None)
            
            if existing_position:
                logger.debug(f"Already have active position for {signal.symbol}")
                return None
            
            # 计算跟随金额
            supporting_data = signal.supporting_data
            smart_money_volume = supporting_data.get('buy_volume_usd', 0) if signal.action == 'buy' else \
                               supporting_data.get('sell_volume_usd', 0)
            
            # 基于Smart Money交易量和跟随比例计算金额
            follow_amount = min(
                smart_money_volume * self.follow_rules.follow_ratio,
                self.follow_rules.max_follow_amount
            )
            
            # 确保最小跟随金额
            if follow_amount < 100:  # 最小100美元
                return None
            
            # 计算风险参数
            entry_price = 1.0  # 简化处理，实际应该获取当前价格
            stop_loss = entry_price * (1 - self.follow_rules.stop_loss_pct) if signal.action == 'buy' else \
                       entry_price * (1 + self.follow_rules.stop_loss_pct)
            take_profit = entry_price * (1 + self.follow_rules.take_profit_pct) if signal.action == 'buy' else \
                         entry_price * (1 - self.follow_rules.take_profit_pct)
            
            return {
                'token_symbol': signal.symbol,
                'action': signal.action,
                'amount_usd': follow_amount,
                'entry_price': entry_price,
                'stop_loss': stop_loss,
                'take_profit': take_profit,
                'signal': signal,
                'confidence': signal.confidence,
                'strength': signal.strength
            }
        
        except Exception as e:
            logger.error(f"Failed to evaluate follow decision: {e}")
            return None
    
    async def _execute_follow_decision(self, decision: Dict[str, Any]) -> Optional[FollowPosition]:
        """执行跟随决策"""
        try:
            # 在实际实现中，这里会调用交易所API执行交易
            # 现在只是创建仓位记录
            
            position = FollowPosition(
                token_symbol=decision['token_symbol'],
                action=decision['action'],
                amount_usd=decision['amount_usd'],
                entry_price=decision['entry_price'],
                entry_time=datetime.now(timezone.utc),
                stop_loss=decision['stop_loss'],
                take_profit=decision['take_profit'],
                source_signal=decision['signal'],
                status='active'
            )
            
            logger.info(f"Executed follow position: {position.action} {position.token_symbol} "
                       f"${position.amount_usd:.2f}")
            
            return position
        
        except Exception as e:
            logger.error(f"Failed to execute follow decision: {e}")
            return None
    
    async def monitor_positions(self) -> List[Dict[str, Any]]:
        """监控活跃仓位"""
        try:
            logger.info(f"Monitoring {len(self.active_positions)} active positions")
            
            position_updates = []
            
            for position in self.active_positions:
                if position.status != 'active':
                    continue
                
                try:
                    # 获取当前价格（简化处理）
                    current_price = await self._get_current_price(position.token_symbol)
                    
                    # 计算当前盈亏
                    if position.action == 'buy':
                        position.current_pnl = (current_price - position.entry_price) / position.entry_price
                    else:
                        position.current_pnl = (position.entry_price - current_price) / position.entry_price
                    
                    # 检查止损止盈条件
                    should_close, reason = self._check_exit_conditions(position, current_price)
                    
                    if should_close:
                        position.status = 'closed'
                        logger.info(f"Closed position {position.token_symbol} due to {reason} "
                                  f"(PnL: {position.current_pnl:.2%})")
                    
                    position_updates.append({
                        'token_symbol': position.token_symbol,
                        'action': position.action,
                        'status': position.status,
                        'current_pnl': position.current_pnl,
                        'entry_price': position.entry_price,
                        'current_price': current_price,
                        'amount_usd': position.amount_usd,
                        'entry_time': position.entry_time,
                        'close_reason': reason if should_close else None
                    })
                
                except Exception as e:
                    logger.error(f"Failed to monitor position {position.token_symbol}: {e}")
                    continue
            
            return position_updates
        
        except Exception as e:
            logger.error(f"Failed to monitor positions: {e}")
            return []
    
    async def _get_current_price(self, token_symbol: str) -> float:
        """获取当前价格"""
        try:
            # 简化处理，实际应该调用价格API
            return 1.0
        except Exception as e:
            logger.error(f"Failed to get current price for {token_symbol}: {e}")
            return 0.0
    
    def _check_exit_conditions(self, position: FollowPosition, current_price: float) -> Tuple[bool, str]:
        """检查退出条件"""
        try:
            # 检查止损
            if position.action == 'buy' and current_price <= position.stop_loss:
                return True, 'stop_loss'
            elif position.action == 'sell' and current_price >= position.stop_loss:
                return True, 'stop_loss'
            
            # 检查止盈
            if position.action == 'buy' and current_price >= position.take_profit:
                return True, 'take_profit'
            elif position.action == 'sell' and current_price <= position.take_profit:
                return True, 'take_profit'
            
            # 检查时间退出（持有时间过长）
            holding_hours = (datetime.now(timezone.utc) - position.entry_time).total_seconds() / 3600
            if holding_hours > 72:  # 持有超过72小时
                return True, 'time_exit'
            
            return False, ''
        
        except Exception as e:
            logger.error(f"Failed to check exit conditions: {e}")
            return False, 'error'
    
    def _position_to_dict(self, position: FollowPosition) -> Dict[str, Any]:
        """将仓位转换为字典"""
        return {
            'token_symbol': position.token_symbol,
            'action': position.action,
            'amount_usd': position.amount_usd,
            'entry_price': position.entry_price,
            'entry_time': position.entry_time,
            'stop_loss': position.stop_loss,
            'take_profit': position.take_profit,
            'status': position.status,
            'current_pnl': position.current_pnl,
            'signal_strength': position.source_signal.strength,
            'signal_confidence': position.source_signal.confidence
        }
    
    async def get_strategy_performance(self, days: int = 30) -> Dict[str, Any]:
        """获取策略表现"""
        try:
            # 获取历史仓位（简化处理）
            closed_positions = [pos for pos in self.active_positions if pos.status == 'closed']
            
            if not closed_positions:
                return {
                    'total_trades': 0,
                    'win_rate': 0.0,
                    'avg_return': 0.0,
                    'total_return': 0.0,
                    'max_drawdown': 0.0,
                    'sharpe_ratio': 0.0
                }
            
            # 计算基本统计
            total_trades = len(closed_positions)
            winning_trades = sum(1 for pos in closed_positions if pos.current_pnl > 0)
            win_rate = winning_trades / total_trades
            
            returns = [pos.current_pnl for pos in closed_positions]
            avg_return = sum(returns) / len(returns)
            total_return = sum(returns)
            
            # 计算最大回撤（简化）
            cumulative_returns = []
            cumulative = 0
            for ret in returns:
                cumulative += ret
                cumulative_returns.append(cumulative)
            
            peak = cumulative_returns[0]
            max_drawdown = 0
            for ret in cumulative_returns:
                if ret > peak:
                    peak = ret
                drawdown = (peak - ret) / peak if peak != 0 else 0
                max_drawdown = max(max_drawdown, drawdown)
            
            # 计算夏普比率（简化）
            import numpy as np
            sharpe_ratio = np.mean(returns) / np.std(returns) if np.std(returns) > 0 else 0
            
            return {
                'total_trades': total_trades,
                'winning_trades': winning_trades,
                'win_rate': win_rate,
                'avg_return': avg_return,
                'total_return': total_return,
                'max_drawdown': max_drawdown,
                'sharpe_ratio': sharpe_ratio,
                'active_positions': len([pos for pos in self.active_positions if pos.status == 'active'])
            }
        
        except Exception as e:
            logger.error(f"Failed to get strategy performance: {e}")
            return {}
