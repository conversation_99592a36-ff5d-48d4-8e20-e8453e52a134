"""
数据API测试模块
测试各种数据获取API的功能
"""
import asyncio
import pytest
from unittest.mock import Mock, patch
from datetime import datetime, timezone

from src.data.blockchain_api import EtherscanAPI, MoralisAPI
from src.data.defi_data import <PERSON>fiLlamaAPI
from src.data.market_data import CoinGeckoAPI
from src.data.data_coordinator import DataCoordinator


class TestEtherscanAPI:
    """Etherscan API测试"""
    
    @pytest.mark.asyncio
    async def test_get_balance_valid_address(self):
        """测试获取有效地址余额"""
        # 模拟API响应
        mock_response = {
            'status': '1',
            'message': 'OK',
            'result': '1000000000000000000'  # 1 ETH in wei
        }
        
        with patch('aiohttp.ClientSession.get') as mock_get:
            mock_get.return_value.__aenter__.return_value.json.return_value = mock_response
            mock_get.return_value.__aenter__.return_value.raise_for_status.return_value = None
            
            async with EtherscanAPI() as api:
                result = await api.get_balance('******************************************')
                
                assert result['balance_wei'] == 1000000000000000000
                assert result['balance_eth'] == 1.0
                assert 'timestamp' in result
    
    @pytest.mark.asyncio
    async def test_get_balance_invalid_address(self):
        """测试获取无效地址余额"""
        async with EtherscanAPI() as api:
            with pytest.raises(ValueError, match="Invalid address"):
                await api.get_balance('invalid_address')
    
    @pytest.mark.asyncio
    async def test_get_transactions(self):
        """测试获取交易历史"""
        mock_response = {
            'status': '1',
            'message': 'OK',
            'result': [
                {
                    'hash': '0x123...',
                    'blockNumber': '12345678',
                    'timeStamp': '1640995200',  # 2022-01-01 00:00:00 UTC
                    'from': '0xabc...',
                    'to': '0xdef...',
                    'value': '1000000000000000000',
                    'gasUsed': '21000',
                    'gasPrice': '20000000000',
                    'isError': '0',
                    'input': '0x'
                }
            ]
        }
        
        with patch('aiohttp.ClientSession.get') as mock_get:
            mock_get.return_value.__aenter__.return_value.json.return_value = mock_response
            mock_get.return_value.__aenter__.return_value.raise_for_status.return_value = None
            
            async with EtherscanAPI() as api:
                result = await api.get_transactions('******************************************')
                
                assert len(result) == 1
                assert result[0]['hash'] == '0x123...'
                assert result[0]['value_eth'] == 1.0
                assert result[0]['is_error'] == False


class TestCoinGeckoAPI:
    """CoinGecko API测试"""
    
    @pytest.mark.asyncio
    async def test_get_price(self):
        """测试获取价格"""
        mock_response = {
            'bitcoin': {
                'usd': 45000,
                'usd_market_cap': 850000000000,
                'usd_24h_vol': 25000000000,
                'usd_24h_change': 2.5,
                'last_updated_at': 1640995200
            }
        }
        
        with patch('aiohttp.ClientSession.get') as mock_get:
            mock_get.return_value.__aenter__.return_value.json.return_value = mock_response
            mock_get.return_value.__aenter__.return_value.raise_for_status.return_value = None
            
            async with CoinGeckoAPI() as api:
                result = await api.get_price('bitcoin')
                
                assert 'bitcoin' in result
                assert result['bitcoin']['usd'] == 45000
                assert 'timestamp' in result['bitcoin']
    
    @pytest.mark.asyncio
    async def test_get_coin_info(self):
        """测试获取代币信息"""
        mock_response = {
            'id': 'bitcoin',
            'symbol': 'btc',
            'name': 'Bitcoin',
            'description': {'en': 'Bitcoin is a cryptocurrency...'},
            'market_cap_rank': 1,
            'market_data': {
                'current_price': {'usd': 45000},
                'market_cap': {'usd': 850000000000},
                'total_volume': {'usd': 25000000000}
            }
        }
        
        with patch('aiohttp.ClientSession.get') as mock_get:
            mock_get.return_value.__aenter__.return_value.json.return_value = mock_response
            mock_get.return_value.__aenter__.return_value.raise_for_status.return_value = None
            
            async with CoinGeckoAPI() as api:
                result = await api.get_coin_info('bitcoin')
                
                assert result['id'] == 'bitcoin'
                assert result['symbol'] == 'btc'
                assert result['name'] == 'Bitcoin'
                assert 'market_data' in result


class TestDefiLlamaAPI:
    """DefiLlama API测试"""
    
    @pytest.mark.asyncio
    async def test_get_protocols(self):
        """测试获取协议列表"""
        mock_response = [
            {
                'id': '1',
                'name': 'Uniswap',
                'address': '0x...',
                'symbol': 'UNI',
                'chain': 'Ethereum',
                'tvl': 5000000000,
                'category': 'Dexes'
            }
        ]
        
        with patch('aiohttp.ClientSession.get') as mock_get:
            mock_get.return_value.__aenter__.return_value.json.return_value = mock_response
            mock_get.return_value.__aenter__.return_value.raise_for_status.return_value = None
            
            async with DefiLlamaAPI() as api:
                result = await api.get_protocols()
                
                assert len(result) == 1
                assert result[0]['name'] == 'Uniswap'
                assert result[0]['tvl'] == 5000000000
    
    @pytest.mark.asyncio
    async def test_get_yields(self):
        """测试获取收益率数据"""
        mock_response = {
            'data': [
                {
                    'chain': 'Ethereum',
                    'project': 'Compound',
                    'symbol': 'USDC',
                    'tvlUsd': 1000000,
                    'apy': 5.5,
                    'stablecoin': True
                }
            ]
        }
        
        with patch('aiohttp.ClientSession.get') as mock_get:
            mock_get.return_value.__aenter__.return_value.json.return_value = mock_response
            mock_get.return_value.__aenter__.return_value.raise_for_status.return_value = None
            
            async with DefiLlamaAPI() as api:
                result = await api.get_yields()
                
                assert len(result) == 1
                assert result[0]['project'] == 'Compound'
                assert result[0]['apy'] == 5.5


class TestDataCoordinator:
    """数据协调器测试"""
    
    @pytest.mark.asyncio
    async def test_get_address_balance_with_cache(self):
        """测试带缓存的余额获取"""
        mock_balance_data = {
            'address': '******************************************',
            'balance_wei': 1000000000000000000,
            'balance_eth': 1.0,
            'timestamp': datetime.now(timezone.utc)
        }
        
        with patch.object(DataCoordinator, '_get_cached_data', return_value=None), \
             patch.object(DataCoordinator, '_set_cached_data'), \
             patch('src.data.blockchain_api.EtherscanAPI.get_balance', return_value=mock_balance_data):
            
            async with DataCoordinator() as coordinator:
                result = await coordinator.get_address_balance('******************************************')
                
                assert result['balance_eth'] == 1.0
                assert result['address'] == '******************************************'
    
    @pytest.mark.asyncio
    async def test_get_market_overview(self):
        """测试市场概览获取"""
        mock_price_data = {'bitcoin': {'usd': 45000}}
        mock_trending_data = {'coins': []}
        mock_fear_greed_data = {'data': []}
        
        with patch.object(DataCoordinator, 'get_token_price', return_value=mock_price_data), \
             patch('src.data.market_data.CoinGeckoAPI.get_trending', return_value=mock_trending_data), \
             patch('src.data.market_data.CoinGeckoAPI.get_fear_greed_index', return_value=mock_fear_greed_data):
            
            async with DataCoordinator() as coordinator:
                result = await coordinator.get_market_overview(['bitcoin'])
                
                assert 'prices' in result
                assert 'trending' in result
                assert 'fear_greed_index' in result
                assert 'timestamp' in result


# 集成测试（需要真实API密钥，通常在CI/CD中跳过）
class TestIntegration:
    """集成测试"""
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_real_api_calls(self):
        """测试真实API调用（需要有效的API密钥）"""
        # 这个测试需要真实的API密钥，通常在开发环境中跳过
        pytest.skip("Integration test requires real API keys")
        
        async with DataCoordinator() as coordinator:
            # 测试获取Vitalik的地址余额
            vitalik_address = '******************************************'
            balance = await coordinator.get_address_balance(vitalik_address)
            
            assert 'balance_eth' in balance
            assert balance['address'].lower() == vitalik_address.lower()
            
            # 测试获取比特币价格
            price_data = await coordinator.get_token_price('bitcoin')
            
            assert 'bitcoin' in price_data
            assert 'usd' in price_data['bitcoin']
            assert price_data['bitcoin']['usd'] > 0


if __name__ == "__main__":
    # 运行基本测试
    asyncio.run(pytest.main([__file__, "-v"]))
