"""
Web应用主程序
基于FastAPI的链上数据分析平台
"""
import asyncio
from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
import uvicorn
from typing import Dict, List, Optional, Any
from datetime import datetime, timezone, timedelta
import json

from src.web.routers import dashboard, analysis, trading, monitoring, settings
from src.web.websocket_manager import WebSocketManager
from src.database.repositories import MarketDataRepository, TransactionRepository
from src.analysis.smart_money_signals import SmartMoneySignalGenerator
from src.monitoring.whale_monitor import WhaleMonitor
from src.monitoring.realtime_monitor import RealtimeMonitor
from src.utils.logger import get_logger

logger = get_logger(__name__)

# 全局变量
websocket_manager = WebSocketManager()
realtime_monitor = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.info("Starting OnChain Analytics Platform")
    
    # 初始化实时监控
    global realtime_monitor
    try:
        realtime_monitor = RealtimeMonitor()
        # 在后台启动实时监控
        asyncio.create_task(start_realtime_monitoring())
        logger.info("Realtime monitoring started")
    except Exception as e:
        logger.error(f"Failed to start realtime monitoring: {e}")
    
    yield
    
    # 关闭时执行
    logger.info("Shutting down OnChain Analytics Platform")
    if realtime_monitor:
        await realtime_monitor.stop()


# 创建FastAPI应用
app = FastAPI(
    title="OnChain Analytics Platform",
    description="专业的链上数据分析和交易决策平台",
    version="1.0.0",
    lifespan=lifespan
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境中应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 静态文件和模板
app.mount("/static", StaticFiles(directory="src/web/static"), name="static")
templates = Jinja2Templates(directory="src/web/templates")

# 注册路由
app.include_router(dashboard.router, prefix="/api/dashboard", tags=["dashboard"])
app.include_router(analysis.router, prefix="/api/analysis", tags=["analysis"])
app.include_router(trading.router, prefix="/api/trading", tags=["trading"])
app.include_router(monitoring.router, prefix="/api/monitoring", tags=["monitoring"])
app.include_router(settings.router, prefix="/api/settings", tags=["settings"])

# WebSocket路由
app.include_router(websocket_manager.router, prefix="/ws")


async def start_realtime_monitoring():
    """启动实时监控"""
    try:
        if realtime_monitor:
            await realtime_monitor.start(['BTC', 'ETH', 'ADA', 'DOT'])
            
            # 监听事件并通过WebSocket广播
            while realtime_monitor.is_running:
                if realtime_monitor.event_queue:
                    event = realtime_monitor.event_queue.pop(0)
                    
                    # 广播事件到所有连接的客户端
                    await websocket_manager.broadcast({
                        'type': 'realtime_event',
                        'data': {
                            'event_type': event.event_type,
                            'symbol': event.symbol,
                            'timestamp': event.timestamp.isoformat(),
                            'data': event.data,
                            'severity': event.severity,
                            'source': event.source
                        }
                    })
                
                await asyncio.sleep(1)  # 每秒检查一次
    
    except Exception as e:
        logger.error(f"Realtime monitoring error: {e}")


@app.get("/", response_class=HTMLResponse)
async def root():
    """主页"""
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>OnChain Analytics Platform</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            .hero-section {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 100px 0;
            }
            .feature-card {
                transition: transform 0.3s;
                border: none;
                box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            }
            .feature-card:hover {
                transform: translateY(-5px);
            }
            .navbar-brand {
                font-weight: bold;
                font-size: 1.5rem;
            }
        </style>
    </head>
    <body>
        <!-- 导航栏 -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
            <div class="container">
                <a class="navbar-brand" href="/">
                    <i class="fas fa-chart-line me-2"></i>OnChain Analytics
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="/dashboard">仪表板</a>
                    <a class="nav-link" href="/analysis">分析工具</a>
                    <a class="nav-link" href="/trading">交易管理</a>
                    <a class="nav-link" href="/monitoring">实时监控</a>
                </div>
            </div>
        </nav>

        <!-- 主要内容 -->
        <section class="hero-section text-center">
            <div class="container">
                <h1 class="display-4 mb-4">专业链上数据分析平台</h1>
                <p class="lead mb-4">智能分析、实时监控、精准决策</p>
                <a href="/dashboard" class="btn btn-light btn-lg">
                    <i class="fas fa-rocket me-2"></i>开始使用
                </a>
            </div>
        </section>

        <section class="py-5">
            <div class="container">
                <div class="row">
                    <div class="col-md-4 mb-4">
                        <div class="card feature-card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-brain fa-3x text-primary mb-3"></i>
                                <h5 class="card-title">Smart Money分析</h5>
                                <p class="card-text">追踪聪明资金动向，识别市场趋势</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-4">
                        <div class="card feature-card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-whale fa-3x text-success mb-3"></i>
                                <h5 class="card-title">巨鲸监控</h5>
                                <p class="card-text">实时监控大额交易，预警市场变化</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-4">
                        <div class="card feature-card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-shield-alt fa-3x text-warning mb-3"></i>
                                <h5 class="card-title">风险管理</h5>
                                <p class="card-text">智能风险评估，保护投资安全</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    </body>
    </html>
    """


@app.get("/dashboard", response_class=HTMLResponse)
async def dashboard():
    """仪表板页面"""
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>仪表板 - OnChain Analytics</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <style>
            .metric-card {
                border-left: 4px solid #007bff;
                transition: all 0.3s;
            }
            .metric-card:hover {
                box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            }
            .sidebar {
                min-height: 100vh;
                background-color: #f8f9fa;
            }
        </style>
    </head>
    <body>
        <div class="container-fluid">
            <div class="row">
                <!-- 侧边栏 -->
                <div class="col-md-2 sidebar p-3">
                    <h5><i class="fas fa-chart-line me-2"></i>OnChain Analytics</h5>
                    <hr>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="/dashboard">
                                <i class="fas fa-tachometer-alt me-2"></i>仪表板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/analysis">
                                <i class="fas fa-chart-bar me-2"></i>分析工具
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/trading">
                                <i class="fas fa-exchange-alt me-2"></i>交易管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/monitoring">
                                <i class="fas fa-eye me-2"></i>实时监控
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- 主内容区 -->
                <div class="col-md-10 p-4">
                    <h2>市场概览</h2>
                    
                    <!-- 关键指标 -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card metric-card">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="text-muted">BTC价格</h6>
                                            <h4 id="btc-price">$50,000</h4>
                                            <small class="text-success">+2.5%</small>
                                        </div>
                                        <i class="fab fa-bitcoin fa-2x text-warning"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card metric-card">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="text-muted">ETH价格</h6>
                                            <h4 id="eth-price">$3,000</h4>
                                            <small class="text-danger">-1.2%</small>
                                        </div>
                                        <i class="fab fa-ethereum fa-2x text-primary"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card metric-card">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="text-muted">巨鲸活动</h6>
                                            <h4 id="whale-activity">15</h4>
                                            <small class="text-info">24h内</small>
                                        </div>
                                        <i class="fas fa-whale fa-2x text-info"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card metric-card">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="text-muted">Smart Money</h6>
                                            <h4 id="smart-money">8</h4>
                                            <small class="text-success">买入信号</small>
                                        </div>
                                        <i class="fas fa-brain fa-2x text-success"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 图表区域 -->
                    <div class="row">
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-header">
                                    <h5>价格走势</h5>
                                </div>
                                <div class="card-body">
                                    <canvas id="priceChart" height="300"></canvas>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5>实时事件</h5>
                                </div>
                                <div class="card-body" style="height: 400px; overflow-y: auto;">
                                    <div id="realtime-events">
                                        <div class="alert alert-info alert-sm">
                                            <small>连接实时数据流...</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script>
            // 初始化价格图表
            const ctx = document.getElementById('priceChart').getContext('2d');
            const priceChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'BTC',
                        data: [],
                        borderColor: 'rgb(255, 193, 7)',
                        backgroundColor: 'rgba(255, 193, 7, 0.1)',
                        tension: 0.1
                    }, {
                        label: 'ETH',
                        data: [],
                        borderColor: 'rgb(13, 110, 253)',
                        backgroundColor: 'rgba(13, 110, 253, 0.1)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: false
                        }
                    }
                }
            });

            // WebSocket连接
            const ws = new WebSocket('ws://localhost:8000/ws/realtime');
            
            ws.onopen = function(event) {
                console.log('WebSocket连接已建立');
                document.getElementById('realtime-events').innerHTML = 
                    '<div class="alert alert-success alert-sm"><small>实时数据连接成功</small></div>';
            };
            
            ws.onmessage = function(event) {
                const data = JSON.parse(event.data);
                
                if (data.type === 'realtime_event') {
                    addRealtimeEvent(data.data);
                } else if (data.type === 'price_update') {
                    updatePrices(data.data);
                }
            };
            
            ws.onerror = function(error) {
                console.error('WebSocket错误:', error);
                document.getElementById('realtime-events').innerHTML = 
                    '<div class="alert alert-danger alert-sm"><small>连接错误</small></div>';
            };

            function addRealtimeEvent(eventData) {
                const eventsContainer = document.getElementById('realtime-events');
                const eventElement = document.createElement('div');
                
                let alertClass = 'alert-info';
                if (eventData.severity === 'high') alertClass = 'alert-danger';
                else if (eventData.severity === 'medium') alertClass = 'alert-warning';
                
                eventElement.className = `alert ${alertClass} alert-sm mb-2`;
                eventElement.innerHTML = `
                    <small>
                        <strong>${eventData.symbol}</strong> - ${eventData.event_type}<br>
                        <span class="text-muted">${new Date(eventData.timestamp).toLocaleTimeString()}</span>
                    </small>
                `;
                
                eventsContainer.insertBefore(eventElement, eventsContainer.firstChild);
                
                // 保持最多20个事件
                while (eventsContainer.children.length > 20) {
                    eventsContainer.removeChild(eventsContainer.lastChild);
                }
            }

            function updatePrices(priceData) {
                // 更新价格显示
                if (priceData.BTC) {
                    document.getElementById('btc-price').textContent = `$${priceData.BTC.toLocaleString()}`;
                }
                if (priceData.ETH) {
                    document.getElementById('eth-price').textContent = `$${priceData.ETH.toLocaleString()}`;
                }
                
                // 更新图表
                const now = new Date().toLocaleTimeString();
                priceChart.data.labels.push(now);
                
                if (priceData.BTC) {
                    priceChart.data.datasets[0].data.push(priceData.BTC);
                }
                if (priceData.ETH) {
                    priceChart.data.datasets[1].data.push(priceData.ETH);
                }
                
                // 保持最多50个数据点
                if (priceChart.data.labels.length > 50) {
                    priceChart.data.labels.shift();
                    priceChart.data.datasets.forEach(dataset => dataset.data.shift());
                }
                
                priceChart.update('none');
            }

            // 定期获取数据
            setInterval(async () => {
                try {
                    const response = await fetch('/api/dashboard/overview');
                    const data = await response.json();
                    
                    // 更新指标
                    if (data.whale_activity) {
                        document.getElementById('whale-activity').textContent = data.whale_activity;
                    }
                    if (data.smart_money_signals) {
                        document.getElementById('smart-money').textContent = data.smart_money_signals;
                    }
                } catch (error) {
                    console.error('获取数据失败:', error);
                }
            }, 30000); // 每30秒更新一次
        </script>
    </body>
    </html>
    """


@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "version": "1.0.0",
        "services": {
            "database": "connected",
            "realtime_monitor": "running" if realtime_monitor and realtime_monitor.is_running else "stopped",
            "websocket": "active"
        }
    }


@app.get("/api/status")
async def get_system_status():
    """获取系统状态"""
    try:
        # 检查各个组件状态
        status = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "components": {
                "database": {
                    "status": "healthy",
                    "last_check": datetime.now(timezone.utc).isoformat()
                },
                "realtime_monitor": {
                    "status": "running" if realtime_monitor and realtime_monitor.is_running else "stopped",
                    "monitored_symbols": realtime_monitor.monitored_symbols if realtime_monitor else [],
                    "event_queue_size": len(realtime_monitor.event_queue) if realtime_monitor else 0
                },
                "websocket": {
                    "status": "active",
                    "connected_clients": len(websocket_manager.active_connections)
                }
            },
            "metrics": {
                "uptime_seconds": 0,  # 简化处理
                "memory_usage_mb": 0,
                "cpu_usage_percent": 0
            }
        }
        
        return status
    
    except Exception as e:
        logger.error(f"Failed to get system status: {e}")
        raise HTTPException(status_code=500, detail="Failed to get system status")


if __name__ == "__main__":
    # 运行应用
    uvicorn.run(
        "src.web.app:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
