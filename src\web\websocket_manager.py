"""
WebSocket管理器
处理实时数据推送和客户端连接管理
"""
import asyncio
import json
from typing import Dict, List, Set
from datetime import datetime, timezone
from fastapi import WebSocket, WebSocketDisconnect, APIRouter
from fastapi.websockets import WebSocketState

from src.utils.logger import get_logger

logger = get_logger(__name__)


class WebSocketManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        # 活跃连接
        self.active_connections: List[WebSocket] = []
        
        # 订阅管理
        self.subscriptions: Dict[WebSocket, Set[str]] = {}
        
        # 路由器
        self.router = APIRouter()
        self._setup_routes()
    
    def _setup_routes(self):
        """设置WebSocket路由"""
        
        @self.router.websocket("/realtime")
        async def websocket_endpoint(websocket: WebSocket):
            await self.connect(websocket)
            try:
                while True:
                    # 接收客户端消息
                    data = await websocket.receive_text()
                    message = json.loads(data)
                    
                    # 处理订阅请求
                    if message.get('type') == 'subscribe':
                        await self.subscribe(websocket, message.get('channels', []))
                    elif message.get('type') == 'unsubscribe':
                        await self.unsubscribe(websocket, message.get('channels', []))
                    elif message.get('type') == 'ping':
                        await self.send_personal_message({'type': 'pong'}, websocket)
            
            except WebSocketDisconnect:
                await self.disconnect(websocket)
            except Exception as e:
                logger.error(f"WebSocket error: {e}")
                await self.disconnect(websocket)
    
    async def connect(self, websocket: WebSocket):
        """接受WebSocket连接"""
        try:
            await websocket.accept()
            self.active_connections.append(websocket)
            self.subscriptions[websocket] = set()
            
            logger.info(f"WebSocket connected. Total connections: {len(self.active_connections)}")
            
            # 发送欢迎消息
            await self.send_personal_message({
                'type': 'welcome',
                'message': 'Connected to OnChain Analytics Platform',
                'timestamp': datetime.now(timezone.utc).isoformat()
            }, websocket)
        
        except Exception as e:
            logger.error(f"Failed to connect WebSocket: {e}")
    
    async def disconnect(self, websocket: WebSocket):
        """断开WebSocket连接"""
        try:
            if websocket in self.active_connections:
                self.active_connections.remove(websocket)
            
            if websocket in self.subscriptions:
                del self.subscriptions[websocket]
            
            logger.info(f"WebSocket disconnected. Total connections: {len(self.active_connections)}")
        
        except Exception as e:
            logger.error(f"Error during WebSocket disconnect: {e}")
    
    async def send_personal_message(self, message: dict, websocket: WebSocket):
        """发送个人消息"""
        try:
            if websocket.client_state == WebSocketState.CONNECTED:
                await websocket.send_text(json.dumps(message))
        except Exception as e:
            logger.error(f"Failed to send personal message: {e}")
            await self.disconnect(websocket)
    
    async def broadcast(self, message: dict, channel: str = None):
        """广播消息"""
        if not self.active_connections:
            return
        
        # 准备消息
        message_text = json.dumps(message)
        
        # 发送给所有连接或特定频道的订阅者
        disconnected_connections = []
        
        for connection in self.active_connections:
            try:
                # 检查频道订阅
                if channel and channel not in self.subscriptions.get(connection, set()):
                    continue
                
                if connection.client_state == WebSocketState.CONNECTED:
                    await connection.send_text(message_text)
                else:
                    disconnected_connections.append(connection)
            
            except Exception as e:
                logger.error(f"Failed to broadcast to connection: {e}")
                disconnected_connections.append(connection)
        
        # 清理断开的连接
        for connection in disconnected_connections:
            await self.disconnect(connection)
    
    async def subscribe(self, websocket: WebSocket, channels: List[str]):
        """订阅频道"""
        try:
            if websocket not in self.subscriptions:
                self.subscriptions[websocket] = set()
            
            for channel in channels:
                self.subscriptions[websocket].add(channel)
            
            await self.send_personal_message({
                'type': 'subscription_confirmed',
                'channels': channels,
                'timestamp': datetime.now(timezone.utc).isoformat()
            }, websocket)
            
            logger.info(f"WebSocket subscribed to channels: {channels}")
        
        except Exception as e:
            logger.error(f"Failed to subscribe to channels: {e}")
    
    async def unsubscribe(self, websocket: WebSocket, channels: List[str]):
        """取消订阅频道"""
        try:
            if websocket in self.subscriptions:
                for channel in channels:
                    self.subscriptions[websocket].discard(channel)
            
            await self.send_personal_message({
                'type': 'unsubscription_confirmed',
                'channels': channels,
                'timestamp': datetime.now(timezone.utc).isoformat()
            }, websocket)
            
            logger.info(f"WebSocket unsubscribed from channels: {channels}")
        
        except Exception as e:
            logger.error(f"Failed to unsubscribe from channels: {e}")
    
    async def send_price_update(self, price_data: Dict[str, float]):
        """发送价格更新"""
        message = {
            'type': 'price_update',
            'data': price_data,
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
        
        await self.broadcast(message, channel='prices')
    
    async def send_whale_alert(self, whale_data: dict):
        """发送巨鲸预警"""
        message = {
            'type': 'whale_alert',
            'data': whale_data,
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
        
        await self.broadcast(message, channel='whale_alerts')
    
    async def send_smart_money_signal(self, signal_data: dict):
        """发送Smart Money信号"""
        message = {
            'type': 'smart_money_signal',
            'data': signal_data,
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
        
        await self.broadcast(message, channel='smart_money')
    
    async def send_technical_signal(self, signal_data: dict):
        """发送技术信号"""
        message = {
            'type': 'technical_signal',
            'data': signal_data,
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
        
        await self.broadcast(message, channel='technical_signals')
    
    async def send_risk_alert(self, risk_data: dict):
        """发送风险预警"""
        message = {
            'type': 'risk_alert',
            'data': risk_data,
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
        
        await self.broadcast(message, channel='risk_alerts')
    
    async def send_system_notification(self, notification: dict):
        """发送系统通知"""
        message = {
            'type': 'system_notification',
            'data': notification,
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
        
        await self.broadcast(message)
    
    def get_connection_stats(self) -> dict:
        """获取连接统计"""
        return {
            'total_connections': len(self.active_connections),
            'active_connections': len([
                conn for conn in self.active_connections 
                if conn.client_state == WebSocketState.CONNECTED
            ]),
            'subscription_stats': {
                channel: len([
                    conn for conn, subs in self.subscriptions.items()
                    if channel in subs
                ])
                for channel in ['prices', 'whale_alerts', 'smart_money', 'technical_signals', 'risk_alerts']
            }
        }


class RealtimeDataBroadcaster:
    """实时数据广播器"""
    
    def __init__(self, websocket_manager: WebSocketManager):
        self.websocket_manager = websocket_manager
        self.is_running = False
        self.broadcast_tasks = []
    
    async def start(self):
        """启动广播器"""
        if self.is_running:
            return
        
        self.is_running = True
        logger.info("Starting realtime data broadcaster")
        
        # 启动各种数据广播任务
        self.broadcast_tasks = [
            asyncio.create_task(self._broadcast_price_updates()),
            asyncio.create_task(self._broadcast_market_stats()),
            asyncio.create_task(self._broadcast_system_health())
        ]
    
    async def stop(self):
        """停止广播器"""
        if not self.is_running:
            return
        
        self.is_running = False
        logger.info("Stopping realtime data broadcaster")
        
        # 取消所有广播任务
        for task in self.broadcast_tasks:
            task.cancel()
        
        # 等待任务完成
        await asyncio.gather(*self.broadcast_tasks, return_exceptions=True)
        self.broadcast_tasks.clear()
    
    async def _broadcast_price_updates(self):
        """广播价格更新"""
        try:
            while self.is_running:
                # 模拟价格数据
                import random
                
                price_data = {
                    'BTC': 50000 + random.uniform(-1000, 1000),
                    'ETH': 3000 + random.uniform(-200, 200),
                    'ADA': 1.0 + random.uniform(-0.1, 0.1),
                    'DOT': 10.0 + random.uniform(-1, 1)
                }
                
                await self.websocket_manager.send_price_update(price_data)
                await asyncio.sleep(5)  # 每5秒更新一次价格
        
        except asyncio.CancelledError:
            pass
        except Exception as e:
            logger.error(f"Price broadcast error: {e}")
    
    async def _broadcast_market_stats(self):
        """广播市场统计"""
        try:
            while self.is_running:
                # 模拟市场统计数据
                import random
                
                stats = {
                    'total_market_cap': random.uniform(2e12, 3e12),
                    'btc_dominance': random.uniform(40, 50),
                    'fear_greed_index': random.randint(20, 80),
                    'active_addresses': random.randint(800000, 1200000)
                }
                
                message = {
                    'type': 'market_stats',
                    'data': stats,
                    'timestamp': datetime.now(timezone.utc).isoformat()
                }
                
                await self.websocket_manager.broadcast(message, channel='market_stats')
                await asyncio.sleep(30)  # 每30秒更新一次统计
        
        except asyncio.CancelledError:
            pass
        except Exception as e:
            logger.error(f"Market stats broadcast error: {e}")
    
    async def _broadcast_system_health(self):
        """广播系统健康状态"""
        try:
            while self.is_running:
                # 系统健康检查
                health_data = {
                    'status': 'healthy',
                    'uptime': '24h 15m',
                    'memory_usage': '65%',
                    'cpu_usage': '45%',
                    'active_connections': len(self.websocket_manager.active_connections)
                }
                
                message = {
                    'type': 'system_health',
                    'data': health_data,
                    'timestamp': datetime.now(timezone.utc).isoformat()
                }
                
                await self.websocket_manager.broadcast(message, channel='system_health')
                await asyncio.sleep(60)  # 每分钟更新一次健康状态
        
        except asyncio.CancelledError:
            pass
        except Exception as e:
            logger.error(f"System health broadcast error: {e}")


# 全局WebSocket管理器实例
websocket_manager = WebSocketManager()
realtime_broadcaster = RealtimeDataBroadcaster(websocket_manager)
