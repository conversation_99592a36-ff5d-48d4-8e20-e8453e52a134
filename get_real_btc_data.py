"""
获取真实BTC数据并进行分析
2025年7月26日
"""
import requests
import json
from datetime import datetime

def get_btc_data():
    """获取真实BTC数据"""
    print("🔍 正在获取真实BTC数据...")
    
    try:
        # 使用CoinGecko免费API
        url = "https://api.coingecko.com/api/v3/simple/price"
        params = {
            'ids': 'bitcoin',
            'vs_currencies': 'usd',
            'include_24hr_change': 'true',
            'include_24hr_vol': 'true',
            'include_market_cap': 'true',
            'include_last_updated_at': 'true'
        }
        
        print("📡 连接CoinGecko API...")
        response = requests.get(url, params=params, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            btc_data = data['bitcoin']
            
            print("✅ 数据获取成功！")
            return btc_data
        else:
            print(f"❌ API请求失败，状态码: {response.status_code}")
            return None
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时，请检查网络连接")
        return None
    except requests.exceptions.ConnectionError:
        print("❌ 网络连接错误")
        return None
    except Exception as e:
        print(f"❌ 获取数据时发生错误: {e}")
        return None

def get_btc_historical_data():
    """获取BTC历史数据"""
    try:
        print("📈 获取历史价格数据...")
        url = "https://api.coingecko.com/api/v3/coins/bitcoin/market_chart"
        params = {
            'vs_currency': 'usd',
            'days': '30',
            'interval': 'daily'
        }
        
        response = requests.get(url, params=params, timeout=15)
        
        if response.status_code == 200:
            data = response.json()
            prices = data['prices']
            
            # 提取最近30天的价格
            recent_prices = [price[1] for price in prices[-30:]]
            
            print("✅ 历史数据获取成功！")
            return recent_prices
        else:
            print(f"❌ 历史数据获取失败，状态码: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 获取历史数据时发生错误: {e}")
        return None

def calculate_technical_indicators(prices):
    """计算技术指标"""
    if not prices or len(prices) < 20:
        return None
    
    current_price = prices[-1]
    
    # 计算移动平均线
    sma_7 = sum(prices[-7:]) / 7 if len(prices) >= 7 else current_price
    sma_20 = sum(prices[-20:]) / 20 if len(prices) >= 20 else current_price
    sma_30 = sum(prices[-30:]) / 30 if len(prices) >= 30 else current_price
    
    # 计算简化RSI
    if len(prices) >= 14:
        gains = []
        losses = []
        for i in range(1, 15):
            change = prices[-i] - prices[-i-1]
            if change > 0:
                gains.append(change)
                losses.append(0)
            else:
                gains.append(0)
                losses.append(abs(change))
        
        avg_gain = sum(gains) / 14
        avg_loss = sum(losses) / 14
        
        if avg_loss == 0:
            rsi = 100
        else:
            rs = avg_gain / avg_loss
            rsi = 100 - (100 / (1 + rs))
    else:
        rsi = 50
    
    # 计算波动率
    if len(prices) >= 7:
        returns = []
        for i in range(1, 8):
            returns.append((prices[-i] - prices[-i-1]) / prices[-i-1])
        volatility = (sum(r**2 for r in returns) / len(returns)) ** 0.5
    else:
        volatility = 0
    
    return {
        'current_price': current_price,
        'sma_7': sma_7,
        'sma_20': sma_20,
        'sma_30': sma_30,
        'rsi': rsi,
        'volatility_7d': volatility
    }

def analyze_real_btc():
    """分析真实BTC数据"""
    print("🚀 OnChain Analytics Platform - 真实BTC数据分析")
    print("=" * 70)
    print(f"📅 分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🌐 数据来源: CoinGecko API (实时数据)")
    
    # 获取实时数据
    btc_data = get_btc_data()
    if not btc_data:
        print("❌ 无法获取实时数据，分析终止")
        return
    
    # 获取历史数据
    historical_prices = get_btc_historical_data()
    
    # 显示基础数据
    print(f"\n📊 实时市场数据")
    print("-" * 40)
    print(f"当前价格: ${btc_data['usd']:,.2f}")
    print(f"24小时变化: {btc_data['usd_24h_change']:+.2f}%")
    print(f"24小时成交量: ${btc_data['usd_24h_vol']:,.0f}")
    print(f"市值: ${btc_data['usd_market_cap']:,.0f}")
    
    # 技术分析
    if historical_prices:
        tech_indicators = calculate_technical_indicators(historical_prices)
        
        if tech_indicators:
            print(f"\n📈 技术指标分析")
            print("-" * 40)
            print(f"7日均线: ${tech_indicators['sma_7']:,.2f}")
            print(f"20日均线: ${tech_indicators['sma_20']:,.2f}")
            print(f"30日均线: ${tech_indicators['sma_30']:,.2f}")
            print(f"RSI(14): {tech_indicators['rsi']:.1f}")
            print(f"7日波动率: {tech_indicators['volatility_7d']:.2%}")
            
            # 技术信号分析
            current_price = btc_data['usd']
            signals = []
            score = 0
            
            if current_price > tech_indicators['sma_7']:
                signals.append("✅ 价格在7日均线上方")
                score += 1
            else:
                signals.append("❌ 价格在7日均线下方")
            
            if current_price > tech_indicators['sma_20']:
                signals.append("✅ 价格在20日均线上方")
                score += 1
            else:
                signals.append("❌ 价格在20日均线下方")
            
            if btc_data['usd_24h_change'] > 0:
                signals.append("✅ 24小时上涨")
                score += 1
            else:
                signals.append("❌ 24小时下跌")
            
            if 30 < tech_indicators['rsi'] < 70:
                signals.append("✅ RSI处于健康区间")
                score += 1
            elif tech_indicators['rsi'] > 70:
                signals.append("⚠️ RSI超买")
            else:
                signals.append("⚠️ RSI超卖")
            
            print(f"\n🎯 技术信号分析")
            print("-" * 40)
            for signal in signals:
                print(f"  {signal}")
            
            # 生成建议
            print(f"\n💡 基于真实数据的投资建议")
            print("-" * 40)
            print(f"技术评分: {score}/4")
            
            if score >= 3:
                recommendation = "🟢 看涨"
                action = "可考虑买入"
                confidence = "较高"
            elif score >= 2:
                recommendation = "🟡 中性"
                action = "观望为主"
                confidence = "中等"
            else:
                recommendation = "🔴 看跌"
                action = "谨慎操作"
                confidence = "较低"
            
            print(f"市场判断: {recommendation}")
            print(f"操作建议: {action}")
            print(f"信号置信度: {confidence}")
            
            # 具体操作建议
            if score >= 3:
                stop_loss = current_price * 0.95
                take_profit = current_price * 1.10
                print(f"\n📋 具体操作参考:")
                print(f"  建议入场: ${current_price:,.2f}")
                print(f"  止损位: ${stop_loss:,.2f} (-5%)")
                print(f"  止盈位: ${take_profit:,.2f} (+10%)")
                print(f"  建议仓位: 不超过总资金的10%")
    
    # 重要声明
    print(f"\n⚠️ 重要声明")
    print("-" * 40)
    print("• 本分析基于有限的技术指标和公开数据")
    print("• 缺少Smart Money和巨鲸等高级数据")
    print("• 加密货币投资风险极高")
    print("• 请根据自身情况谨慎决策")
    print("• 本分析仅供参考，不构成投资建议")
    
    print(f"\n✅ 基于真实数据的BTC分析完成")
    print(f"📊 数据更新时间: {datetime.fromtimestamp(btc_data['last_updated_at']).strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    analyze_real_btc()
