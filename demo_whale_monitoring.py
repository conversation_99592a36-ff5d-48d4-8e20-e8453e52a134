"""
巨鲸监控系统演示脚本
演示巨鲸检测、交易监控和预警功能
"""
import asyncio
import sys
from pathlib import Path
from datetime import datetime, timezone

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.monitoring.whale_detector import WhaleDetector, WhaleType
from src.monitoring.whale_monitor import WhaleMonitor, AlertLevel
from src.monitoring.alert_system import AlertSystem, NotificationChannel
from src.utils.logger import get_logger

logger = get_logger(__name__)


async def demo_whale_detection():
    """演示巨鲸检测功能"""
    print("\n=== 巨鲸检测演示 ===")
    
    try:
        async with WhaleDetector() as detector:
            print("--- 扫描巨鲸地址 ---")
            
            # 扫描最近24小时的巨鲸活动
            whale_profiles = await detector.scan_for_whales(hours=24)
            
            print(f"检测到 {len(whale_profiles)} 个巨鲸地址")
            
            # 显示前5个最有影响力的巨鲸
            for i, profile in enumerate(whale_profiles[:5], 1):
                print(f"\n{i}. 巨鲸地址: {profile.address}")
                print(f"   类型: {profile.whale_type.value}")
                print(f"   余额: {profile.balance_eth:.2f} ETH (${profile.balance_usd:,.0f})")
                print(f"   总交易量: ${profile.total_volume_usd:,.0f}")
                print(f"   交易数量: {profile.transaction_count:,}")
                print(f"   风险分数: {profile.risk_score:.3f}")
                print(f"   影响力分数: {profile.influence_score:.3f}")
                print(f"   首次发现: {profile.first_seen.strftime('%Y-%m-%d %H:%M')}")
                print(f"   最后活跃: {profile.last_active.strftime('%Y-%m-%d %H:%M')}")
                print(f"   标签: {', '.join(profile.labels)}")
                
                # 显示交易模式
                pattern = profile.trading_pattern
                if pattern:
                    print(f"   交易模式:")
                    print(f"     平均交易规模: ${pattern.get('avg_transaction_size', 0):,.0f}")
                    print(f"     交易频率: {pattern.get('transaction_frequency', 0):.2f} 次/天")
                    print(f"     代币多样性: {pattern.get('token_diversity', 0)} 种")
                    print(f"     Gas使用模式: {pattern.get('gas_usage_pattern', 'normal')}")
                
                # 显示关联地址
                if profile.associated_addresses:
                    print(f"   关联地址: {len(profile.associated_addresses)} 个")
                    for addr in profile.associated_addresses[:3]:
                        print(f"     {addr}")
            
            # 按类型统计巨鲸
            print(f"\n--- 巨鲸类型分布 ---")
            type_counts = {}
            for profile in whale_profiles:
                whale_type = profile.whale_type.value
                type_counts[whale_type] = type_counts.get(whale_type, 0) + 1
            
            for whale_type, count in sorted(type_counts.items(), key=lambda x: x[1], reverse=True):
                print(f"{whale_type}: {count} 个")
    
    except Exception as e:
        print(f"巨鲸检测演示失败: {e}")


async def demo_whale_transaction_monitoring():
    """演示巨鲸交易监控"""
    print("\n=== 巨鲸交易监控演示 ===")
    
    try:
        async with WhaleMonitor() as monitor:
            print("--- 监控巨鲸交易 ---")
            
            # 监控最近1小时的巨鲸交易
            whale_transactions = await monitor.monitor_whale_transactions(hours=1)
            
            print(f"检测到 {len(whale_transactions)} 笔巨鲸交易")
            
            # 显示交易详情
            for i, tx in enumerate(whale_transactions[:10], 1):
                print(f"\n{i}. 交易哈希: {tx.tx_hash}")
                print(f"   金额: ${tx.value_usd:,.0f} {tx.token_symbol}")
                print(f"   从: {tx.from_address}")
                print(f"   到: {tx.to_address}")
                print(f"   类型: {tx.transaction_type}")
                print(f"   预警级别: {tx.alert_level.value}")
                print(f"   影响分数: {tx.impact_score:.3f}")
                print(f"   时间: {tx.timestamp.strftime('%Y-%m-%d %H:%M:%S')}")
                
                # 显示市场背景
                context = tx.market_context
                if context:
                    print(f"   市场背景:")
                    print(f"     市场状态: {context.get('market_hours', 'unknown')}")
                    print(f"     星期: {context.get('day_of_week', 'unknown')}")
                    print(f"     Gas价格: {context.get('gas_price_level', 'unknown')}")
            
            # 按预警级别统计
            print(f"\n--- 预警级别分布 ---")
            level_counts = {}
            for tx in whale_transactions:
                level = tx.alert_level.value
                level_counts[level] = level_counts.get(level, 0) + 1
            
            for level, count in sorted(level_counts.items()):
                print(f"{level}: {count} 笔")
            
            # 按交易类型统计
            print(f"\n--- 交易类型分布 ---")
            type_counts = {}
            for tx in whale_transactions:
                tx_type = tx.transaction_type
                type_counts[tx_type] = type_counts.get(tx_type, 0) + 1
            
            for tx_type, count in sorted(type_counts.items(), key=lambda x: x[1], reverse=True):
                print(f"{tx_type}: {count} 笔")
    
    except Exception as e:
        print(f"巨鲸交易监控演示失败: {e}")


async def demo_fund_flow_analysis():
    """演示资金流向分析"""
    print("\n=== 资金流向分析演示 ===")
    
    try:
        async with WhaleMonitor() as monitor:
            print("--- 分析资金流向 ---")
            
            # 分析最近24小时的资金流向
            flow_analyses = await monitor.analyze_fund_flows(hours=24)
            
            print(f"识别到 {len(flow_analyses)} 种资金流向模式")
            
            for i, flow in enumerate(flow_analyses, 1):
                print(f"\n{i}. 流向: {flow.direction}")
                print(f"   来源类型: {flow.source_type}")
                print(f"   目标类型: {flow.destination_type}")
                print(f"   总金额: ${flow.amount_usd:,.0f}")
                print(f"   影响分数: {flow.impact_score:.3f}")
                
                if flow.market_implications:
                    print(f"   市场含义:")
                    for implication in flow.market_implications:
                        print(f"     • {implication}")
            
            # 计算总体资金流向
            print(f"\n--- 总体资金流向摘要 ---")
            total_inflow = sum(flow.amount_usd for flow in flow_analyses if flow.direction == 'inflow')
            total_outflow = sum(flow.amount_usd for flow in flow_analyses if flow.direction == 'outflow')
            net_flow = total_inflow - total_outflow
            
            print(f"交易所流入: ${total_inflow:,.0f}")
            print(f"交易所流出: ${total_outflow:,.0f}")
            print(f"净流向: ${net_flow:,.0f} ({'流入' if net_flow > 0 else '流出'})")
            
            if abs(net_flow) > 50000000:  # 5000万美元
                if net_flow > 0:
                    print("⚠️  大量资金流入交易所，可能面临抛售压力")
                else:
                    print("📈 大量资金流出交易所，可能表明长期持有意图")
    
    except Exception as e:
        print(f"资金流向分析演示失败: {e}")


async def demo_alert_system():
    """演示预警系统"""
    print("\n=== 预警系统演示 ===")
    
    try:
        alert_system = AlertSystem()
        
        print("--- 处理巨鲸预警 ---")
        
        # 处理最近1小时的预警
        notifications = await alert_system.process_whale_alerts(hours=1)
        
        print(f"生成了 {len(notifications)} 条通知")
        
        # 显示通知详情
        for i, notification in enumerate(notifications[:5], 1):
            print(f"\n{i}. 通知标题: {notification.title}")
            print(f"   渠道: {notification.channel.value}")
            print(f"   预警级别: {notification.alert_level.value}")
            print(f"   时间: {notification.timestamp.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"   内容预览:")
            # 显示内容的前200个字符
            content_preview = notification.content.replace('\n', ' ')[:200]
            print(f"     {content_preview}...")
        
        # 按渠道统计通知
        print(f"\n--- 通知渠道分布 ---")
        channel_counts = {}
        for notification in notifications:
            channel = notification.channel.value
            channel_counts[channel] = channel_counts.get(channel, 0) + 1
        
        for channel, count in sorted(channel_counts.items()):
            print(f"{channel}: {count} 条")
        
        # 获取预警统计
        print(f"\n--- 预警统计 (过去7天) ---")
        stats = await alert_system.get_alert_statistics(days=7)
        
        print(f"总预警数: {stats.get('total_alerts', 0):,}")
        print(f"平均金额: ${stats.get('avg_amount_usd', 0):,.0f}")
        print(f"总金额: ${stats.get('total_amount_usd', 0):,.0f}")
        print(f"已处理: {stats.get('processed_alerts', 0):,}")
        print(f"已发送通知: {stats.get('notification_sent', 0):,}")
        
        # 按级别统计
        if 'by_level' in stats:
            print(f"\n按预警级别:")
            for level, count in sorted(stats['by_level'].items()):
                print(f"  {level}: {count:,}")
        
        # 按类型统计
        if 'by_type' in stats:
            print(f"\n按预警类型:")
            for alert_type, count in sorted(stats['by_type'].items(), key=lambda x: x[1], reverse=True):
                print(f"  {alert_type}: {count:,}")
    
    except Exception as e:
        print(f"预警系统演示失败: {e}")


async def demo_monitoring_rules():
    """演示监控规则"""
    print("\n=== 监控规则演示 ===")
    
    try:
        async with WhaleMonitor() as monitor:
            print("--- 当前监控规则 ---")
            
            for i, rule in enumerate(monitor.monitoring_rules, 1):
                print(f"\n{i}. 规则名称: {rule.name}")
                print(f"   描述: {rule.description}")
                print(f"   最小金额: ${rule.min_amount_usd:,.0f}")
                print(f"   预警级别: {rule.alert_level.value}")
                print(f"   状态: {'启用' if rule.enabled else '禁用'}")
                print(f"   条件:")
                for key, value in rule.conditions.items():
                    print(f"     {key}: {value}")
        
        alert_system = AlertSystem()
        print(f"\n--- 预警规则 ---")
        
        for i, rule in enumerate(alert_system.alert_rules, 1):
            print(f"\n{i}. 规则名称: {rule.name}")
            print(f"   描述: {rule.description}")
            print(f"   通知渠道: {[ch.value for ch in rule.channels]}")
            print(f"   冷却时间: {rule.cooldown_minutes} 分钟")
            print(f"   状态: {'启用' if rule.enabled else '禁用'}")
            print(f"   条件:")
            for key, value in rule.conditions.items():
                print(f"     {key}: {value}")
    
    except Exception as e:
        print(f"监控规则演示失败: {e}")


async def main():
    """主演示函数"""
    print("🐋 巨鲸行为监控系统演示")
    print("=" * 50)
    
    # 检查配置
    try:
        from config.settings import MONITORING_CONFIG, NOTIFICATION_CONFIG
        print("✅ 配置文件加载成功")
        
        # 检查巨鲸监控配置
        whale_config = MONITORING_CONFIG.get('whale_threshold', {})
        print(f"巨鲸监控配置:")
        print(f"  ETH阈值: {whale_config.get('eth', 'N/A')} ETH")
        print(f"  USDT阈值: ${whale_config.get('usdt', 'N/A'):,}")
        
        # 检查通知配置
        notification_config = NOTIFICATION_CONFIG
        print(f"通知配置:")
        print(f"  Telegram: {'已配置' if notification_config.get('telegram', {}).get('enabled') else '未配置'}")
        print(f"  Discord: {'已配置' if notification_config.get('discord', {}).get('enabled') else '未配置'}")
        print(f"  Email: {'已配置' if notification_config.get('email', {}).get('enabled') else '未配置'}")
    
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        print("某些功能可能无法正常工作")
    
    # 运行演示
    demos = [
        ("巨鲸检测", demo_whale_detection),
        ("巨鲸交易监控", demo_whale_transaction_monitoring),
        ("资金流向分析", demo_fund_flow_analysis),
        ("预警系统", demo_alert_system),
        ("监控规则", demo_monitoring_rules),
    ]
    
    for demo_name, demo_func in demos:
        try:
            print(f"\n🔄 开始 {demo_name} 演示...")
            await demo_func()
            print(f"✅ {demo_name} 演示完成")
        except Exception as e:
            print(f"❌ {demo_name} 演示失败: {e}")
            logger.error(f"{demo_name} demo failed", exc_info=True)
        
        # 在演示之间添加延迟
        await asyncio.sleep(1)
    
    print("\n🎉 所有演示完成!")
    print("=" * 50)
    print("\n💡 提示:")
    print("- 巨鲸监控需要实时数据支持")
    print("- 建议配置多种通知渠道以确保及时收到预警")
    print("- 可以根据需要调整监控规则和阈值")
    print("- 注意区分正常的机构交易和异常活动")


if __name__ == "__main__":
    # 运行演示
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 演示被用户中断")
    except Exception as e:
        print(f"\n💥 演示过程中发生错误: {e}")
        logger.error("Demo failed", exc_info=True)
