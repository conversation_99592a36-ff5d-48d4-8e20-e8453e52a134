"""
数据管理演示脚本
演示数据存储、查询和管理功能
"""
import asyncio
import sys
from pathlib import Path
from datetime import datetime, timezone

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.database.data_manager import DataManager
from src.database.repositories import AddressRepository, TransactionRepository, MarketDataRepository
from src.utils.logger import get_logger

logger = get_logger(__name__)


async def demo_address_management():
    """演示地址管理功能"""
    print("\n=== 地址管理演示 ===")
    
    try:
        # 创建地址仓库实例
        address_repo = AddressRepository()
        
        # 演示地址列表
        demo_addresses = [
            {
                'address': '******************************************',
                'label': 'Vitalik Buterin',
                'address_type': 'wallet',
                'is_smart_money': True,
                'total_transactions': 1500,
                'total_volume_usd': 50000000,
                'roi_percentage': 1000.0,
                'tags': ['founder', 'ethereum', 'vitalik']
            },
            {
                'address': '******************************************',
                'label': 'Binance Hot Wallet',
                'address_type': 'exchange',
                'is_whale': True,
                'total_transactions': 50000,
                'total_volume_usd': 1000000000,
                'tags': ['exchange', 'binance', 'hot_wallet']
            }
        ]
        
        print("--- 创建演示地址记录 ---")
        for addr_data in demo_addresses:
            try:
                # 检查地址是否已存在
                existing = address_repo.get_by_address(addr_data['address'])
                if existing:
                    print(f"地址已存在: {addr_data['label']} ({addr_data['address']})")
                else:
                    # 创建新地址记录
                    address_record = address_repo.create(**addr_data)
                    print(f"创建地址记录: {addr_data['label']} (ID: {address_record.id})")
            except Exception as e:
                print(f"创建地址记录失败 {addr_data['address']}: {e}")
        
        print("\n--- 查询Smart Money地址 ---")
        try:
            smart_money_addresses = address_repo.get_smart_money_addresses(limit=10)
            print(f"找到 {len(smart_money_addresses)} 个Smart Money地址:")
            for addr in smart_money_addresses:
                print(f"  {addr.label or 'Unknown'}: {addr.address}")
                print(f"    ROI: {addr.roi_percentage or 0:.2f}%")
                print(f"    交易数: {addr.total_transactions or 0}")
        except Exception as e:
            print(f"查询Smart Money地址失败: {e}")
        
        print("\n--- 查询巨鲸地址 ---")
        try:
            whale_addresses = address_repo.get_whale_addresses(limit=10)
            print(f"找到 {len(whale_addresses)} 个巨鲸地址:")
            for addr in whale_addresses:
                print(f"  {addr.label or 'Unknown'}: {addr.address}")
                print(f"    交易量: ${addr.total_volume_usd or 0:,.0f}")
                print(f"    交易数: {addr.total_transactions or 0}")
        except Exception as e:
            print(f"查询巨鲸地址失败: {e}")
        
        print("\n--- 搜索地址 ---")
        try:
            search_results = address_repo.search_addresses("vitalik", limit=5)
            print(f"搜索 'vitalik' 找到 {len(search_results)} 个结果:")
            for addr in search_results:
                print(f"  {addr.label or 'Unknown'}: {addr.address}")
        except Exception as e:
            print(f"搜索地址失败: {e}")
    
    except Exception as e:
        print(f"地址管理演示失败: {e}")


async def demo_transaction_analysis():
    """演示交易分析功能"""
    print("\n=== 交易分析演示 ===")
    
    try:
        transaction_repo = TransactionRepository()
        
        print("--- 获取交易统计 ---")
        try:
            stats = transaction_repo.get_transaction_stats(hours=24)
            print(f"过去24小时交易统计:")
            print(f"  总交易数: {stats['total_transactions']:,}")
            print(f"  总交易量: ${stats['total_volume_usd']:,.2f}")
            print(f"  平均交易价值: ${stats['average_value_usd']:,.2f}")
            print(f"  巨鲸交易数: {stats['whale_transactions']:,}")
            print(f"  Smart Money交易数: {stats['smart_money_transactions']:,}")
        except Exception as e:
            print(f"获取交易统计失败: {e}")
        
        print("\n--- 获取大额交易 ---")
        try:
            large_transactions = transaction_repo.get_large_transactions(
                min_value_usd=100000, hours=24, limit=5
            )
            print(f"过去24小时大额交易 (>$100k):")
            for tx in large_transactions:
                print(f"  {tx.tx_hash[:10]}...")
                print(f"    金额: ${tx.value_usd:,.2f}")
                print(f"    时间: {tx.block_timestamp}")
                print(f"    从: {tx.from_address[:10]}...")
                print(f"    到: {tx.to_address[:10]}..." if tx.to_address else "    到: Contract Creation")
        except Exception as e:
            print(f"获取大额交易失败: {e}")
        
        print("\n--- 获取巨鲸交易 ---")
        try:
            whale_transactions = transaction_repo.get_whale_transactions(hours=24, limit=5)
            print(f"过去24小时巨鲸交易:")
            for tx in whale_transactions:
                print(f"  {tx.tx_hash[:10]}...")
                print(f"    金额: ${tx.value_usd:,.2f}")
                print(f"    代币: {tx.token_symbol or 'ETH'}")
                print(f"    时间: {tx.block_timestamp}")
        except Exception as e:
            print(f"获取巨鲸交易失败: {e}")
    
    except Exception as e:
        print(f"交易分析演示失败: {e}")


async def demo_market_data_management():
    """演示市场数据管理"""
    print("\n=== 市场数据管理演示 ===")
    
    try:
        market_repo = MarketDataRepository()
        
        # 创建一些演示市场数据
        demo_market_data = [
            {
                'symbol': 'BTC',
                'timestamp': datetime.now(timezone.utc),
                'price_usd': 45000.0,
                'volume_24h': 25000000000,
                'market_cap': 850000000000,
                'price_change_24h': 2.5
            },
            {
                'symbol': 'ETH',
                'timestamp': datetime.now(timezone.utc),
                'price_usd': 3200.0,
                'volume_24h': 15000000000,
                'market_cap': 380000000000,
                'price_change_24h': 1.8
            }
        ]
        
        print("--- 批量插入市场数据 ---")
        try:
            inserted_count = market_repo.bulk_insert_market_data(demo_market_data)
            print(f"成功插入 {inserted_count} 条市场数据记录")
        except Exception as e:
            print(f"插入市场数据失败: {e}")
        
        print("\n--- 获取最新价格 ---")
        for symbol in ['BTC', 'ETH']:
            try:
                latest_price = market_repo.get_latest_price(symbol)
                if latest_price:
                    print(f"{symbol} 最新价格:")
                    print(f"  价格: ${latest_price.price_usd:,.2f}")
                    print(f"  24h变化: {latest_price.price_change_24h:+.2f}%")
                    print(f"  市值: ${latest_price.market_cap:,.0f}")
                    print(f"  更新时间: {latest_price.timestamp}")
                else:
                    print(f"{symbol} 暂无价格数据")
            except Exception as e:
                print(f"获取 {symbol} 价格失败: {e}")
        
        print("\n--- 获取价格历史 ---")
        try:
            btc_history = market_repo.get_price_history('BTC', days=7)
            print(f"BTC 过去7天价格历史: {len(btc_history)} 条记录")
            if btc_history:
                earliest = min(btc_history, key=lambda x: x.timestamp)
                latest = max(btc_history, key=lambda x: x.timestamp)
                print(f"  最早记录: {earliest.timestamp} - ${earliest.price_usd:,.2f}")
                print(f"  最新记录: {latest.timestamp} - ${latest.price_usd:,.2f}")
        except Exception as e:
            print(f"获取价格历史失败: {e}")
    
    except Exception as e:
        print(f"市场数据管理演示失败: {e}")


async def demo_data_manager():
    """演示数据管理器功能"""
    print("\n=== 数据管理器演示 ===")
    
    try:
        async with DataManager() as manager:
            print("--- 获取系统统计 ---")
            try:
                stats = await manager.get_system_stats()
                print("系统统计信息:")
                print(f"  数据库健康状态: {stats['database_health']}")
                print("  表记录统计:")
                for table, count in stats['table_stats'].items():
                    if count >= 0:
                        print(f"    {table}: {count:,} 条记录")
                    else:
                        print(f"    {table}: 查询失败")
                
                if 'recent_activity' in stats:
                    activity = stats['recent_activity']
                    print("  最近24小时活动:")
                    print(f"    新交易: {activity.get('recent_transactions', 0):,}")
                    print(f"    巨鲸预警: {activity.get('recent_whale_alerts', 0):,}")
                    print(f"    Smart Money活动: {activity.get('recent_smart_money_activities', 0):,}")
            except Exception as e:
                print(f"获取系统统计失败: {e}")
            
            print("\n--- 演示数据清理 ---")
            try:
                # 使用较短的保留期进行演示
                retention_policy = {
                    'transactions': 30,
                    'market_data': 90,
                    'whale_alerts': 7
                }
                
                cleanup_result = await manager.cleanup_old_data(retention_policy)
                print("数据清理结果:")
                print(f"  清理开始时间: {cleanup_result['cleanup_started']}")
                print(f"  清理完成时间: {cleanup_result['cleanup_completed']}")
                print(f"  总删除记录数: {cleanup_result['total_deleted_records']:,}")
                print("  各表清理情况:")
                for table, count in cleanup_result['tables_cleaned'].items():
                    print(f"    {table}: 删除 {count:,} 条记录")
            except Exception as e:
                print(f"数据清理演示失败: {e}")
            
            print("\n--- 演示数据导出 ---")
            try:
                export_config = {
                    'addresses': True,
                    'transactions': True,
                    'market_data': True,
                    'address_limit': 100,
                    'transaction_limit': 1000,
                    'transaction_days': 7,
                    'market_data_limit': 1000,
                    'market_data_days': 30
                }
                
                export_result = await manager.export_data(export_config)
                print("数据导出结果:")
                print(f"  导出目录: {export_result['export_dir']}")
                print(f"  导出文件数: {len(export_result['exported_files'])}")
                print("  导出文件详情:")
                for file_info in export_result['exported_files']:
                    print(f"    {file_info['file_path']}")
                    print(f"      记录数: {file_info['record_count']:,}")
                    print(f"      文件大小: {file_info['file_size_bytes']:,} 字节")
            except Exception as e:
                print(f"数据导出演示失败: {e}")
    
    except Exception as e:
        print(f"数据管理器演示失败: {e}")


async def main():
    """主演示函数"""
    print("🗄️  数据存储和管理系统演示")
    print("=" * 50)
    
    # 检查数据库连接
    try:
        from src.database.database import db_manager
        health = db_manager.health_check()
        if health['database']:
            print("✅ 数据库连接正常")
        else:
            print("❌ 数据库连接失败")
            print("请确保PostgreSQL服务正在运行并且配置正确")
            return
        
        if health['redis']:
            print("✅ Redis连接正常")
        else:
            print("⚠️  Redis连接失败（可选功能）")
    
    except Exception as e:
        print(f"❌ 数据库连接检查失败: {e}")
        print("请检查数据库配置和连接")
        return
    
    # 运行演示
    demos = [
        ("地址管理", demo_address_management),
        ("交易分析", demo_transaction_analysis),
        ("市场数据管理", demo_market_data_management),
        ("数据管理器", demo_data_manager),
    ]
    
    for demo_name, demo_func in demos:
        try:
            print(f"\n🔄 开始 {demo_name} 演示...")
            await demo_func()
            print(f"✅ {demo_name} 演示完成")
        except Exception as e:
            print(f"❌ {demo_name} 演示失败: {e}")
            logger.error(f"{demo_name} demo failed", exc_info=True)
        
        # 在演示之间添加延迟
        await asyncio.sleep(1)
    
    print("\n🎉 所有演示完成!")
    print("=" * 50)


if __name__ == "__main__":
    # 运行演示
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 演示被用户中断")
    except Exception as e:
        print(f"\n💥 演示过程中发生错误: {e}")
        logger.error("Demo failed", exc_info=True)
