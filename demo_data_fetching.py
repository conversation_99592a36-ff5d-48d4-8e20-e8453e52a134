"""
数据获取演示脚本
演示如何使用数据获取模块获取链上数据和市场数据
"""
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.data.data_coordinator import DataCoordinator
from src.utils.logger import get_logger

logger = get_logger(__name__)


async def demo_blockchain_data():
    """演示区块链数据获取"""
    print("\n=== 区块链数据获取演示 ===")
    
    # 使用一些知名地址进行演示
    demo_addresses = [
        '******************************************',  # Vitalik Buterin
        '******************************************',  # Binance Hot Wallet
    ]
    
    try:
        async with DataCoordinator() as coordinator:
            for address in demo_addresses:
                print(f"\n--- 地址: {address} ---")
                
                try:
                    # 获取余额
                    balance = await coordinator.get_address_balance(address)
                    print(f"余额: {balance['balance_eth']:.4f} ETH")
                    
                    # 获取最近的交易
                    transactions = await coordinator.get_address_transactions(address, limit=5)
                    print(f"最近交易数量: {len(transactions)}")
                    
                    if transactions:
                        latest_tx = transactions[0]
                        print(f"最新交易: {latest_tx['hash']}")
                        print(f"交易金额: {latest_tx['value_eth']:.4f} ETH")
                        print(f"交易时间: {latest_tx['timestamp']}")
                    
                    # 获取代币转账记录
                    token_transfers = await coordinator.get_token_transfers(address, limit=3)
                    print(f"最近代币转账数量: {len(token_transfers)}")
                    
                    if token_transfers:
                        latest_transfer = token_transfers[0]
                        print(f"最新代币转账: {latest_transfer['token_symbol']}")
                        print(f"转账数量: {latest_transfer['value_formatted']:.4f}")
                
                except Exception as e:
                    print(f"获取地址 {address} 数据时出错: {e}")
                
                # 添加延迟以避免API限制
                await asyncio.sleep(1)
    
    except Exception as e:
        print(f"区块链数据获取演示失败: {e}")


async def demo_market_data():
    """演示市场数据获取"""
    print("\n=== 市场数据获取演示 ===")
    
    # 演示代币列表
    demo_coins = ['bitcoin', 'ethereum', 'binancecoin', 'cardano', 'solana']
    
    try:
        async with DataCoordinator() as coordinator:
            # 获取价格数据
            print("\n--- 代币价格 ---")
            price_data = await coordinator.get_token_price(demo_coins)
            
            for coin_id, data in price_data.items():
                if 'usd' in data:
                    price = data['usd']
                    change_24h = data.get('usd_24h_change', 0)
                    market_cap = data.get('usd_market_cap', 0)
                    
                    print(f"{coin_id.upper()}:")
                    print(f"  价格: ${price:,.2f}")
                    print(f"  24h变化: {change_24h:+.2f}%")
                    if market_cap:
                        print(f"  市值: ${market_cap:,.0f}")
            
            # 获取市场概览
            print("\n--- 市场概览 ---")
            market_overview = await coordinator.get_market_overview(['bitcoin', 'ethereum'])
            
            if 'trending' in market_overview:
                trending = market_overview['trending']
                if 'coins' in trending and trending['coins']:
                    print("热门搜索代币:")
                    for i, coin in enumerate(trending['coins'][:5], 1):
                        coin_data = coin.get('item', {})
                        print(f"  {i}. {coin_data.get('name', 'N/A')} ({coin_data.get('symbol', 'N/A')})")
            
            if 'fear_greed_index' in market_overview:
                fgi_data = market_overview['fear_greed_index']
                if 'data' in fgi_data and fgi_data['data']:
                    fgi = fgi_data['data'][0]
                    print(f"\n恐惧贪婪指数: {fgi.get('value', 'N/A')} ({fgi.get('value_classification', 'N/A')})")
    
    except Exception as e:
        print(f"市场数据获取演示失败: {e}")


async def demo_defi_data():
    """演示DeFi数据获取"""
    print("\n=== DeFi数据获取演示 ===")
    
    try:
        async with DataCoordinator() as coordinator:
            # 获取DeFi协议列表
            print("\n--- 顶级DeFi协议 ---")
            protocols = await coordinator.get_defi_protocols()
            
            # 按TVL排序并显示前10个
            sorted_protocols = sorted(protocols, key=lambda x: x.get('tvl', 0), reverse=True)
            
            for i, protocol in enumerate(sorted_protocols[:10], 1):
                name = protocol.get('name', 'N/A')
                tvl = protocol.get('tvl', 0)
                category = protocol.get('category', 'N/A')
                chain = protocol.get('chain', 'N/A')
                
                print(f"{i:2d}. {name}")
                print(f"    TVL: ${tvl:,.0f}")
                print(f"    类别: {category}")
                print(f"    链: {chain}")
            
            # 获取收益机会
            print("\n--- 高收益机会 (前5个) ---")
            yields = await coordinator.get_yield_opportunities()
            
            # 按APY排序并过滤稳定币池
            stable_yields = [y for y in yields if y.get('stable_coin') and y.get('apy', 0) > 0]
            sorted_yields = sorted(stable_yields, key=lambda x: x.get('apy', 0), reverse=True)
            
            for i, pool in enumerate(sorted_yields[:5], 1):
                project = pool.get('project', 'N/A')
                symbol = pool.get('symbol', 'N/A')
                apy = pool.get('apy', 0)
                tvl = pool.get('tvl_usd', 0)
                chain = pool.get('chain', 'N/A')
                
                print(f"{i}. {project} - {symbol}")
                print(f"   APY: {apy:.2f}%")
                print(f"   TVL: ${tvl:,.0f}")
                print(f"   链: {chain}")
    
    except Exception as e:
        print(f"DeFi数据获取演示失败: {e}")


async def demo_smart_money_analysis():
    """演示Smart Money分析"""
    print("\n=== Smart Money分析演示 ===")
    
    # 一些知名的Smart Money地址
    smart_money_addresses = [
        '******************************************',  # Vitalik
        '0x742d35Cc6634C0532925a3b8D4C9db96C4b4d8b6',  # Random whale
    ]
    
    try:
        async with DataCoordinator() as coordinator:
            for address in smart_money_addresses:
                print(f"\n--- 分析地址: {address} ---")
                
                try:
                    # 获取最近的代币转账
                    token_transfers = await coordinator.get_token_transfers(address, limit=10)
                    
                    if token_transfers:
                        # 分析代币活动
                        token_activity = {}
                        for transfer in token_transfers:
                            token_symbol = transfer['token_symbol']
                            if token_symbol not in token_activity:
                                token_activity[token_symbol] = {
                                    'buy_count': 0,
                                    'sell_count': 0,
                                    'total_volume': 0
                                }
                            
                            # 简单判断买入/卖出（实际需要更复杂的逻辑）
                            if transfer['to_address'].lower() == address.lower():
                                token_activity[token_symbol]['buy_count'] += 1
                            else:
                                token_activity[token_symbol]['sell_count'] += 1
                            
                            token_activity[token_symbol]['total_volume'] += transfer['value_formatted']
                        
                        print("代币活动摘要:")
                        for token, activity in token_activity.items():
                            print(f"  {token}:")
                            print(f"    买入次数: {activity['buy_count']}")
                            print(f"    卖出次数: {activity['sell_count']}")
                            print(f"    总交易量: {activity['total_volume']:.4f}")
                    
                    else:
                        print("未找到代币转账记录")
                
                except Exception as e:
                    print(f"分析地址 {address} 时出错: {e}")
                
                await asyncio.sleep(1)
    
    except Exception as e:
        print(f"Smart Money分析演示失败: {e}")


async def main():
    """主演示函数"""
    print("🚀 链上数据分析系统演示")
    print("=" * 50)
    
    # 检查配置
    try:
        from config.settings import API_CONFIG
        print("✅ 配置文件加载成功")
        
        # 检查API密钥配置
        missing_keys = []
        for api_name, config in API_CONFIG.items():
            if not config.get('api_key'):
                missing_keys.append(api_name)
        
        if missing_keys:
            print(f"⚠️  警告: 以下API密钥未配置: {', '.join(missing_keys)}")
            print("   某些功能可能无法正常工作")
        else:
            print("✅ 所有API密钥已配置")
    
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        print("请检查配置文件和API密钥设置")
        return
    
    # 运行演示
    demos = [
        ("区块链数据", demo_blockchain_data),
        ("市场数据", demo_market_data),
        ("DeFi数据", demo_defi_data),
        ("Smart Money分析", demo_smart_money_analysis),
    ]
    
    for demo_name, demo_func in demos:
        try:
            print(f"\n🔄 开始 {demo_name} 演示...")
            await demo_func()
            print(f"✅ {demo_name} 演示完成")
        except Exception as e:
            print(f"❌ {demo_name} 演示失败: {e}")
            logger.error(f"{demo_name} demo failed", exc_info=True)
        
        # 在演示之间添加延迟
        await asyncio.sleep(2)
    
    print("\n🎉 所有演示完成!")
    print("=" * 50)


if __name__ == "__main__":
    # 运行演示
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 演示被用户中断")
    except Exception as e:
        print(f"\n💥 演示过程中发生错误: {e}")
        logger.error("Demo failed", exc_info=True)
