"""
回测性能分析模块
分析回测结果并生成详细的性能报告
"""
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timezone
import warnings
warnings.filterwarnings('ignore')

from src.utils.logger import get_logger

logger = get_logger(__name__)


class PerformanceAnalyzer:
    """性能分析器"""
    
    def __init__(self):
        self.results = {}
        self.benchmark_results = {}
    
    def analyze_backtest_results(self, results: Dict[str, Any], 
                                benchmark_results: Dict[str, Any] = None) -> Dict[str, Any]:
        """分析回测结果"""
        try:
            logger.info("Analyzing backtest results")
            
            self.results = results
            self.benchmark_results = benchmark_results or {}
            
            analysis = {
                'basic_metrics': self._calculate_basic_metrics(),
                'risk_metrics': self._calculate_risk_metrics(),
                'trade_analysis': self._analyze_trades(),
                'drawdown_analysis': self._analyze_drawdowns(),
                'monthly_returns': self._calculate_monthly_returns(),
                'benchmark_comparison': self._compare_with_benchmark(),
                'performance_attribution': self._analyze_performance_attribution()
            }
            
            logger.info("Backtest analysis completed")
            return analysis
        
        except Exception as e:
            logger.error(f"Failed to analyze backtest results: {e}")
            return {}
    
    def _calculate_basic_metrics(self) -> Dict[str, Any]:
        """计算基本指标"""
        try:
            equity_curve = self.results.get('equity_curve', [])
            if not equity_curve:
                return {}
            
            timestamps, values = zip(*equity_curve)
            equity_series = pd.Series(values, index=pd.to_datetime(timestamps))
            
            # 计算收益率
            returns = equity_series.pct_change().dropna()
            
            # 基本统计
            initial_value = equity_series.iloc[0]
            final_value = equity_series.iloc[-1]
            total_return = (final_value - initial_value) / initial_value
            
            # 年化收益率
            days = (equity_series.index[-1] - equity_series.index[0]).days
            annualized_return = (1 + total_return) ** (365 / days) - 1 if days > 0 else 0
            
            # 波动率
            volatility = returns.std() * np.sqrt(252)
            
            # 夏普比率
            risk_free_rate = 0.02  # 假设无风险利率2%
            sharpe_ratio = (annualized_return - risk_free_rate) / volatility if volatility > 0 else 0
            
            # 索提诺比率
            downside_returns = returns[returns < 0]
            downside_volatility = downside_returns.std() * np.sqrt(252)
            sortino_ratio = (annualized_return - risk_free_rate) / downside_volatility if downside_volatility > 0 else 0
            
            # 卡尔马比率
            max_drawdown = self._calculate_max_drawdown(equity_series)
            calmar_ratio = annualized_return / abs(max_drawdown) if max_drawdown != 0 else 0
            
            return {
                'initial_value': initial_value,
                'final_value': final_value,
                'total_return': total_return,
                'annualized_return': annualized_return,
                'volatility': volatility,
                'sharpe_ratio': sharpe_ratio,
                'sortino_ratio': sortino_ratio,
                'calmar_ratio': calmar_ratio,
                'max_drawdown': max_drawdown,
                'trading_days': len(equity_series),
                'total_days': days
            }
        
        except Exception as e:
            logger.error(f"Failed to calculate basic metrics: {e}")
            return {}
    
    def _calculate_risk_metrics(self) -> Dict[str, Any]:
        """计算风险指标"""
        try:
            equity_curve = self.results.get('equity_curve', [])
            if not equity_curve:
                return {}
            
            timestamps, values = zip(*equity_curve)
            equity_series = pd.Series(values, index=pd.to_datetime(timestamps))
            returns = equity_series.pct_change().dropna()
            
            # VaR (Value at Risk)
            var_95 = np.percentile(returns, 5)
            var_99 = np.percentile(returns, 1)
            
            # CVaR (Conditional Value at Risk)
            cvar_95 = returns[returns <= var_95].mean()
            cvar_99 = returns[returns <= var_99].mean()
            
            # 最大连续亏损天数
            negative_returns = returns < 0
            max_consecutive_losses = self._calculate_max_consecutive(negative_returns)
            
            # 最大连续盈利天数
            positive_returns = returns > 0
            max_consecutive_wins = self._calculate_max_consecutive(positive_returns)
            
            # Beta (相对于基准)
            beta = self._calculate_beta(returns)
            
            # 信息比率
            information_ratio = self._calculate_information_ratio(returns)
            
            return {
                'var_95': var_95,
                'var_99': var_99,
                'cvar_95': cvar_95,
                'cvar_99': cvar_99,
                'max_consecutive_losses': max_consecutive_losses,
                'max_consecutive_wins': max_consecutive_wins,
                'beta': beta,
                'information_ratio': information_ratio,
                'skewness': returns.skew(),
                'kurtosis': returns.kurtosis()
            }
        
        except Exception as e:
            logger.error(f"Failed to calculate risk metrics: {e}")
            return {}
    
    def _analyze_trades(self) -> Dict[str, Any]:
        """分析交易"""
        try:
            trades = self.results.get('trades', [])
            if not trades:
                return {}
            
            # 转换为DataFrame
            trades_df = pd.DataFrame(trades)
            
            # 基本统计
            total_trades = len(trades_df)
            winning_trades = len(trades_df[trades_df['pnl'] > 0])
            losing_trades = len(trades_df[trades_df['pnl'] < 0])
            
            win_rate = winning_trades / total_trades if total_trades > 0 else 0
            
            # 盈亏统计
            avg_win = trades_df[trades_df['pnl'] > 0]['pnl'].mean() if winning_trades > 0 else 0
            avg_loss = trades_df[trades_df['pnl'] < 0]['pnl'].mean() if losing_trades > 0 else 0
            
            profit_factor = abs(avg_win / avg_loss) if avg_loss != 0 else float('inf')
            
            # 最大盈利和亏损
            max_win = trades_df['pnl'].max()
            max_loss = trades_df['pnl'].min()
            
            # 平均持仓时间（简化计算）
            avg_holding_period = 1  # 天，简化处理
            
            # 按资产分析
            asset_performance = {}
            for symbol in trades_df['symbol'].unique():
                symbol_trades = trades_df[trades_df['symbol'] == symbol]
                asset_performance[symbol] = {
                    'total_trades': len(symbol_trades),
                    'win_rate': len(symbol_trades[symbol_trades['pnl'] > 0]) / len(symbol_trades),
                    'total_pnl': symbol_trades['pnl'].sum(),
                    'avg_pnl': symbol_trades['pnl'].mean()
                }
            
            return {
                'total_trades': total_trades,
                'winning_trades': winning_trades,
                'losing_trades': losing_trades,
                'win_rate': win_rate,
                'avg_win': avg_win,
                'avg_loss': avg_loss,
                'profit_factor': profit_factor,
                'max_win': max_win,
                'max_loss': max_loss,
                'avg_holding_period': avg_holding_period,
                'asset_performance': asset_performance
            }
        
        except Exception as e:
            logger.error(f"Failed to analyze trades: {e}")
            return {}
    
    def _analyze_drawdowns(self) -> Dict[str, Any]:
        """分析回撤"""
        try:
            equity_curve = self.results.get('equity_curve', [])
            if not equity_curve:
                return {}
            
            timestamps, values = zip(*equity_curve)
            equity_series = pd.Series(values, index=pd.to_datetime(timestamps))
            
            # 计算回撤
            peak = equity_series.expanding().max()
            drawdown = (equity_series - peak) / peak
            
            # 最大回撤
            max_drawdown = drawdown.min()
            max_drawdown_date = drawdown.idxmin()
            
            # 回撤持续时间
            drawdown_periods = []
            in_drawdown = False
            start_date = None
            
            for date, dd in drawdown.items():
                if dd < 0 and not in_drawdown:
                    in_drawdown = True
                    start_date = date
                elif dd >= 0 and in_drawdown:
                    in_drawdown = False
                    if start_date:
                        duration = (date - start_date).days
                        drawdown_periods.append(duration)
            
            avg_drawdown_duration = np.mean(drawdown_periods) if drawdown_periods else 0
            max_drawdown_duration = max(drawdown_periods) if drawdown_periods else 0
            
            # 回撤频率
            drawdown_frequency = len(drawdown_periods) / len(equity_series) * 252  # 年化
            
            return {
                'max_drawdown': max_drawdown,
                'max_drawdown_date': max_drawdown_date,
                'avg_drawdown_duration': avg_drawdown_duration,
                'max_drawdown_duration': max_drawdown_duration,
                'drawdown_frequency': drawdown_frequency,
                'total_drawdown_periods': len(drawdown_periods)
            }
        
        except Exception as e:
            logger.error(f"Failed to analyze drawdowns: {e}")
            return {}
    
    def _calculate_monthly_returns(self) -> Dict[str, Any]:
        """计算月度收益"""
        try:
            equity_curve = self.results.get('equity_curve', [])
            if not equity_curve:
                return {}
            
            timestamps, values = zip(*equity_curve)
            equity_series = pd.Series(values, index=pd.to_datetime(timestamps))
            
            # 重采样为月度数据
            monthly_equity = equity_series.resample('M').last()
            monthly_returns = monthly_equity.pct_change().dropna()
            
            # 月度统计
            monthly_stats = {
                'avg_monthly_return': monthly_returns.mean(),
                'monthly_volatility': monthly_returns.std(),
                'best_month': monthly_returns.max(),
                'worst_month': monthly_returns.min(),
                'positive_months': len(monthly_returns[monthly_returns > 0]),
                'negative_months': len(monthly_returns[monthly_returns < 0]),
                'monthly_win_rate': len(monthly_returns[monthly_returns > 0]) / len(monthly_returns)
            }
            
            # 月度收益分布
            monthly_distribution = {
                'returns': monthly_returns.tolist(),
                'dates': monthly_returns.index.strftime('%Y-%m').tolist()
            }
            
            return {
                'monthly_stats': monthly_stats,
                'monthly_distribution': monthly_distribution
            }
        
        except Exception as e:
            logger.error(f"Failed to calculate monthly returns: {e}")
            return {}
    
    def _compare_with_benchmark(self) -> Dict[str, Any]:
        """与基准比较"""
        try:
            if not self.benchmark_results:
                return {'no_benchmark': True}
            
            # 获取策略和基准的权益曲线
            strategy_curve = self.results.get('equity_curve', [])
            benchmark_curve = self.benchmark_results.get('equity_curve', [])
            
            if not strategy_curve or not benchmark_curve:
                return {'insufficient_data': True}
            
            # 转换为Series
            strategy_timestamps, strategy_values = zip(*strategy_curve)
            strategy_series = pd.Series(strategy_values, index=pd.to_datetime(strategy_timestamps))
            
            benchmark_timestamps, benchmark_values = zip(*benchmark_curve)
            benchmark_series = pd.Series(benchmark_values, index=pd.to_datetime(benchmark_timestamps))
            
            # 对齐时间序列
            aligned_data = pd.DataFrame({
                'strategy': strategy_series,
                'benchmark': benchmark_series
            }).dropna()
            
            # 计算超额收益
            strategy_returns = aligned_data['strategy'].pct_change().dropna()
            benchmark_returns = aligned_data['benchmark'].pct_change().dropna()
            excess_returns = strategy_returns - benchmark_returns
            
            # 比较指标
            strategy_total_return = (strategy_series.iloc[-1] - strategy_series.iloc[0]) / strategy_series.iloc[0]
            benchmark_total_return = (benchmark_series.iloc[-1] - benchmark_series.iloc[0]) / benchmark_series.iloc[0]
            
            alpha = excess_returns.mean() * 252  # 年化Alpha
            tracking_error = excess_returns.std() * np.sqrt(252)  # 跟踪误差
            information_ratio = alpha / tracking_error if tracking_error > 0 else 0
            
            return {
                'strategy_total_return': strategy_total_return,
                'benchmark_total_return': benchmark_total_return,
                'excess_return': strategy_total_return - benchmark_total_return,
                'alpha': alpha,
                'tracking_error': tracking_error,
                'information_ratio': information_ratio,
                'correlation': strategy_returns.corr(benchmark_returns)
            }
        
        except Exception as e:
            logger.error(f"Failed to compare with benchmark: {e}")
            return {}
    
    def _analyze_performance_attribution(self) -> Dict[str, Any]:
        """性能归因分析"""
        try:
            trades = self.results.get('trades', [])
            if not trades:
                return {}
            
            trades_df = pd.DataFrame(trades)
            
            # 按资产归因
            asset_contribution = {}
            total_pnl = trades_df['pnl'].sum()
            
            for symbol in trades_df['symbol'].unique():
                symbol_trades = trades_df[trades_df['symbol'] == symbol]
                symbol_pnl = symbol_trades['pnl'].sum()
                contribution = symbol_pnl / total_pnl if total_pnl != 0 else 0
                
                asset_contribution[symbol] = {
                    'pnl': symbol_pnl,
                    'contribution': contribution,
                    'trade_count': len(symbol_trades)
                }
            
            # 按时间归因（月度）
            trades_df['timestamp'] = pd.to_datetime(trades_df['timestamp'])
            trades_df['month'] = trades_df['timestamp'].dt.to_period('M')
            
            monthly_contribution = {}
            for month in trades_df['month'].unique():
                month_trades = trades_df[trades_df['month'] == month]
                month_pnl = month_trades['pnl'].sum()
                contribution = month_pnl / total_pnl if total_pnl != 0 else 0
                
                monthly_contribution[str(month)] = {
                    'pnl': month_pnl,
                    'contribution': contribution,
                    'trade_count': len(month_trades)
                }
            
            return {
                'asset_contribution': asset_contribution,
                'monthly_contribution': monthly_contribution,
                'total_pnl': total_pnl
            }
        
        except Exception as e:
            logger.error(f"Failed to analyze performance attribution: {e}")
            return {}
    
    def _calculate_max_drawdown(self, equity_series: pd.Series) -> float:
        """计算最大回撤"""
        peak = equity_series.expanding().max()
        drawdown = (equity_series - peak) / peak
        return drawdown.min()
    
    def _calculate_max_consecutive(self, boolean_series: pd.Series) -> int:
        """计算最大连续True的数量"""
        max_consecutive = 0
        current_consecutive = 0
        
        for value in boolean_series:
            if value:
                current_consecutive += 1
                max_consecutive = max(max_consecutive, current_consecutive)
            else:
                current_consecutive = 0
        
        return max_consecutive
    
    def _calculate_beta(self, returns: pd.Series) -> float:
        """计算Beta"""
        if not self.benchmark_results:
            return 1.0
        
        # 简化处理，返回1.0
        return 1.0
    
    def _calculate_information_ratio(self, returns: pd.Series) -> float:
        """计算信息比率"""
        if not self.benchmark_results:
            return 0.0
        
        # 简化处理，返回0.0
        return 0.0
    
    def generate_report(self, analysis: Dict[str, Any]) -> str:
        """生成文本报告"""
        try:
            report = []
            report.append("=" * 60)
            report.append("回测性能分析报告")
            report.append("=" * 60)
            
            # 基本指标
            basic_metrics = analysis.get('basic_metrics', {})
            if basic_metrics:
                report.append("\n📊 基本指标:")
                report.append(f"  总收益率: {basic_metrics.get('total_return', 0):.2%}")
                report.append(f"  年化收益率: {basic_metrics.get('annualized_return', 0):.2%}")
                report.append(f"  年化波动率: {basic_metrics.get('volatility', 0):.2%}")
                report.append(f"  夏普比率: {basic_metrics.get('sharpe_ratio', 0):.3f}")
                report.append(f"  最大回撤: {basic_metrics.get('max_drawdown', 0):.2%}")
            
            # 交易分析
            trade_analysis = analysis.get('trade_analysis', {})
            if trade_analysis:
                report.append("\n📈 交易分析:")
                report.append(f"  总交易数: {trade_analysis.get('total_trades', 0)}")
                report.append(f"  胜率: {trade_analysis.get('win_rate', 0):.2%}")
                report.append(f"  盈亏比: {trade_analysis.get('profit_factor', 0):.2f}")
                report.append(f"  平均盈利: ${trade_analysis.get('avg_win', 0):.2f}")
                report.append(f"  平均亏损: ${trade_analysis.get('avg_loss', 0):.2f}")
            
            # 风险指标
            risk_metrics = analysis.get('risk_metrics', {})
            if risk_metrics:
                report.append("\n⚠️ 风险指标:")
                report.append(f"  VaR (95%): {risk_metrics.get('var_95', 0):.2%}")
                report.append(f"  最大连续亏损: {risk_metrics.get('max_consecutive_losses', 0)} 天")
                report.append(f"  偏度: {risk_metrics.get('skewness', 0):.3f}")
                report.append(f"  峰度: {risk_metrics.get('kurtosis', 0):.3f}")
            
            # 基准比较
            benchmark_comparison = analysis.get('benchmark_comparison', {})
            if benchmark_comparison and not benchmark_comparison.get('no_benchmark'):
                report.append("\n📊 基准比较:")
                report.append(f"  超额收益: {benchmark_comparison.get('excess_return', 0):.2%}")
                report.append(f"  Alpha: {benchmark_comparison.get('alpha', 0):.2%}")
                report.append(f"  信息比率: {benchmark_comparison.get('information_ratio', 0):.3f}")
            
            report.append("\n" + "=" * 60)
            
            return "\n".join(report)
        
        except Exception as e:
            logger.error(f"Failed to generate report: {e}")
            return "报告生成失败"
