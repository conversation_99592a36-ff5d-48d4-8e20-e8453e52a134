"""
风险管理模块演示脚本
演示风险评估、仓位管理和止损止盈功能
"""
import asyncio
import sys
import numpy as np
from pathlib import Path
from datetime import datetime, timezone, timedelta

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.risk_management.risk_engine import RiskEngine, RiskLevel
from src.risk_management.position_manager import (
    PositionManager, PositionSizeConfig, PositionSizeMethod
)
from src.risk_management.stop_loss_manager import (
    StopLossManager, StopLossConfig, TakeProfitConfig,
    StopLossType, TakeProfitType
)
from src.utils.logger import get_logger

logger = get_logger(__name__)


async def demo_risk_assessment():
    """演示风险评估功能"""
    print("\n=== 风险评估演示 ===")
    
    try:
        print("--- 创建风险评估引擎 ---")
        risk_engine = RiskEngine()
        
        print(f"✅ 风险引擎创建成功")
        print(f"风险参数配置:")
        for param_type, thresholds in risk_engine.risk_params.items():
            print(f"  {param_type}:")
            for level, value in thresholds.items():
                print(f"    {level}: {value}")
        
        print(f"\n--- 单资产风险评估 ---")
        
        # 评估多个资产的风险
        symbols = ['BTC', 'ETH', 'ADA', 'DOT']
        
        for symbol in symbols:
            try:
                print(f"\n{symbol} 风险评估:")
                
                # 由于需要真实数据，这里使用模拟评估
                risk_metrics = await risk_engine.assess_asset_risk(symbol, days=30)
                
                print(f"  风险等级: {risk_metrics.risk_level.value}")
                print(f"  综合风险评分: {risk_metrics.overall_risk_score:.3f}")
                print(f"  年化波动率: {risk_metrics.volatility:.2%}")
                print(f"  VaR (95%): {risk_metrics.var_95:.2%}")
                print(f"  CVaR (95%): {risk_metrics.cvar_95:.2%}")
                print(f"  流动性评分: {risk_metrics.liquidity_score:.3f}")
                print(f"  信用评分: {risk_metrics.credit_score:.3f}")
                print(f"  交易对手风险: {risk_metrics.counterparty_risk:.3f}")
                print(f"  智能合约风险: {risk_metrics.smart_contract_risk:.3f}")
                
                # 风险建议
                if risk_metrics.risk_level == RiskLevel.VERY_HIGH:
                    print(f"  ⚠️ 建议: 极高风险，谨慎投资")
                elif risk_metrics.risk_level == RiskLevel.HIGH:
                    print(f"  ⚠️ 建议: 高风险，控制仓位")
                elif risk_metrics.risk_level == RiskLevel.MEDIUM:
                    print(f"  ✅ 建议: 中等风险，适度配置")
                else:
                    print(f"  ✅ 建议: 低风险，可适当增配")
            
            except Exception as e:
                print(f"  ❌ {symbol} 风险评估失败: {e}")
        
        print(f"\n--- 投资组合风险评估 ---")
        
        # 模拟投资组合
        portfolio_positions = {
            'BTC': {'value': 50000, 'quantity': 1.0},
            'ETH': {'value': 30000, 'quantity': 10.0},
            'ADA': {'value': 15000, 'quantity': 15000.0},
            'DOT': {'value': 5000, 'quantity': 500.0}
        }
        
        print(f"投资组合构成:")
        total_value = sum(pos['value'] for pos in portfolio_positions.values())
        for symbol, position in portfolio_positions.items():
            weight = position['value'] / total_value
            print(f"  {symbol}: ${position['value']:,} ({weight:.1%})")
        
        print(f"总价值: ${total_value:,}")
        
        # 评估投资组合风险
        portfolio_risk = await risk_engine.assess_portfolio_risk(portfolio_positions, days=30)
        
        print(f"\n投资组合风险分析:")
        print(f"  总价值: ${portfolio_risk.total_value:,}")
        print(f"  投资组合波动率: {portfolio_risk.portfolio_volatility:.2%}")
        print(f"  投资组合VaR: {portfolio_risk.portfolio_var:.2%}")
        print(f"  投资组合CVaR: {portfolio_risk.portfolio_cvar:.2%}")
        print(f"  最大回撤: {portfolio_risk.max_drawdown:.2%}")
        print(f"  夏普比率: {portfolio_risk.sharpe_ratio:.2f}")
        print(f"  集中度风险: {portfolio_risk.concentration_risk:.3f}")
        print(f"  最大单仓位: {portfolio_risk.largest_position_pct:.1%}")
        print(f"  前5大仓位: {portfolio_risk.top_5_positions_pct:.1%}")
        print(f"  平均相关性: {portfolio_risk.avg_correlation:.3f}")
        print(f"  流动性风险: {portfolio_risk.liquidity_risk:.3f}")
        print(f"  综合风险等级: {portfolio_risk.overall_risk_level.value}")
        
        print(f"\n风险管理建议:")
        for i, recommendation in enumerate(portfolio_risk.risk_recommendations, 1):
            print(f"  {i}. {recommendation}")
    
    except Exception as e:
        print(f"风险评估演示失败: {e}")


async def demo_position_management():
    """演示仓位管理功能"""
    print("\n=== 仓位管理演示 ===")
    
    try:
        print("--- 仓位管理配置 ---")
        
        # 测试不同的仓位管理方法
        position_methods = [
            (PositionSizeMethod.FIXED_PERCENTAGE, "固定百分比"),
            (PositionSizeMethod.VOLATILITY_ADJUSTED, "波动率调整"),
            (PositionSizeMethod.RISK_PARITY, "风险平价"),
            (PositionSizeMethod.KELLY_CRITERION, "凯利公式")
        ]
        
        for method, method_name in position_methods:
            print(f"\n--- {method_name}仓位管理 ---")
            
            config = PositionSizeConfig(
                method=method,
                base_size=0.1,  # 10%基础仓位
                max_position_size=0.2,  # 20%最大单仓位
                max_total_exposure=0.8,  # 80%最大总敞口
                risk_per_trade=0.02,  # 2%单笔风险
                rebalance_threshold=0.05  # 5%再平衡阈值
            )
            
            manager = PositionManager(config)
            manager.set_capital(100000, 80000)  # 总资金10万，可用8万
            
            print(f"配置参数:")
            print(f"  方法: {method_name}")
            print(f"  基础仓位: {config.base_size:.1%}")
            print(f"  最大单仓位: {config.max_position_size:.1%}")
            print(f"  最大总敞口: {config.max_total_exposure:.1%}")
            print(f"  单笔风险: {config.risk_per_trade:.1%}")
            
            # 计算不同资产的建议仓位
            symbols = ['BTC', 'ETH', 'ADA']
            
            for symbol in symbols:
                try:
                    position_size = await manager.calculate_position_size(
                        symbol, signal_strength=0.8
                    )
                    
                    position_pct = position_size / manager.total_capital
                    print(f"  {symbol}: ${position_size:,.0f} ({position_pct:.1%})")
                
                except Exception as e:
                    print(f"  {symbol}: 计算失败 - {e}")
        
        print(f"\n--- 仓位调整建议 ---")
        
        # 使用波动率调整方法进行详细演示
        config = PositionSizeConfig(
            method=PositionSizeMethod.VOLATILITY_ADJUSTED,
            base_size=0.15,
            max_position_size=0.25,
            max_total_exposure=0.8,
            risk_per_trade=0.02,
            rebalance_threshold=0.05
        )
        
        manager = PositionManager(config)
        manager.set_capital(100000)
        
        # 设置当前持仓
        current_positions = {
            'BTC': {'value': 20000, 'quantity': 0.4},
            'ETH': {'value': 15000, 'quantity': 5.0}
        }
        manager.update_positions(current_positions)
        
        # 模拟交易信号
        signals = [
            {
                'symbol': 'BTC',
                'action': 'buy',
                'confidence': 0.9,
                'reason': '技术突破信号'
            },
            {
                'symbol': 'ETH',
                'action': 'sell',
                'confidence': 0.7,
                'reason': '获利了结'
            },
            {
                'symbol': 'ADA',
                'action': 'buy',
                'confidence': 0.6,
                'reason': 'Smart Money买入'
            }
        ]
        
        print(f"当前持仓:")
        for symbol, position in current_positions.items():
            print(f"  {symbol}: ${position['value']:,}")
        
        print(f"\n交易信号:")
        for signal in signals:
            print(f"  {signal['symbol']}: {signal['action']} "
                  f"(置信度: {signal['confidence']:.1%}, 原因: {signal['reason']})")
        
        # 生成仓位调整建议
        recommendations = await manager.generate_position_recommendations(signals)
        
        print(f"\n仓位调整建议:")
        for rec in recommendations:
            change_pct = rec.size_change / manager.total_capital
            print(f"  {rec.symbol}:")
            print(f"    动作: {rec.action}")
            print(f"    当前仓位: ${rec.current_size:,.0f}")
            print(f"    建议仓位: ${rec.recommended_size:,.0f}")
            print(f"    变化: ${rec.size_change:,.0f} ({change_pct:+.1%})")
            print(f"    原因: {rec.reason}")
            print(f"    置信度: {rec.confidence:.1%}")
            print(f"    风险等级: {rec.risk_level.value}")
        
        # 投资组合再平衡
        print(f"\n--- 投资组合再平衡 ---")
        
        rebalance_recommendations = await manager.rebalance_portfolio()
        
        if rebalance_recommendations:
            print(f"再平衡建议:")
            for rec in rebalance_recommendations:
                print(f"  {rec.symbol}: {rec.action} "
                      f"${rec.size_change:+,.0f} ({rec.reason})")
        else:
            print(f"当前投资组合无需再平衡")
        
        # 仓位摘要
        summary = manager.get_position_summary()
        print(f"\n仓位摘要:")
        print(f"  总资金: ${summary['total_capital']:,}")
        print(f"  已用资金: ${summary['used_capital']:,}")
        print(f"  可用资金: ${summary['available_capital']:,}")
        print(f"  资金利用率: {summary['capital_utilization']:.1%}")
        print(f"  持仓数量: {summary['number_of_positions']}")
        print(f"  最大单仓位: ${summary['largest_position']:,} "
              f"({summary['largest_position_pct']:.1%})")
    
    except Exception as e:
        print(f"仓位管理演示失败: {e}")


async def demo_stop_loss_management():
    """演示止损止盈管理功能"""
    print("\n=== 止损止盈管理演示 ===")
    
    try:
        print("--- 止损止盈配置 ---")
        
        # 不同的止损止盈策略
        strategies = [
            {
                'name': '固定百分比策略',
                'stop_config': StopLossConfig(
                    stop_type=StopLossType.FIXED_PERCENTAGE,
                    stop_percentage=0.05
                ),
                'profit_config': TakeProfitConfig(
                    profit_type=TakeProfitType.FIXED_PERCENTAGE,
                    profit_percentage=0.10
                )
            },
            {
                'name': '波动率调整策略',
                'stop_config': StopLossConfig(
                    stop_type=StopLossType.VOLATILITY_BASED,
                    volatility_multiplier=2.0
                ),
                'profit_config': TakeProfitConfig(
                    profit_type=TakeProfitType.RISK_REWARD_RATIO,
                    risk_reward_ratio=2.0
                )
            },
            {
                'name': '移动止损策略',
                'stop_config': StopLossConfig(
                    stop_type=StopLossType.TRAILING_STOP,
                    trailing_percentage=0.03
                ),
                'profit_config': TakeProfitConfig(
                    profit_type=TakeProfitType.TRAILING_PROFIT,
                    trailing_percentage=0.05
                )
            },
            {
                'name': '分批止盈策略',
                'stop_config': StopLossConfig(
                    stop_type=StopLossType.FIXED_PERCENTAGE,
                    stop_percentage=0.08
                ),
                'profit_config': TakeProfitConfig(
                    profit_type=TakeProfitType.PARTIAL_PROFIT,
                    partial_levels=[(0.05, 0.3), (0.10, 0.5), (0.20, 1.0)]
                )
            }
        ]
        
        for strategy in strategies:
            print(f"\n--- {strategy['name']} ---")
            
            manager = StopLossManager(strategy['stop_config'], strategy['profit_config'])
            
            # 模拟开仓
            entry_price = 50000
            quantity = 1.0
            position_id = f"pos_{int(datetime.now().timestamp())}"
            
            print(f"开仓信息:")
            print(f"  资产: BTC")
            print(f"  开仓价格: ${entry_price:,}")
            print(f"  数量: {quantity}")
            print(f"  仓位方向: 多头")
            
            # 创建止损止盈订单
            stop_orders = await manager.create_stop_orders(
                symbol='BTC',
                position_id=position_id,
                entry_price=entry_price,
                quantity=quantity,
                position_side='long'
            )
            
            print(f"\n创建的订单:")
            for order in stop_orders:
                order_type_name = '止损' if order.order_type == 'stop_loss' else '止盈'
                trigger_pct = (order.trigger_price - entry_price) / entry_price
                
                print(f"  {order_type_name}订单:")
                print(f"    触发价格: ${order.trigger_price:,.2f} ({trigger_pct:+.1%})")
                print(f"    数量: {order.quantity}")
                print(f"    创建时间: {order.created_at.strftime('%H:%M:%S')}")
            
            # 模拟价格变化和订单更新
            print(f"\n价格变化模拟:")
            
            price_scenarios = [
                (52000, "价格上涨4%"),
                (48000, "价格下跌4%"),
                (55000, "价格大涨10%"),
                (46000, "价格大跌8%")
            ]
            
            for current_price, scenario in price_scenarios:
                print(f"\n  {scenario} -> ${current_price:,}")
                
                # 更新订单
                triggered_orders = await manager.update_stop_orders({'BTC': current_price})
                
                if triggered_orders:
                    for triggered in triggered_orders:
                        order_type_name = '止损' if triggered['order_type'] == 'stop_loss' else '止盈'
                        pnl = triggered['pnl']
                        pnl_pct = pnl / (entry_price * quantity)
                        
                        print(f"    ✅ {order_type_name}触发!")
                        print(f"    触发价格: ${triggered['trigger_price']:,.2f}")
                        print(f"    当前价格: ${triggered['current_price']:,.2f}")
                        print(f"    盈亏: ${pnl:+,.2f} ({pnl_pct:+.1%})")
                else:
                    print(f"    📊 订单未触发，继续监控")
                
                # 显示活跃订单状态
                active_orders = manager.get_active_orders('BTC')
                if active_orders:
                    print(f"    活跃订单: {len(active_orders)}个")
                    for order in active_orders:
                        order_type_name = '止损' if order.order_type == 'stop_loss' else '止盈'
                        distance_pct = (order.trigger_price - current_price) / current_price
                        print(f"      {order_type_name}: ${order.trigger_price:,.2f} "
                              f"({distance_pct:+.1%})")
        
        print(f"\n--- 止损止盈统计 ---")
        
        # 使用最后一个管理器的统计
        stats = manager.get_statistics()
        
        print(f"统计信息:")
        print(f"  总止损次数: {stats['total_stop_losses']}")
        print(f"  总止盈次数: {stats['total_take_profits']}")
        print(f"  总订单数: {stats['total_orders']}")
        print(f"  平均止损幅度: {stats['avg_stop_loss_pct']:.2%}")
        print(f"  平均止盈幅度: {stats['avg_take_profit_pct']:.2%}")
        print(f"  止损止盈比: {stats['stop_profit_ratio']:.2f}")
        print(f"  活跃订单数: {stats['active_orders_count']}")
        
        print(f"\n配置信息:")
        config_info = stats['config']
        print(f"  止损类型: {config_info['stop_loss_type']}")
        print(f"  止损百分比: {config_info['stop_percentage']:.1%}")
        print(f"  止盈类型: {config_info['take_profit_type']}")
        print(f"  止盈百分比: {config_info['profit_percentage']:.1%}")
    
    except Exception as e:
        print(f"止损止盈管理演示失败: {e}")


async def demo_integrated_risk_management():
    """演示集成风险管理场景"""
    print("\n=== 集成风险管理场景演示 ===")
    
    try:
        print("--- 完整风险管理流程 ---")
        
        # 1. 风险评估
        print("1. 进行风险评估...")
        risk_engine = RiskEngine()
        
        # 模拟投资组合
        portfolio = {
            'BTC': {'value': 40000, 'quantity': 0.8},
            'ETH': {'value': 25000, 'quantity': 8.33},
            'ADA': {'value': 20000, 'quantity': 20000},
            'DOT': {'value': 15000, 'quantity': 1500}
        }
        
        portfolio_risk = await risk_engine.assess_portfolio_risk(portfolio)
        
        print(f"   投资组合风险等级: {portfolio_risk.overall_risk_level.value}")
        print(f"   集中度风险: {portfolio_risk.concentration_risk:.3f}")
        print(f"   流动性风险: {portfolio_risk.liquidity_risk:.3f}")
        
        # 2. 仓位调整
        print("\n2. 基于风险调整仓位...")
        
        position_config = PositionSizeConfig(
            method=PositionSizeMethod.RISK_PARITY,
            base_size=0.12,
            max_position_size=0.25,
            max_total_exposure=0.85,
            risk_per_trade=0.015
        )
        
        position_manager = PositionManager(position_config)
        position_manager.set_capital(100000)
        position_manager.update_positions(portfolio)
        
        # 基于风险等级调整建议
        if portfolio_risk.overall_risk_level in [RiskLevel.HIGH, RiskLevel.VERY_HIGH]:
            print("   ⚠️ 高风险投资组合，建议降低仓位")
            # 生成减仓建议
            rebalance_recs = await position_manager.rebalance_portfolio()
            
            for rec in rebalance_recs[:3]:  # 显示前3个建议
                print(f"     {rec.symbol}: {rec.action} ${rec.size_change:+,.0f}")
        
        # 3. 设置止损止盈
        print("\n3. 设置动态止损止盈...")
        
        # 根据风险等级调整止损止盈参数
        if portfolio_risk.overall_risk_level == RiskLevel.HIGH:
            stop_percentage = 0.04  # 更严格的止损
            profit_percentage = 0.08
        elif portfolio_risk.overall_risk_level == RiskLevel.MEDIUM:
            stop_percentage = 0.06
            profit_percentage = 0.12
        else:
            stop_percentage = 0.08
            profit_percentage = 0.15
        
        stop_config = StopLossConfig(
            stop_type=StopLossType.VOLATILITY_BASED,
            stop_percentage=stop_percentage,
            volatility_multiplier=1.5
        )
        
        profit_config = TakeProfitConfig(
            profit_type=TakeProfitType.TRAILING_PROFIT,
            profit_percentage=profit_percentage,
            trailing_percentage=0.04
        )
        
        stop_manager = StopLossManager(stop_config, profit_config)
        
        print(f"   止损参数: {stop_percentage:.1%} (基于风险等级调整)")
        print(f"   止盈参数: {profit_percentage:.1%}")
        
        # 为主要持仓设置止损止盈
        for symbol, position in portfolio.items():
            if position['value'] > 10000:  # 只为大仓位设置
                entry_price = position['value'] / position['quantity']
                
                orders = await stop_manager.create_stop_orders(
                    symbol=symbol,
                    position_id=f"pos_{symbol}",
                    entry_price=entry_price,
                    quantity=position['quantity']
                )
                
                print(f"   {symbol}: 创建 {len(orders)} 个止损止盈订单")
        
        # 4. 风险监控
        print("\n4. 持续风险监控...")
        
        # 模拟市场变化
        market_scenarios = [
            ("市场上涨", {'BTC': 52000, 'ETH': 3200, 'ADA': 1.2, 'DOT': 12}),
            ("市场下跌", {'BTC': 46000, 'ETH': 2800, 'ADA': 0.9, 'DOT': 8}),
            ("剧烈波动", {'BTC': 44000, 'ETH': 2600, 'ADA': 0.8, 'DOT': 7})
        ]
        
        for scenario_name, prices in market_scenarios:
            print(f"\n   {scenario_name}场景:")
            
            # 更新止损止盈
            triggered_orders = await stop_manager.update_stop_orders(prices)
            
            if triggered_orders:
                total_pnl = sum(order['pnl'] for order in triggered_orders)
                print(f"     触发 {len(triggered_orders)} 个订单")
                print(f"     总盈亏: ${total_pnl:+,.0f}")
                
                for order in triggered_orders[:2]:  # 显示前2个
                    order_type = '止损' if order['order_type'] == 'stop_loss' else '止盈'
                    print(f"       {order['symbol']} {order_type}: ${order['pnl']:+,.0f}")
            else:
                print(f"     无订单触发，继续监控")
            
            # 重新评估风险
            updated_portfolio = {
                symbol: {
                    'value': prices[symbol] * position['quantity'],
                    'quantity': position['quantity']
                }
                for symbol, position in portfolio.items()
            }
            
            updated_risk = await risk_engine.assess_portfolio_risk(updated_portfolio)
            
            if updated_risk.overall_risk_level != portfolio_risk.overall_risk_level:
                print(f"     风险等级变化: {portfolio_risk.overall_risk_level.value} "
                      f"-> {updated_risk.overall_risk_level.value}")
        
        # 5. 风险报告
        print("\n5. 生成风险管理报告...")
        
        position_summary = position_manager.get_position_summary()
        stop_stats = stop_manager.get_statistics()
        
        print(f"\n📊 风险管理报告:")
        print(f"   投资组合总价值: ${position_summary['total_capital']:,}")
        print(f"   资金利用率: {position_summary['capital_utilization']:.1%}")
        print(f"   最大单仓位占比: {position_summary['largest_position_pct']:.1%}")
        print(f"   投资组合风险等级: {portfolio_risk.overall_risk_level.value}")
        print(f"   集中度风险: {portfolio_risk.concentration_risk:.3f}")
        print(f"   止损止盈订单: {stop_stats['active_orders_count']} 个活跃")
        
        print(f"\n💡 风险管理建议:")
        for i, rec in enumerate(portfolio_risk.risk_recommendations[:3], 1):
            print(f"   {i}. {rec}")
        
        print(f"\n✅ 集成风险管理流程演示完成")
    
    except Exception as e:
        print(f"集成风险管理演示失败: {e}")


async def main():
    """主演示函数"""
    print("🛡️ 风险管理模块演示")
    print("=" * 50)
    
    # 检查配置
    try:
        from config.settings import RISK_CONFIG
        print("✅ 配置文件加载成功")
        
        # 检查风险管理配置
        print(f"风险管理配置:")
        print(f"  默认止损比例: {RISK_CONFIG.get('default_stop_loss', 0.05):.1%}")
        print(f"  默认止盈比例: {RISK_CONFIG.get('default_take_profit', 0.10):.1%}")
        print(f"  最大单仓位: {RISK_CONFIG.get('max_position_size', 0.20):.1%}")
        print(f"  最大总敞口: {RISK_CONFIG.get('max_total_exposure', 0.80):.1%}")
    
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        print("使用默认配置继续演示")
    
    # 运行演示
    demos = [
        ("风险评估", demo_risk_assessment),
        ("仓位管理", demo_position_management),
        ("止损止盈管理", demo_stop_loss_management),
        ("集成风险管理场景", demo_integrated_risk_management),
    ]
    
    for demo_name, demo_func in demos:
        try:
            print(f"\n🔄 开始 {demo_name} 演示...")
            await demo_func()
            print(f"✅ {demo_name} 演示完成")
        except Exception as e:
            print(f"❌ {demo_name} 演示失败: {e}")
            logger.error(f"{demo_name} demo failed", exc_info=True)
        
        # 在演示之间添加延迟
        await asyncio.sleep(1)
    
    print("\n🎉 所有演示完成!")
    print("=" * 50)
    print("\n💡 提示:")
    print("- 风险管理是投资成功的关键因素")
    print("- 建议根据市场环境动态调整风险参数")
    print("- 止损止盈策略应与投资目标相匹配")
    print("- 定期评估和调整投资组合风险")
    print("- 永远不要忽视流动性和交易对手风险")


if __name__ == "__main__":
    # 运行演示
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 演示被用户中断")
    except Exception as e:
        print(f"\n💥 演示过程中发生错误: {e}")
        logger.error("Demo failed", exc_info=True)
