"""
主配置文件
包含系统的所有配置参数
"""
import os
from pathlib import Path
from decouple import config

# 项目根目录
BASE_DIR = Path(__file__).parent.parent

# 数据库配置
DATABASE_CONFIG = {
    'host': config('DB_HOST', default='localhost'),
    'port': config('DB_PORT', default=5432, cast=int),
    'database': config('DB_NAME', default='onchain_analysis'),
    'username': config('DB_USER', default='postgres'),
    'password': config('DB_PASSWORD', default=''),
    'pool_size': config('DB_POOL_SIZE', default=10, cast=int),
    'max_overflow': config('DB_MAX_OVERFLOW', default=20, cast=int),
}

# Redis配置
REDIS_CONFIG = {
    'host': config('REDIS_HOST', default='localhost'),
    'port': config('REDIS_PORT', default=6379, cast=int),
    'db': config('REDIS_DB', default=0, cast=int),
    'password': config('REDIS_PASSWORD', default=None),
    'decode_responses': True,
}

# API配置
API_CONFIG = {
    'etherscan': {
        'api_key': config('ETHERSCAN_API_KEY', default=''),
        'base_url': 'https://api.etherscan.io/api',
        'rate_limit': 5,  # requests per second
    },
    'moralis': {
        'api_key': config('MORALIS_API_KEY', default=''),
        'base_url': 'https://deep-index.moralis.io/api/v2',
        'rate_limit': 25,
    },
    'coingecko': {
        'api_key': config('COINGECKO_API_KEY', default=''),
        'base_url': 'https://api.coingecko.com/api/v3',
        'rate_limit': 10,
    },
    'defillama': {
        'base_url': 'https://api.llama.fi',
        'rate_limit': 10,
    },
    'dune': {
        'api_key': config('DUNE_API_KEY', default=''),
        'base_url': 'https://api.dune.com/api/v1',
        'rate_limit': 5,
    }
}

# 区块链网络配置
BLOCKCHAIN_CONFIG = {
    'ethereum': {
        'rpc_url': config('ETH_RPC_URL', default='https://mainnet.infura.io/v3/YOUR_PROJECT_ID'),
        'chain_id': 1,
        'block_time': 12,  # seconds
    },
    'bsc': {
        'rpc_url': config('BSC_RPC_URL', default='https://bsc-dataseed.binance.org/'),
        'chain_id': 56,
        'block_time': 3,
    },
    'polygon': {
        'rpc_url': config('POLYGON_RPC_URL', default='https://polygon-rpc.com/'),
        'chain_id': 137,
        'block_time': 2,
    }
}

# 监控配置
MONITORING_CONFIG = {
    'whale_threshold': {
        'eth': 100,  # ETH
        'btc': 10,   # BTC
        'usdt': 1000000,  # USDT
    },
    'alert_channels': {
        'email': config('ALERT_EMAIL', default=''),
        'telegram': {
            'bot_token': config('TELEGRAM_BOT_TOKEN', default=''),
            'chat_id': config('TELEGRAM_CHAT_ID', default=''),
        },
        'discord': {
            'webhook_url': config('DISCORD_WEBHOOK_URL', default=''),
        }
    },
    'check_intervals': {
        'whale_tracking': 60,  # seconds
        'smart_money': 300,    # seconds
        'market_data': 30,     # seconds
    }
}

# 交易配置
TRADING_CONFIG = {
    'risk_management': {
        'max_position_size': 0.05,  # 5% of portfolio
        'max_daily_loss': 0.02,     # 2% daily loss limit
        'stop_loss_pct': 0.05,      # 5% stop loss
        'take_profit_pct': 0.15,    # 15% take profit
    },
    'smart_money': {
        'min_roi': 5.0,             # Minimum 500% ROI
        'min_trades': 50,           # Minimum 50 trades
        'follow_amount': 0.01,      # 1% of portfolio per follow
    },
    'backtesting': {
        'initial_capital': 100000,  # $100k
        'commission': 0.001,        # 0.1% commission
        'slippage': 0.001,          # 0.1% slippage
    }
}

# 日志配置
LOGGING_CONFIG = {
    'level': config('LOG_LEVEL', default='INFO'),
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'file_path': BASE_DIR / 'logs' / 'app.log',
    'max_file_size': 10 * 1024 * 1024,  # 10MB
    'backup_count': 5,
}

# 缓存配置
CACHE_CONFIG = {
    'default_timeout': 300,  # 5 minutes
    'market_data_timeout': 60,  # 1 minute
    'blockchain_data_timeout': 600,  # 10 minutes
    'analysis_timeout': 1800,  # 30 minutes
}

# Web应用配置
WEB_CONFIG = {
    'host': config('WEB_HOST', default='0.0.0.0'),
    'port': config('WEB_PORT', default=5000, cast=int),
    'debug': config('DEBUG', default=False, cast=bool),
    'secret_key': config('SECRET_KEY', default='your-secret-key-here'),
}

# 数据目录配置
DATA_DIRS = {
    'raw': BASE_DIR / 'data' / 'raw',
    'processed': BASE_DIR / 'data' / 'processed',
    'cache': BASE_DIR / 'data' / 'cache',
    'logs': BASE_DIR / 'logs',
}

# 确保数据目录存在
for dir_path in DATA_DIRS.values():
    dir_path.mkdir(parents=True, exist_ok=True)

# 开发环境配置
DEVELOPMENT = {
    'auto_reload': True,
    'debug_sql': config('DEBUG_SQL', default=False, cast=bool),
    'mock_apis': config('MOCK_APIS', default=False, cast=bool),
}

# 生产环境配置
PRODUCTION = {
    'workers': config('WORKERS', default=4, cast=int),
    'timeout': config('TIMEOUT', default=30, cast=int),
    'keepalive': config('KEEPALIVE', default=2, cast=int),
}
