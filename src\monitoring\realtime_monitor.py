"""
实时监控模块
实时监控链上数据、价格变化和异常活动
"""
import asyncio
import json
import time
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timezone, timedelta
from dataclasses import dataclass
from collections import deque
import websockets

from src.data.data_coordinator import DataCoordinator
from src.monitoring.whale_monitor import WhaleMonitor
from src.monitoring.alert_system import AlertSystem
from src.indicators.indicator_engine import IndicatorEngine
from src.database.repositories import TransactionRepository, MarketDataRepository
from src.utils.logger import get_logger
from config.settings import MONITORING_CONFIG, API_CONFIG

logger = get_logger(__name__)


@dataclass
class MonitoringEvent:
    """监控事件"""
    event_type: str
    symbol: str
    timestamp: datetime
    data: Dict[str, Any]
    severity: str  # low, medium, high, critical
    source: str


@dataclass
class MonitoringMetrics:
    """监控指标"""
    timestamp: datetime
    events_per_minute: int
    active_monitors: int
    alert_count: int
    system_health: Dict[str, Any]


class RealtimeMonitor:
    """实时监控器"""
    
    def __init__(self):
        self.data_coordinator = None
        self.whale_monitor = None
        self.alert_system = None
        self.indicator_engine = None
        
        # 监控状态
        self.is_running = False
        self.monitors = {}
        self.event_queue = deque(maxlen=1000)
        self.metrics_history = deque(maxlen=100)
        
        # 监控配置
        self.config = MONITORING_CONFIG
        self.monitored_symbols = ['BTC', 'ETH', 'BNB', 'ADA', 'DOT']
        
        # 事件处理器
        self.event_handlers = {}
        
        # WebSocket连接
        self.websocket_connections = {}
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.data_coordinator = await DataCoordinator().__aenter__()
        self.whale_monitor = await WhaleMonitor().__aenter__()
        self.alert_system = AlertSystem()
        self.indicator_engine = await IndicatorEngine().__aenter__()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.stop_monitoring()
        
        if self.indicator_engine:
            await self.indicator_engine.__aexit__(exc_type, exc_val, exc_tb)
        if self.whale_monitor:
            await self.whale_monitor.__aexit__(exc_type, exc_val, exc_tb)
        if self.data_coordinator:
            await self.data_coordinator.__aexit__(exc_type, exc_val, exc_tb)
    
    async def start_monitoring(self) -> None:
        """启动实时监控"""
        try:
            logger.info("Starting realtime monitoring system")
            
            if self.is_running:
                logger.warning("Monitoring system is already running")
                return
            
            self.is_running = True
            
            # 启动各种监控任务
            tasks = [
                self._monitor_price_changes(),
                self._monitor_whale_activities(),
                self._monitor_network_metrics(),
                self._monitor_smart_money_activities(),
                self._monitor_technical_signals(),
                self._process_events(),
                self._collect_metrics(),
                self._health_check_loop()
            ]
            
            # 启动WebSocket监控
            if self.config.get('websocket_enabled', True):
                tasks.append(self._start_websocket_monitoring())
            
            # 并行运行所有监控任务
            await asyncio.gather(*tasks, return_exceptions=True)
        
        except Exception as e:
            logger.error(f"Failed to start monitoring: {e}")
            self.is_running = False
            raise
    
    async def stop_monitoring(self) -> None:
        """停止实时监控"""
        try:
            logger.info("Stopping realtime monitoring system")
            self.is_running = False
            
            # 关闭WebSocket连接
            for symbol, ws in self.websocket_connections.items():
                try:
                    await ws.close()
                except Exception as e:
                    logger.error(f"Failed to close WebSocket for {symbol}: {e}")
            
            self.websocket_connections.clear()
            
            logger.info("Realtime monitoring system stopped")
        
        except Exception as e:
            logger.error(f"Failed to stop monitoring: {e}")
    
    async def _monitor_price_changes(self) -> None:
        """监控价格变化"""
        try:
            logger.info("Starting price change monitoring")
            
            price_cache = {}
            
            while self.is_running:
                try:
                    for symbol in self.monitored_symbols:
                        # 获取当前价格
                        current_price = await self._get_current_price(symbol)
                        
                        if current_price and symbol in price_cache:
                            previous_price = price_cache[symbol]
                            
                            # 计算价格变化
                            price_change = (current_price - previous_price) / previous_price
                            
                            # 检查是否触发价格预警
                            if abs(price_change) > 0.05:  # 5%变化
                                severity = 'high' if abs(price_change) > 0.10 else 'medium'
                                
                                event = MonitoringEvent(
                                    event_type='price_change',
                                    symbol=symbol,
                                    timestamp=datetime.now(timezone.utc),
                                    data={
                                        'previous_price': previous_price,
                                        'current_price': current_price,
                                        'change_percentage': price_change * 100,
                                        'change_direction': 'up' if price_change > 0 else 'down'
                                    },
                                    severity=severity,
                                    source='price_monitor'
                                )
                                
                                await self._emit_event(event)
                        
                        if current_price:
                            price_cache[symbol] = current_price
                    
                    await asyncio.sleep(30)  # 每30秒检查一次
                
                except Exception as e:
                    logger.error(f"Error in price monitoring: {e}")
                    await asyncio.sleep(60)
        
        except Exception as e:
            logger.error(f"Price monitoring failed: {e}")
    
    async def _monitor_whale_activities(self) -> None:
        """监控巨鲸活动"""
        try:
            logger.info("Starting whale activity monitoring")
            
            while self.is_running:
                try:
                    # 监控最近1小时的巨鲸交易
                    whale_transactions = await self.whale_monitor.monitor_whale_transactions(hours=1)
                    
                    for tx in whale_transactions:
                        event = MonitoringEvent(
                            event_type='whale_transaction',
                            symbol=tx.token_symbol,
                            timestamp=tx.timestamp,
                            data={
                                'tx_hash': tx.tx_hash,
                                'from_address': tx.from_address,
                                'to_address': tx.to_address,
                                'value_usd': tx.value_usd,
                                'transaction_type': tx.transaction_type,
                                'alert_level': tx.alert_level.value,
                                'impact_score': tx.impact_score
                            },
                            severity=tx.alert_level.value,
                            source='whale_monitor'
                        )
                        
                        await self._emit_event(event)
                    
                    await asyncio.sleep(300)  # 每5分钟检查一次
                
                except Exception as e:
                    logger.error(f"Error in whale monitoring: {e}")
                    await asyncio.sleep(600)
        
        except Exception as e:
            logger.error(f"Whale monitoring failed: {e}")
    
    async def _monitor_network_metrics(self) -> None:
        """监控网络指标"""
        try:
            logger.info("Starting network metrics monitoring")
            
            while self.is_running:
                try:
                    for symbol in self.monitored_symbols:
                        # 获取网络指标
                        metrics = await self._get_network_metrics(symbol)
                        
                        if metrics:
                            # 检查异常指标
                            anomalies = self._detect_network_anomalies(symbol, metrics)
                            
                            for anomaly in anomalies:
                                event = MonitoringEvent(
                                    event_type='network_anomaly',
                                    symbol=symbol,
                                    timestamp=datetime.now(timezone.utc),
                                    data={
                                        'anomaly_type': anomaly['type'],
                                        'current_value': anomaly['current_value'],
                                        'expected_range': anomaly['expected_range'],
                                        'deviation': anomaly['deviation']
                                    },
                                    severity=anomaly['severity'],
                                    source='network_monitor'
                                )
                                
                                await self._emit_event(event)
                    
                    await asyncio.sleep(600)  # 每10分钟检查一次
                
                except Exception as e:
                    logger.error(f"Error in network monitoring: {e}")
                    await asyncio.sleep(900)
        
        except Exception as e:
            logger.error(f"Network monitoring failed: {e}")
    
    async def _monitor_smart_money_activities(self) -> None:
        """监控Smart Money活动"""
        try:
            logger.info("Starting Smart Money activity monitoring")
            
            while self.is_running:
                try:
                    # 获取最近的Smart Money活动
                    from src.analysis.smart_money_signals import SmartMoneySignalGenerator
                    
                    async with SmartMoneySignalGenerator() as generator:
                        top_moves = await generator.get_top_smart_money_moves(hours=1, limit=10)
                        
                        for move in top_moves:
                            if move['impact_score'] > 0.5:  # 高影响力活动
                                event = MonitoringEvent(
                                    event_type='smart_money_activity',
                                    symbol=move['token_symbol'],
                                    timestamp=datetime.now(timezone.utc),
                                    data={
                                        'direction': move['direction'],
                                        'impact_score': move['impact_score'],
                                        'total_volume_usd': move['total_volume_usd'],
                                        'unique_addresses': move['unique_addresses'],
                                        'buy_ratio': move['buy_ratio']
                                    },
                                    severity='high' if move['impact_score'] > 0.8 else 'medium',
                                    source='smart_money_monitor'
                                )
                                
                                await self._emit_event(event)
                    
                    await asyncio.sleep(900)  # 每15分钟检查一次
                
                except Exception as e:
                    logger.error(f"Error in Smart Money monitoring: {e}")
                    await asyncio.sleep(1200)
        
        except Exception as e:
            logger.error(f"Smart Money monitoring failed: {e}")
    
    async def _monitor_technical_signals(self) -> None:
        """监控技术信号"""
        try:
            logger.info("Starting technical signal monitoring")
            
            while self.is_running:
                try:
                    for symbol in self.monitored_symbols:
                        # 获取当前技术信号
                        signal = await self.indicator_engine.analyze_symbol(symbol, days=7)
                        
                        # 检查信号变化
                        if signal.composite_strength > 0.7 and signal.confidence > 0.8:
                            event = MonitoringEvent(
                                event_type='technical_signal',
                                symbol=symbol,
                                timestamp=signal.timestamp,
                                data={
                                    'composite_signal': signal.composite_signal,
                                    'composite_strength': signal.composite_strength,
                                    'confidence': signal.confidence,
                                    'recommendation': signal.recommendation,
                                    'technical_signal': signal.technical_signal,
                                    'onchain_signal': signal.onchain_signal
                                },
                                severity='high' if signal.composite_strength > 0.8 else 'medium',
                                source='technical_monitor'
                            )
                            
                            await self._emit_event(event)
                    
                    await asyncio.sleep(1800)  # 每30分钟检查一次
                
                except Exception as e:
                    logger.error(f"Error in technical monitoring: {e}")
                    await asyncio.sleep(2400)
        
        except Exception as e:
            logger.error(f"Technical monitoring failed: {e}")
    
    async def _start_websocket_monitoring(self) -> None:
        """启动WebSocket监控"""
        try:
            logger.info("Starting WebSocket monitoring")
            
            # 为每个监控的代币启动WebSocket连接
            tasks = []
            for symbol in self.monitored_symbols:
                task = self._monitor_websocket_symbol(symbol)
                tasks.append(task)
            
            await asyncio.gather(*tasks, return_exceptions=True)
        
        except Exception as e:
            logger.error(f"WebSocket monitoring failed: {e}")
    
    async def _monitor_websocket_symbol(self, symbol: str) -> None:
        """监控单个代币的WebSocket数据"""
        try:
            # 这里应该连接到实际的WebSocket API
            # 现在使用模拟数据
            
            while self.is_running:
                try:
                    # 模拟WebSocket数据接收
                    await asyncio.sleep(5)
                    
                    # 模拟接收到实时交易数据
                    mock_data = {
                        'symbol': symbol,
                        'price': 50000 + (time.time() % 1000),
                        'volume': 1000000,
                        'timestamp': datetime.now(timezone.utc)
                    }
                    
                    # 处理实时数据
                    await self._process_realtime_data(symbol, mock_data)
                
                except Exception as e:
                    logger.error(f"Error in WebSocket monitoring for {symbol}: {e}")
                    await asyncio.sleep(30)
        
        except Exception as e:
            logger.error(f"WebSocket monitoring for {symbol} failed: {e}")
    
    async def _process_realtime_data(self, symbol: str, data: Dict[str, Any]) -> None:
        """处理实时数据"""
        try:
            # 检查数据异常
            if self._is_data_anomalous(symbol, data):
                event = MonitoringEvent(
                    event_type='data_anomaly',
                    symbol=symbol,
                    timestamp=data['timestamp'],
                    data=data,
                    severity='medium',
                    source='websocket_monitor'
                )
                
                await self._emit_event(event)
        
        except Exception as e:
            logger.error(f"Failed to process realtime data: {e}")
    
    def _is_data_anomalous(self, symbol: str, data: Dict[str, Any]) -> bool:
        """检查数据是否异常"""
        try:
            # 简化的异常检测逻辑
            price = data.get('price', 0)
            volume = data.get('volume', 0)
            
            # 价格异常检测
            if price <= 0 or price > 1000000:
                return True
            
            # 成交量异常检测
            if volume < 0 or volume > 1e12:
                return True
            
            return False
        
        except Exception as e:
            logger.error(f"Failed to check data anomaly: {e}")
            return False
    
    async def _process_events(self) -> None:
        """处理监控事件"""
        try:
            logger.info("Starting event processing")
            
            while self.is_running:
                try:
                    if self.event_queue:
                        event = self.event_queue.popleft()
                        
                        # 处理事件
                        await self._handle_event(event)
                    
                    await asyncio.sleep(1)
                
                except Exception as e:
                    logger.error(f"Error in event processing: {e}")
                    await asyncio.sleep(5)
        
        except Exception as e:
            logger.error(f"Event processing failed: {e}")
    
    async def _handle_event(self, event: MonitoringEvent) -> None:
        """处理单个事件"""
        try:
            logger.debug(f"Handling event: {event.event_type} for {event.symbol}")
            
            # 根据事件类型和严重程度决定处理方式
            if event.severity in ['high', 'critical']:
                # 发送预警通知
                await self._send_alert(event)
            
            # 记录事件
            await self._log_event(event)
            
            # 调用注册的事件处理器
            if event.event_type in self.event_handlers:
                handler = self.event_handlers[event.event_type]
                await handler(event)
        
        except Exception as e:
            logger.error(f"Failed to handle event: {e}")
    
    async def _send_alert(self, event: MonitoringEvent) -> None:
        """发送预警"""
        try:
            # 创建预警消息
            alert_message = self._create_alert_message(event)
            
            # 通过预警系统发送
            # 这里可以扩展为发送到不同的通知渠道
            logger.info(f"ALERT: {alert_message}")
        
        except Exception as e:
            logger.error(f"Failed to send alert: {e}")
    
    def _create_alert_message(self, event: MonitoringEvent) -> str:
        """创建预警消息"""
        try:
            if event.event_type == 'price_change':
                data = event.data
                return (f"🚨 {event.symbol} 价格{data['change_direction']} "
                       f"{abs(data['change_percentage']):.2f}% "
                       f"(${data['current_price']:,.2f})")
            
            elif event.event_type == 'whale_transaction':
                data = event.data
                return (f"🐋 {event.symbol} 巨鲸交易: "
                       f"${data['value_usd']:,.0f} "
                       f"({data['transaction_type']})")
            
            elif event.event_type == 'smart_money_activity':
                data = event.data
                return (f"🧠 {event.symbol} Smart Money {data['direction']}: "
                       f"${data['total_volume_usd']:,.0f} "
                       f"({data['unique_addresses']} 地址)")
            
            elif event.event_type == 'technical_signal':
                data = event.data
                return (f"📊 {event.symbol} 技术信号: "
                       f"{data['composite_signal']} "
                       f"(强度: {data['composite_strength']:.2f})")
            
            else:
                return f"⚠️ {event.symbol} {event.event_type}: {event.severity}"
        
        except Exception as e:
            logger.error(f"Failed to create alert message: {e}")
            return f"Alert: {event.event_type} for {event.symbol}"
    
    async def _log_event(self, event: MonitoringEvent) -> None:
        """记录事件"""
        try:
            # 这里可以将事件记录到数据库或日志文件
            logger.info(f"Event logged: {event.event_type} - {event.symbol} - {event.severity}")
        
        except Exception as e:
            logger.error(f"Failed to log event: {e}")
    
    async def _collect_metrics(self) -> None:
        """收集监控指标"""
        try:
            logger.info("Starting metrics collection")
            
            while self.is_running:
                try:
                    # 计算指标
                    current_time = datetime.now(timezone.utc)
                    
                    # 计算每分钟事件数
                    recent_events = [
                        e for e in self.event_queue 
                        if (current_time - e.timestamp).total_seconds() < 60
                    ]
                    events_per_minute = len(recent_events)
                    
                    # 活跃监控器数量
                    active_monitors = len([m for m in self.monitors.values() if m.get('active', False)])
                    
                    # 预警数量
                    alert_count = len([e for e in recent_events if e.severity in ['high', 'critical']])
                    
                    # 系统健康状态
                    system_health = await self._check_system_health()
                    
                    metrics = MonitoringMetrics(
                        timestamp=current_time,
                        events_per_minute=events_per_minute,
                        active_monitors=active_monitors,
                        alert_count=alert_count,
                        system_health=system_health
                    )
                    
                    self.metrics_history.append(metrics)
                    
                    await asyncio.sleep(60)  # 每分钟收集一次
                
                except Exception as e:
                    logger.error(f"Error in metrics collection: {e}")
                    await asyncio.sleep(120)
        
        except Exception as e:
            logger.error(f"Metrics collection failed: {e}")
    
    async def _health_check_loop(self) -> None:
        """健康检查循环"""
        try:
            logger.info("Starting health check loop")
            
            while self.is_running:
                try:
                    health_status = await self._check_system_health()
                    
                    # 检查是否有健康问题
                    if not health_status.get('overall_healthy', True):
                        event = MonitoringEvent(
                            event_type='system_health',
                            symbol='SYSTEM',
                            timestamp=datetime.now(timezone.utc),
                            data=health_status,
                            severity='high',
                            source='health_monitor'
                        )
                        
                        await self._emit_event(event)
                    
                    await asyncio.sleep(300)  # 每5分钟检查一次
                
                except Exception as e:
                    logger.error(f"Error in health check: {e}")
                    await asyncio.sleep(600)
        
        except Exception as e:
            logger.error(f"Health check loop failed: {e}")
    
    async def _check_system_health(self) -> Dict[str, Any]:
        """检查系统健康状态"""
        try:
            health = {
                'overall_healthy': True,
                'components': {},
                'timestamp': datetime.now(timezone.utc)
            }
            
            # 检查数据库连接
            try:
                from src.database.database import db_manager
                db_health = db_manager.health_check()
                health['components']['database'] = db_health
                if not db_health.get('database', False):
                    health['overall_healthy'] = False
            except Exception as e:
                health['components']['database'] = {'error': str(e)}
                health['overall_healthy'] = False
            
            # 检查API连接
            try:
                # 简化的API健康检查
                health['components']['api'] = {'status': 'ok'}
            except Exception as e:
                health['components']['api'] = {'error': str(e)}
                health['overall_healthy'] = False
            
            # 检查内存使用
            import psutil
            memory_percent = psutil.virtual_memory().percent
            health['components']['memory'] = {
                'usage_percent': memory_percent,
                'status': 'ok' if memory_percent < 80 else 'warning'
            }
            
            if memory_percent > 90:
                health['overall_healthy'] = False
            
            return health
        
        except Exception as e:
            logger.error(f"Failed to check system health: {e}")
            return {'overall_healthy': False, 'error': str(e)}
    
    async def _emit_event(self, event: MonitoringEvent) -> None:
        """发出监控事件"""
        try:
            self.event_queue.append(event)
            logger.debug(f"Event emitted: {event.event_type} - {event.symbol}")
        
        except Exception as e:
            logger.error(f"Failed to emit event: {e}")
    
    def register_event_handler(self, event_type: str, handler: Callable) -> None:
        """注册事件处理器"""
        self.event_handlers[event_type] = handler
        logger.info(f"Registered handler for event type: {event_type}")
    
    async def _get_current_price(self, symbol: str) -> Optional[float]:
        """获取当前价格"""
        try:
            # 这里应该调用实际的价格API
            # 现在返回模拟价格
            import random
            base_prices = {'BTC': 50000, 'ETH': 3000, 'BNB': 300, 'ADA': 1, 'DOT': 20}
            base_price = base_prices.get(symbol, 100)
            return base_price * (1 + random.uniform(-0.05, 0.05))
        
        except Exception as e:
            logger.error(f"Failed to get current price for {symbol}: {e}")
            return None
    
    async def _get_network_metrics(self, symbol: str) -> Optional[Dict[str, Any]]:
        """获取网络指标"""
        try:
            # 这里应该获取实际的网络指标
            # 现在返回模拟数据
            import random
            return {
                'active_addresses': random.randint(50000, 150000),
                'transaction_count': random.randint(200000, 400000),
                'hash_rate': random.uniform(100, 200),
                'network_difficulty': random.uniform(20, 30)
            }
        
        except Exception as e:
            logger.error(f"Failed to get network metrics for {symbol}: {e}")
            return None
    
    def _detect_network_anomalies(self, symbol: str, metrics: Dict[str, Any]) -> List[Dict[str, Any]]:
        """检测网络异常"""
        try:
            anomalies = []
            
            # 简化的异常检测逻辑
            active_addresses = metrics.get('active_addresses', 0)
            if active_addresses < 30000:  # 活跃地址过少
                anomalies.append({
                    'type': 'low_active_addresses',
                    'current_value': active_addresses,
                    'expected_range': [50000, 150000],
                    'deviation': (50000 - active_addresses) / 50000,
                    'severity': 'medium'
                })
            
            return anomalies
        
        except Exception as e:
            logger.error(f"Failed to detect network anomalies: {e}")
            return []
    
    def get_monitoring_status(self) -> Dict[str, Any]:
        """获取监控状态"""
        try:
            latest_metrics = self.metrics_history[-1] if self.metrics_history else None
            
            return {
                'is_running': self.is_running,
                'monitored_symbols': self.monitored_symbols,
                'active_monitors': len(self.monitors),
                'event_queue_size': len(self.event_queue),
                'latest_metrics': {
                    'timestamp': latest_metrics.timestamp if latest_metrics else None,
                    'events_per_minute': latest_metrics.events_per_minute if latest_metrics else 0,
                    'alert_count': latest_metrics.alert_count if latest_metrics else 0,
                    'system_health': latest_metrics.system_health if latest_metrics else {}
                } if latest_metrics else None,
                'websocket_connections': len(self.websocket_connections)
            }
        
        except Exception as e:
            logger.error(f"Failed to get monitoring status: {e}")
            return {'error': str(e)}
