"""
回测策略模块
包含各种交易策略的实现
"""
import asyncio
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any
from datetime import datetime, timezone, timedelta
from abc import ABC, abstractmethod

from src.utils.logger import get_logger

logger = get_logger(__name__)


class BaseStrategy(ABC):
    """策略基类"""
    
    def __init__(self, name: str, params: Dict[str, Any] = None):
        self.name = name
        self.params = params or {}
        # 这些计算器在实际使用时会被初始化
        self.technical_calculator = None
        self.onchain_calculator = None
        self.smart_money_generator = None
    
    @abstractmethod
    async def generate_signals(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """生成交易信号"""
        pass
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """获取策略信息"""
        return {
            'name': self.name,
            'params': self.params,
            'description': self.__doc__ or 'No description available'
        }


class MovingAverageCrossoverStrategy(BaseStrategy):
    """移动平均线交叉策略"""
    
    def __init__(self, fast_period: int = 20, slow_period: int = 50):
        super().__init__(
            name="Moving Average Crossover",
            params={
                'fast_period': fast_period,
                'slow_period': slow_period
            }
        )
        self.fast_period = fast_period
        self.slow_period = slow_period
        self.positions = {}  # 记录当前持仓状态
    
    async def generate_signals(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """生成移动平均线交叉信号"""
        signals = []
        
        try:
            current_prices = data['current_prices']
            price_data = data['price_data']
            
            for symbol in current_prices.keys():
                if symbol not in price_data:
                    continue
                
                df = price_data[symbol]
                
                if len(df) < self.slow_period:
                    continue
                
                # 计算移动平均线
                df['fast_ma'] = df['close'].rolling(window=self.fast_period).mean()
                df['slow_ma'] = df['close'].rolling(window=self.slow_period).mean()
                
                # 获取最近两个数据点
                if len(df) < 2:
                    continue
                
                current = df.iloc[-1]
                previous = df.iloc[-2]
                
                # 检查交叉信号
                current_position = self.positions.get(symbol, 'none')
                
                # 金叉：快线上穿慢线，买入信号
                if (previous['fast_ma'] <= previous['slow_ma'] and 
                    current['fast_ma'] > current['slow_ma'] and
                    current_position != 'long'):
                    
                    signals.append({
                        'symbol': symbol,
                        'action': 'buy',
                        'reason': 'Golden Cross',
                        'fast_ma': current['fast_ma'],
                        'slow_ma': current['slow_ma'],
                        'price': current['close']
                    })
                    self.positions[symbol] = 'long'
                
                # 死叉：快线下穿慢线，卖出信号
                elif (previous['fast_ma'] >= previous['slow_ma'] and 
                      current['fast_ma'] < current['slow_ma'] and
                      current_position == 'long'):
                    
                    signals.append({
                        'symbol': symbol,
                        'action': 'sell',
                        'reason': 'Death Cross',
                        'fast_ma': current['fast_ma'],
                        'slow_ma': current['slow_ma'],
                        'price': current['close']
                    })
                    self.positions[symbol] = 'none'
        
        except Exception as e:
            logger.error(f"Failed to generate MA crossover signals: {e}")
        
        return signals


class RSIMeanReversionStrategy(BaseStrategy):
    """RSI均值回归策略"""
    
    def __init__(self, rsi_period: int = 14, oversold_threshold: float = 30, 
                 overbought_threshold: float = 70):
        super().__init__(
            name="RSI Mean Reversion",
            params={
                'rsi_period': rsi_period,
                'oversold_threshold': oversold_threshold,
                'overbought_threshold': overbought_threshold
            }
        )
        self.rsi_period = rsi_period
        self.oversold_threshold = oversold_threshold
        self.overbought_threshold = overbought_threshold
        self.positions = {}
    
    async def generate_signals(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """生成RSI均值回归信号"""
        signals = []
        
        try:
            current_prices = data['current_prices']
            price_data = data['price_data']
            
            for symbol in current_prices.keys():
                if symbol not in price_data:
                    continue
                
                df = price_data[symbol]
                
                if len(df) < self.rsi_period + 1:
                    continue
                
                # 计算RSI
                df['rsi'] = self._calculate_rsi(df['close'], self.rsi_period)
                
                current = df.iloc[-1]
                current_position = self.positions.get(symbol, 'none')
                
                # RSI超卖，买入信号
                if (current['rsi'] < self.oversold_threshold and 
                    current_position != 'long'):
                    
                    signals.append({
                        'symbol': symbol,
                        'action': 'buy',
                        'reason': f'RSI Oversold ({current["rsi"]:.2f})',
                        'rsi': current['rsi'],
                        'price': current['close']
                    })
                    self.positions[symbol] = 'long'
                
                # RSI超买，卖出信号
                elif (current['rsi'] > self.overbought_threshold and 
                      current_position == 'long'):
                    
                    signals.append({
                        'symbol': symbol,
                        'action': 'sell',
                        'reason': f'RSI Overbought ({current["rsi"]:.2f})',
                        'rsi': current['rsi'],
                        'price': current['close']
                    })
                    self.positions[symbol] = 'none'
        
        except Exception as e:
            logger.error(f"Failed to generate RSI signals: {e}")
        
        return signals
    
    def _calculate_rsi(self, prices: pd.Series, period: int) -> pd.Series:
        """计算RSI"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi


class BollingerBandsStrategy(BaseStrategy):
    """布林带策略"""
    
    def __init__(self, period: int = 20, std_dev: float = 2.0):
        super().__init__(
            name="Bollinger Bands",
            params={
                'period': period,
                'std_dev': std_dev
            }
        )
        self.period = period
        self.std_dev = std_dev
        self.positions = {}
    
    async def generate_signals(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """生成布林带信号"""
        signals = []
        
        try:
            current_prices = data['current_prices']
            price_data = data['price_data']
            
            for symbol in current_prices.keys():
                if symbol not in price_data:
                    continue
                
                df = price_data[symbol]
                
                if len(df) < self.period:
                    continue
                
                # 计算布林带
                df['bb_middle'] = df['close'].rolling(window=self.period).mean()
                bb_std = df['close'].rolling(window=self.period).std()
                df['bb_upper'] = df['bb_middle'] + (bb_std * self.std_dev)
                df['bb_lower'] = df['bb_middle'] - (bb_std * self.std_dev)
                
                current = df.iloc[-1]
                current_position = self.positions.get(symbol, 'none')
                
                # 价格触及下轨，买入信号
                if (current['close'] <= current['bb_lower'] and 
                    current_position != 'long'):
                    
                    signals.append({
                        'symbol': symbol,
                        'action': 'buy',
                        'reason': 'Price at Lower Bollinger Band',
                        'price': current['close'],
                        'bb_lower': current['bb_lower'],
                        'bb_middle': current['bb_middle'],
                        'bb_upper': current['bb_upper']
                    })
                    self.positions[symbol] = 'long'
                
                # 价格触及上轨，卖出信号
                elif (current['close'] >= current['bb_upper'] and 
                      current_position == 'long'):
                    
                    signals.append({
                        'symbol': symbol,
                        'action': 'sell',
                        'reason': 'Price at Upper Bollinger Band',
                        'price': current['close'],
                        'bb_lower': current['bb_lower'],
                        'bb_middle': current['bb_middle'],
                        'bb_upper': current['bb_upper']
                    })
                    self.positions[symbol] = 'none'
        
        except Exception as e:
            logger.error(f"Failed to generate Bollinger Bands signals: {e}")
        
        return signals


class SmartMoneyFollowStrategy(BaseStrategy):
    """Smart Money跟随策略"""
    
    def __init__(self, min_confidence: float = 0.7, min_impact_score: float = 0.6):
        super().__init__(
            name="Smart Money Follow",
            params={
                'min_confidence': min_confidence,
                'min_impact_score': min_impact_score
            }
        )
        self.min_confidence = min_confidence
        self.min_impact_score = min_impact_score
        self.positions = {}
    
    async def generate_signals(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """生成Smart Money跟随信号"""
        signals = []
        
        try:
            # 注意：这里需要Smart Money数据，在实际回测中可能需要模拟
            # 模拟Smart Money信号
            # 在实际实现中，这里会查询Smart Money活动数据
            mock_smart_money_signals = [
                {
                    'symbol': 'BTC',
                    'direction': 'buy',
                    'confidence': 0.8,
                    'impact_score': 0.7,
                    'volume_usd': 5000000
                },
                {
                    'symbol': 'ETH',
                    'direction': 'sell',
                    'confidence': 0.75,
                    'impact_score': 0.65,
                    'volume_usd': 3000000
                }
            ]
            
            for sm_signal in mock_smart_money_signals:
                symbol = sm_signal['symbol']
                
                # 检查信号质量
                if (sm_signal['confidence'] >= self.min_confidence and 
                    sm_signal['impact_score'] >= self.min_impact_score):
                    
                    current_position = self.positions.get(symbol, 'none')
                    
                    if sm_signal['direction'] == 'buy' and current_position != 'long':
                        signals.append({
                            'symbol': symbol,
                            'action': 'buy',
                            'reason': 'Smart Money Buy Signal',
                            'confidence': sm_signal['confidence'],
                            'impact_score': sm_signal['impact_score'],
                            'volume_usd': sm_signal['volume_usd']
                        })
                        self.positions[symbol] = 'long'
                    
                    elif sm_signal['direction'] == 'sell' and current_position == 'long':
                        signals.append({
                            'symbol': symbol,
                            'action': 'sell',
                            'reason': 'Smart Money Sell Signal',
                            'confidence': sm_signal['confidence'],
                            'impact_score': sm_signal['impact_score'],
                            'volume_usd': sm_signal['volume_usd']
                        })
                        self.positions[symbol] = 'none'
        
        except Exception as e:
            logger.error(f"Failed to generate Smart Money signals: {e}")
        
        return signals


class CompositeStrategy(BaseStrategy):
    """综合策略"""
    
    def __init__(self, strategies: List[BaseStrategy], weights: List[float] = None):
        super().__init__(
            name="Composite Strategy",
            params={
                'strategies': [s.name for s in strategies],
                'weights': weights or [1.0] * len(strategies)
            }
        )
        self.strategies = strategies
        self.weights = weights or [1.0] * len(strategies)
        self.positions = {}
    
    async def generate_signals(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """生成综合信号"""
        signals = []
        
        try:
            # 收集所有子策略的信号
            all_signals = {}
            
            for strategy in self.strategies:
                strategy_signals = await strategy.generate_signals(data)
                
                for signal in strategy_signals:
                    symbol = signal['symbol']
                    action = signal['action']
                    
                    if symbol not in all_signals:
                        all_signals[symbol] = {'buy': [], 'sell': []}
                    
                    all_signals[symbol][action].append({
                        'strategy': strategy.name,
                        'signal': signal
                    })
            
            # 综合分析信号
            for symbol, symbol_signals in all_signals.items():
                buy_signals = symbol_signals['buy']
                sell_signals = symbol_signals['sell']
                
                # 计算加权投票
                buy_weight = sum(self.weights[i] for i, s in enumerate(self.strategies) 
                               if any(sig['strategy'] == s.name for sig in buy_signals))
                sell_weight = sum(self.weights[i] for i, s in enumerate(self.strategies) 
                                if any(sig['strategy'] == s.name for sig in sell_signals))
                
                current_position = self.positions.get(symbol, 'none')
                
                # 买入信号占优
                if buy_weight > sell_weight and current_position != 'long':
                    signals.append({
                        'symbol': symbol,
                        'action': 'buy',
                        'reason': f'Composite Buy (weight: {buy_weight:.2f})',
                        'buy_signals': len(buy_signals),
                        'sell_signals': len(sell_signals),
                        'contributing_strategies': [sig['strategy'] for sig in buy_signals]
                    })
                    self.positions[symbol] = 'long'
                
                # 卖出信号占优
                elif sell_weight > buy_weight and current_position == 'long':
                    signals.append({
                        'symbol': symbol,
                        'action': 'sell',
                        'reason': f'Composite Sell (weight: {sell_weight:.2f})',
                        'buy_signals': len(buy_signals),
                        'sell_signals': len(sell_signals),
                        'contributing_strategies': [sig['strategy'] for sig in sell_signals]
                    })
                    self.positions[symbol] = 'none'
        
        except Exception as e:
            logger.error(f"Failed to generate composite signals: {e}")
        
        return signals


class BuyAndHoldStrategy(BaseStrategy):
    """买入持有策略"""
    
    def __init__(self):
        super().__init__(name="Buy and Hold")
        self.initialized = False
    
    async def generate_signals(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """生成买入持有信号"""
        signals = []
        
        if not self.initialized:
            # 只在第一次执行时买入所有资产
            current_prices = data['current_prices']
            
            for symbol in current_prices.keys():
                signals.append({
                    'symbol': symbol,
                    'action': 'buy',
                    'reason': 'Buy and Hold Initial Purchase',
                    'price': current_prices[symbol]
                })
            
            self.initialized = True
        
        return signals


# 策略工厂
class StrategyFactory:
    """策略工厂"""
    
    @staticmethod
    def create_strategy(strategy_name: str, **kwargs) -> BaseStrategy:
        """创建策略实例"""
        strategies = {
            'ma_crossover': MovingAverageCrossoverStrategy,
            'rsi_mean_reversion': RSIMeanReversionStrategy,
            'bollinger_bands': BollingerBandsStrategy,
            'smart_money_follow': SmartMoneyFollowStrategy,
            'buy_and_hold': BuyAndHoldStrategy
        }
        
        if strategy_name not in strategies:
            raise ValueError(f"Unknown strategy: {strategy_name}")
        
        return strategies[strategy_name](**kwargs)
    
    @staticmethod
    def get_available_strategies() -> List[str]:
        """获取可用策略列表"""
        return [
            'ma_crossover',
            'rsi_mean_reversion', 
            'bollinger_bands',
            'smart_money_follow',
            'buy_and_hold'
        ]
    
    @staticmethod
    def create_composite_strategy(strategy_configs: List[Dict[str, Any]]) -> CompositeStrategy:
        """创建综合策略"""
        strategies = []
        weights = []
        
        for config in strategy_configs:
            strategy_name = config['name']
            strategy_params = config.get('params', {})
            weight = config.get('weight', 1.0)
            
            strategy = StrategyFactory.create_strategy(strategy_name, **strategy_params)
            strategies.append(strategy)
            weights.append(weight)
        
        return CompositeStrategy(strategies, weights)
