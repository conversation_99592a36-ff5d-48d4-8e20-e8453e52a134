"""
设置API路由
提供系统配置、用户偏好和通知设置功能
"""
from fastapi import APIRouter, HTTPException, Query
from typing import Dict, List, Optional, Any
from datetime import datetime, timezone
import json

from src.utils.logger import get_logger

logger = get_logger(__name__)

router = APIRouter()

# 模拟设置存储
settings_storage = {
    'user_preferences': {
        'theme': 'dark',
        'language': 'zh-CN',
        'timezone': 'Asia/Shanghai',
        'default_symbols': ['BTC', 'ETH', 'ADA'],
        'chart_preferences': {
            'default_timeframe': '1d',
            'show_volume': True,
            'show_indicators': ['SMA', 'RSI'],
            'chart_type': 'candlestick'
        }
    },
    'notification_settings': {
        'email_enabled': True,
        'telegram_enabled': False,
        'discord_enabled': False,
        'price_alerts': True,
        'whale_alerts': True,
        'smart_money_alerts': True,
        'system_alerts': True,
        'alert_frequency': 'immediate',
        'quiet_hours': {
            'enabled': False,
            'start_time': '22:00',
            'end_time': '08:00'
        }
    },
    'trading_settings': {
        'default_position_size': 0.1,
        'max_position_size': 0.2,
        'risk_per_trade': 0.02,
        'auto_stop_loss': True,
        'default_stop_loss_pct': 0.05,
        'auto_take_profit': True,
        'default_take_profit_pct': 0.10,
        'slippage_tolerance': 0.005
    },
    'monitoring_settings': {
        'whale_threshold_usd': 1000000,
        'anomaly_sensitivity': 'medium',
        'correlation_threshold': 0.8,
        'volatility_threshold': 0.05,
        'volume_spike_threshold': 2.0
    }
}


@router.get("/user-preferences")
async def get_user_preferences():
    """获取用户偏好设置"""
    try:
        return {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'preferences': settings_storage['user_preferences']
        }
    
    except Exception as e:
        logger.error(f"Failed to get user preferences: {e}")
        raise HTTPException(status_code=500, detail="Failed to get user preferences")


@router.put("/user-preferences")
async def update_user_preferences(preferences: dict):
    """更新用户偏好设置"""
    try:
        # 验证和更新设置
        valid_themes = ['light', 'dark', 'auto']
        valid_languages = ['zh-CN', 'en-US', 'ja-JP']
        valid_timezones = ['Asia/Shanghai', 'UTC', 'America/New_York', 'Europe/London']
        
        if 'theme' in preferences and preferences['theme'] not in valid_themes:
            raise HTTPException(status_code=400, detail="Invalid theme")
        
        if 'language' in preferences and preferences['language'] not in valid_languages:
            raise HTTPException(status_code=400, detail="Invalid language")
        
        if 'timezone' in preferences and preferences['timezone'] not in valid_timezones:
            raise HTTPException(status_code=400, detail="Invalid timezone")
        
        # 更新设置
        settings_storage['user_preferences'].update(preferences)
        
        return {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'status': 'success',
            'updated_preferences': settings_storage['user_preferences']
        }
    
    except Exception as e:
        logger.error(f"Failed to update user preferences: {e}")
        raise HTTPException(status_code=500, detail="Failed to update user preferences")


@router.get("/notification-settings")
async def get_notification_settings():
    """获取通知设置"""
    try:
        return {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'notification_settings': settings_storage['notification_settings']
        }
    
    except Exception as e:
        logger.error(f"Failed to get notification settings: {e}")
        raise HTTPException(status_code=500, detail="Failed to get notification settings")


@router.put("/notification-settings")
async def update_notification_settings(settings: dict):
    """更新通知设置"""
    try:
        # 验证设置
        valid_frequencies = ['immediate', 'hourly', 'daily']
        
        if 'alert_frequency' in settings and settings['alert_frequency'] not in valid_frequencies:
            raise HTTPException(status_code=400, detail="Invalid alert frequency")
        
        # 更新设置
        settings_storage['notification_settings'].update(settings)
        
        return {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'status': 'success',
            'updated_settings': settings_storage['notification_settings']
        }
    
    except Exception as e:
        logger.error(f"Failed to update notification settings: {e}")
        raise HTTPException(status_code=500, detail="Failed to update notification settings")


@router.get("/trading-settings")
async def get_trading_settings():
    """获取交易设置"""
    try:
        return {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'trading_settings': settings_storage['trading_settings']
        }
    
    except Exception as e:
        logger.error(f"Failed to get trading settings: {e}")
        raise HTTPException(status_code=500, detail="Failed to get trading settings")


@router.put("/trading-settings")
async def update_trading_settings(settings: dict):
    """更新交易设置"""
    try:
        # 验证设置
        if 'default_position_size' in settings:
            if not 0 < settings['default_position_size'] <= 1:
                raise HTTPException(status_code=400, detail="Invalid default position size")
        
        if 'max_position_size' in settings:
            if not 0 < settings['max_position_size'] <= 1:
                raise HTTPException(status_code=400, detail="Invalid max position size")
        
        if 'risk_per_trade' in settings:
            if not 0 < settings['risk_per_trade'] <= 0.1:
                raise HTTPException(status_code=400, detail="Invalid risk per trade")
        
        # 更新设置
        settings_storage['trading_settings'].update(settings)
        
        return {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'status': 'success',
            'updated_settings': settings_storage['trading_settings']
        }
    
    except Exception as e:
        logger.error(f"Failed to update trading settings: {e}")
        raise HTTPException(status_code=500, detail="Failed to update trading settings")


@router.get("/monitoring-settings")
async def get_monitoring_settings():
    """获取监控设置"""
    try:
        return {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'monitoring_settings': settings_storage['monitoring_settings']
        }
    
    except Exception as e:
        logger.error(f"Failed to get monitoring settings: {e}")
        raise HTTPException(status_code=500, detail="Failed to get monitoring settings")


@router.put("/monitoring-settings")
async def update_monitoring_settings(settings: dict):
    """更新监控设置"""
    try:
        # 验证设置
        valid_sensitivities = ['low', 'medium', 'high']
        
        if 'anomaly_sensitivity' in settings and settings['anomaly_sensitivity'] not in valid_sensitivities:
            raise HTTPException(status_code=400, detail="Invalid anomaly sensitivity")
        
        if 'whale_threshold_usd' in settings:
            if settings['whale_threshold_usd'] < 100000:
                raise HTTPException(status_code=400, detail="Whale threshold too low")
        
        # 更新设置
        settings_storage['monitoring_settings'].update(settings)
        
        return {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'status': 'success',
            'updated_settings': settings_storage['monitoring_settings']
        }
    
    except Exception as e:
        logger.error(f"Failed to update monitoring settings: {e}")
        raise HTTPException(status_code=500, detail="Failed to update monitoring settings")


@router.get("/all-settings")
async def get_all_settings():
    """获取所有设置"""
    try:
        return {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'settings': settings_storage
        }
    
    except Exception as e:
        logger.error(f"Failed to get all settings: {e}")
        raise HTTPException(status_code=500, detail="Failed to get all settings")


@router.post("/export-settings")
async def export_settings():
    """导出设置"""
    try:
        export_data = {
            'export_timestamp': datetime.now(timezone.utc).isoformat(),
            'version': '1.0',
            'settings': settings_storage
        }
        
        return {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'export_data': export_data,
            'filename': f"onchain_analytics_settings_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        }
    
    except Exception as e:
        logger.error(f"Failed to export settings: {e}")
        raise HTTPException(status_code=500, detail="Failed to export settings")


@router.post("/import-settings")
async def import_settings(import_data: dict):
    """导入设置"""
    try:
        # 验证导入数据
        if 'settings' not in import_data:
            raise HTTPException(status_code=400, detail="Invalid import data")
        
        imported_settings = import_data['settings']
        
        # 验证设置结构
        required_sections = ['user_preferences', 'notification_settings', 'trading_settings', 'monitoring_settings']
        
        for section in required_sections:
            if section not in imported_settings:
                raise HTTPException(status_code=400, detail=f"Missing section: {section}")
        
        # 更新设置
        settings_storage.update(imported_settings)
        
        return {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'status': 'success',
            'imported_sections': list(imported_settings.keys()),
            'message': 'Settings imported successfully'
        }
    
    except Exception as e:
        logger.error(f"Failed to import settings: {e}")
        raise HTTPException(status_code=500, detail="Failed to import settings")


@router.post("/reset-settings")
async def reset_settings(section: Optional[str] = None):
    """重置设置"""
    try:
        default_settings = {
            'user_preferences': {
                'theme': 'dark',
                'language': 'zh-CN',
                'timezone': 'Asia/Shanghai',
                'default_symbols': ['BTC', 'ETH', 'ADA'],
                'chart_preferences': {
                    'default_timeframe': '1d',
                    'show_volume': True,
                    'show_indicators': ['SMA', 'RSI'],
                    'chart_type': 'candlestick'
                }
            },
            'notification_settings': {
                'email_enabled': True,
                'telegram_enabled': False,
                'discord_enabled': False,
                'price_alerts': True,
                'whale_alerts': True,
                'smart_money_alerts': True,
                'system_alerts': True,
                'alert_frequency': 'immediate',
                'quiet_hours': {
                    'enabled': False,
                    'start_time': '22:00',
                    'end_time': '08:00'
                }
            },
            'trading_settings': {
                'default_position_size': 0.1,
                'max_position_size': 0.2,
                'risk_per_trade': 0.02,
                'auto_stop_loss': True,
                'default_stop_loss_pct': 0.05,
                'auto_take_profit': True,
                'default_take_profit_pct': 0.10,
                'slippage_tolerance': 0.005
            },
            'monitoring_settings': {
                'whale_threshold_usd': 1000000,
                'anomaly_sensitivity': 'medium',
                'correlation_threshold': 0.8,
                'volatility_threshold': 0.05,
                'volume_spike_threshold': 2.0
            }
        }
        
        if section:
            if section not in default_settings:
                raise HTTPException(status_code=400, detail="Invalid section")
            
            settings_storage[section] = default_settings[section]
            reset_sections = [section]
        else:
            settings_storage.update(default_settings)
            reset_sections = list(default_settings.keys())
        
        return {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'status': 'success',
            'reset_sections': reset_sections,
            'message': 'Settings reset to defaults'
        }
    
    except Exception as e:
        logger.error(f"Failed to reset settings: {e}")
        raise HTTPException(status_code=500, detail="Failed to reset settings")


@router.get("/system-info")
async def get_system_info():
    """获取系统信息"""
    try:
        import platform
        import psutil
        
        system_info = {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'system': {
                'platform': platform.system(),
                'platform_version': platform.version(),
                'architecture': platform.architecture()[0],
                'processor': platform.processor(),
                'python_version': platform.python_version()
            },
            'application': {
                'name': 'OnChain Analytics Platform',
                'version': '1.0.0',
                'build_date': '2024-01-01',
                'environment': 'development'
            },
            'resources': {
                'cpu_count': psutil.cpu_count(),
                'memory_total_gb': round(psutil.virtual_memory().total / (1024**3), 2),
                'disk_total_gb': round(psutil.disk_usage('/').total / (1024**3), 2),
                'disk_free_gb': round(psutil.disk_usage('/').free / (1024**3), 2)
            }
        }
        
        return system_info
    
    except Exception as e:
        logger.error(f"Failed to get system info: {e}")
        # 返回基本信息
        return {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'application': {
                'name': 'OnChain Analytics Platform',
                'version': '1.0.0',
                'environment': 'development'
            },
            'error': 'Could not retrieve detailed system information'
        }
