"""
Smart Money信号生成器
基于Smart Money活动生成交易信号
"""
import asyncio
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timezone, timedelta
from collections import defaultdict, Counter
from dataclasses import dataclass

from .smart_money import SmartMoneyAnalyzer
from src.database.repositories import TradingSignalRepository, SmartMoneyActivityRepository
from src.utils.logger import get_logger
from config.settings import TRADING_CONFIG

logger = get_logger(__name__)


@dataclass
class SmartMoneySignal:
    """Smart Money信号"""
    signal_type: str
    symbol: str
    action: str  # buy, sell, hold
    strength: float  # 0-1
    confidence: float  # 0-1
    price_target: Optional[float]
    stop_loss: Optional[float]
    time_horizon: str  # short, medium, long
    source_addresses: List[str]
    supporting_data: Dict[str, Any]
    generated_at: datetime


class SmartMoneySignalGenerator:
    """Smart Money信号生成器"""
    
    def __init__(self):
        self.smart_money_analyzer = None
        self.smart_money_repo = SmartMoneyActivityRepository()
        self.signal_repo = TradingSignalRepository()
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.smart_money_analyzer = await SmartMoneyAnalyzer().__aenter__()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.smart_money_analyzer:
            await self.smart_money_analyzer.__aexit__(exc_type, exc_val, exc_tb)
    
    async def generate_signals(self, hours: int = 24) -> List[SmartMoneySignal]:
        """生成Smart Money信号"""
        try:
            logger.info(f"Generating Smart Money signals for the last {hours} hours")
            
            # 获取最近的Smart Money活动
            recent_activities = self.smart_money_repo.get_recent_activities(hours=hours, limit=1000)
            
            if not recent_activities:
                logger.info("No recent Smart Money activities found")
                return []
            
            # 按代币分组活动
            token_activities = self._group_activities_by_token(recent_activities)
            
            signals = []
            
            for token_symbol, activities in token_activities.items():
                try:
                    # 分析代币的Smart Money活动模式
                    signal = await self._analyze_token_activity_pattern(token_symbol, activities)
                    
                    if signal and signal.strength >= 0.3:  # 只保留强度足够的信号
                        signals.append(signal)
                        
                        # 存储信号到数据库
                        await self._store_signal(signal)
                        
                        logger.info(f"Generated {signal.action} signal for {token_symbol} "
                                  f"(strength: {signal.strength:.2f}, confidence: {signal.confidence:.2f})")
                
                except Exception as e:
                    logger.error(f"Failed to analyze activities for {token_symbol}: {e}")
                    continue
            
            # 按强度排序
            signals.sort(key=lambda x: x.strength, reverse=True)
            
            logger.info(f"Generated {len(signals)} Smart Money signals")
            return signals
        
        except Exception as e:
            logger.error(f"Failed to generate Smart Money signals: {e}")
            raise
    
    def _group_activities_by_token(self, activities: List[Any]) -> Dict[str, List[Any]]:
        """按代币分组活动"""
        token_groups = defaultdict(list)
        
        for activity in activities:
            # 获取代币符号，如果没有则使用地址
            token_symbol = getattr(activity, 'token_symbol', None) or \
                          getattr(activity, 'token_address', 'UNKNOWN')
            token_groups[token_symbol].append(activity)
        
        return dict(token_groups)
    
    async def _analyze_token_activity_pattern(self, token_symbol: str, 
                                            activities: List[Any]) -> Optional[SmartMoneySignal]:
        """分析代币活动模式"""
        try:
            if len(activities) < 2:  # 需要至少2个活动才能分析模式
                return None
            
            # 统计买入和卖出活动
            buy_activities = [a for a in activities if a.action_type == 'buy']
            sell_activities = [a for a in activities if a.action_type == 'sell']
            
            # 计算活动统计
            total_buy_volume = sum(getattr(a, 'usd_amount', 0) for a in buy_activities)
            total_sell_volume = sum(getattr(a, 'usd_amount', 0) for a in sell_activities)
            
            # 统计参与的Smart Money地址数量
            unique_addresses = len(set(getattr(a, 'address', '') for a in activities))
            
            # 计算平均置信度
            avg_confidence = sum(getattr(a, 'confidence_score', 0) for a in activities) / len(activities)
            
            # 分析时间模式
            time_pattern = self._analyze_time_pattern(activities)
            
            # 生成信号
            signal = self._generate_signal_from_pattern(
                token_symbol, buy_activities, sell_activities,
                total_buy_volume, total_sell_volume, unique_addresses,
                avg_confidence, time_pattern
            )
            
            return signal
        
        except Exception as e:
            logger.error(f"Failed to analyze token activity pattern for {token_symbol}: {e}")
            return None
    
    def _analyze_time_pattern(self, activities: List[Any]) -> Dict[str, Any]:
        """分析时间模式"""
        try:
            # 按小时分组活动
            hourly_activities = defaultdict(int)
            
            for activity in activities:
                created_at = getattr(activity, 'created_at', datetime.now(timezone.utc))
                hour = created_at.hour
                hourly_activities[hour] += 1
            
            # 计算活动集中度
            total_activities = len(activities)
            max_hourly_activities = max(hourly_activities.values()) if hourly_activities else 0
            concentration = max_hourly_activities / total_activities if total_activities > 0 else 0
            
            # 计算活动趋势
            sorted_activities = sorted(activities, key=lambda x: getattr(x, 'created_at', datetime.now()))
            
            # 简单的趋势分析：比较前半段和后半段的活动类型
            mid_point = len(sorted_activities) // 2
            early_activities = sorted_activities[:mid_point]
            late_activities = sorted_activities[mid_point:]
            
            early_buys = sum(1 for a in early_activities if a.action_type == 'buy')
            late_buys = sum(1 for a in late_activities if a.action_type == 'buy')
            
            trend = 'increasing_buys' if late_buys > early_buys else \
                   'decreasing_buys' if late_buys < early_buys else 'stable'
            
            return {
                'concentration': concentration,
                'trend': trend,
                'activity_span_hours': (sorted_activities[-1].created_at - sorted_activities[0].created_at).total_seconds() / 3600,
                'peak_hour': max(hourly_activities.keys(), key=hourly_activities.get) if hourly_activities else 0
            }
        
        except Exception as e:
            logger.error(f"Failed to analyze time pattern: {e}")
            return {}
    
    def _generate_signal_from_pattern(self, token_symbol: str, buy_activities: List[Any],
                                    sell_activities: List[Any], total_buy_volume: float,
                                    total_sell_volume: float, unique_addresses: int,
                                    avg_confidence: float, time_pattern: Dict[str, Any]) -> SmartMoneySignal:
        """从模式生成信号"""
        try:
            # 计算买卖比例
            total_volume = total_buy_volume + total_sell_volume
            buy_ratio = total_buy_volume / total_volume if total_volume > 0 else 0
            
            # 确定信号方向
            if buy_ratio > 0.7:  # 70%以上是买入
                action = 'buy'
                base_strength = buy_ratio
            elif buy_ratio < 0.3:  # 30%以下是买入（即70%以上是卖出）
                action = 'sell'
                base_strength = 1 - buy_ratio
            else:
                action = 'hold'
                base_strength = 0.5
            
            # 调整强度基于其他因素
            strength_multipliers = {
                'address_count': min(unique_addresses / 10, 1.0),  # 更多地址参与增加强度
                'volume': min(total_volume / 1000000, 1.0),  # 更大交易量增加强度
                'confidence': avg_confidence,  # 平均置信度
                'time_concentration': time_pattern.get('concentration', 0.5)  # 时间集中度
            }
            
            # 计算最终强度
            final_strength = base_strength
            for multiplier in strength_multipliers.values():
                final_strength *= (0.8 + 0.4 * multiplier)  # 乘数在0.8-1.2之间
            
            final_strength = min(final_strength, 1.0)
            
            # 计算置信度
            confidence_factors = [
                avg_confidence,
                min(unique_addresses / 5, 1.0),  # 5个以上地址参与得满分
                min(len(buy_activities + sell_activities) / 10, 1.0),  # 10个以上活动得满分
                time_pattern.get('concentration', 0.5)
            ]
            
            confidence = sum(confidence_factors) / len(confidence_factors)
            
            # 确定时间范围
            activity_span = time_pattern.get('activity_span_hours', 24)
            if activity_span <= 4:
                time_horizon = 'short'
            elif activity_span <= 24:
                time_horizon = 'medium'
            else:
                time_horizon = 'long'
            
            # 计算价格目标和止损（简化版本）
            price_target = None
            stop_loss = None
            
            if action == 'buy':
                # 基于历史Smart Money表现设置目标
                price_target_pct = 0.15 * final_strength  # 强度越高目标越高
                stop_loss_pct = 0.05
            elif action == 'sell':
                price_target_pct = -0.10 * final_strength
                stop_loss_pct = 0.03
            
            # 收集支持数据
            supporting_data = {
                'buy_volume_usd': total_buy_volume,
                'sell_volume_usd': total_sell_volume,
                'buy_ratio': buy_ratio,
                'unique_addresses': unique_addresses,
                'total_activities': len(buy_activities + sell_activities),
                'avg_confidence': avg_confidence,
                'time_pattern': time_pattern,
                'strength_multipliers': strength_multipliers
            }
            
            # 获取参与的地址列表
            source_addresses = list(set(getattr(a, 'address', '') for a in buy_activities + sell_activities))
            
            return SmartMoneySignal(
                signal_type='smart_money_follow',
                symbol=token_symbol,
                action=action,
                strength=final_strength,
                confidence=confidence,
                price_target=price_target,
                stop_loss=stop_loss,
                time_horizon=time_horizon,
                source_addresses=source_addresses,
                supporting_data=supporting_data,
                generated_at=datetime.now(timezone.utc)
            )
        
        except Exception as e:
            logger.error(f"Failed to generate signal from pattern: {e}")
            return None
    
    async def _store_signal(self, signal: SmartMoneySignal) -> None:
        """存储信号到数据库"""
        try:
            signal_data = {
                'signal_type': signal.signal_type,
                'symbol': signal.symbol,
                'action': signal.action,
                'strength': signal.strength,
                'confidence': signal.confidence,
                'price_target': signal.price_target,
                'stop_loss': signal.stop_loss,
                'time_horizon': signal.time_horizon,
                'source_data': {
                    'source_addresses': signal.source_addresses,
                    'supporting_data': signal.supporting_data
                },
                'is_active': True
            }
            
            self.signal_repo.create(**signal_data)
            logger.debug(f"Stored Smart Money signal for {signal.symbol}")
        
        except Exception as e:
            logger.error(f"Failed to store signal: {e}")
    
    async def get_top_smart_money_moves(self, hours: int = 24, limit: int = 10) -> List[Dict[str, Any]]:
        """获取顶级Smart Money动向"""
        try:
            logger.info(f"Getting top Smart Money moves for the last {hours} hours")
            
            # 获取最近活动
            recent_activities = self.smart_money_repo.get_recent_activities(hours=hours, limit=500)
            
            # 按代币和动作类型分组统计
            token_stats = defaultdict(lambda: {
                'buy_volume': 0,
                'sell_volume': 0,
                'buy_count': 0,
                'sell_count': 0,
                'unique_addresses': set(),
                'avg_confidence': 0,
                'activities': []
            })
            
            for activity in recent_activities:
                token_symbol = getattr(activity, 'token_symbol', 'UNKNOWN')
                stats = token_stats[token_symbol]
                
                if activity.action_type == 'buy':
                    stats['buy_volume'] += getattr(activity, 'usd_amount', 0)
                    stats['buy_count'] += 1
                else:
                    stats['sell_volume'] += getattr(activity, 'usd_amount', 0)
                    stats['sell_count'] += 1
                
                stats['unique_addresses'].add(getattr(activity, 'address', ''))
                stats['activities'].append(activity)
            
            # 计算每个代币的影响分数
            top_moves = []
            for token_symbol, stats in token_stats.items():
                total_volume = stats['buy_volume'] + stats['sell_volume']
                if total_volume < 10000:  # 过滤小额交易
                    continue
                
                # 计算影响分数
                volume_score = min(total_volume / 1000000, 1.0)  # 100万美元得满分
                address_score = min(len(stats['unique_addresses']) / 10, 1.0)  # 10个地址得满分
                activity_score = min((stats['buy_count'] + stats['sell_count']) / 20, 1.0)  # 20个活动得满分
                
                impact_score = (volume_score + address_score + activity_score) / 3
                
                # 确定主要动向
                buy_ratio = stats['buy_volume'] / total_volume if total_volume > 0 else 0
                if buy_ratio > 0.7:
                    direction = 'strong_buy'
                elif buy_ratio > 0.6:
                    direction = 'buy'
                elif buy_ratio < 0.3:
                    direction = 'strong_sell'
                elif buy_ratio < 0.4:
                    direction = 'sell'
                else:
                    direction = 'mixed'
                
                top_moves.append({
                    'token_symbol': token_symbol,
                    'direction': direction,
                    'impact_score': impact_score,
                    'total_volume_usd': total_volume,
                    'buy_volume_usd': stats['buy_volume'],
                    'sell_volume_usd': stats['sell_volume'],
                    'buy_ratio': buy_ratio,
                    'unique_addresses': len(stats['unique_addresses']),
                    'total_activities': stats['buy_count'] + stats['sell_count'],
                    'sample_addresses': list(stats['unique_addresses'])[:5]  # 前5个地址作为样本
                })
            
            # 按影响分数排序并返回前N个
            top_moves.sort(key=lambda x: x['impact_score'], reverse=True)
            
            logger.info(f"Found {len(top_moves[:limit])} top Smart Money moves")
            return top_moves[:limit]
        
        except Exception as e:
            logger.error(f"Failed to get top Smart Money moves: {e}")
            return []
