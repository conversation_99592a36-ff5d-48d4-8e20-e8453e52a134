"""
巨鲸交易监控器
实时监控巨鲸交易活动并生成预警
"""
import asyncio
from typing import Dict, List, Optional, Any, Tu<PERSON>
from datetime import datetime, timezone, timedelta
from dataclasses import dataclass
import json

from .whale_detector import WhaleDetector, WhaleTransaction, AlertLevel
from src.database.repositories import WhaleAlertRepository, TransactionRepository
from src.database.data_service import DataService
from src.utils.logger import get_logger
from config.settings import MONITORING_CONFIG

logger = get_logger(__name__)


@dataclass
class MonitoringRule:
    """监控规则"""
    name: str
    description: str
    min_amount_usd: float
    alert_level: AlertLevel
    conditions: Dict[str, Any]
    enabled: bool = True


@dataclass
class FlowAnalysis:
    """资金流向分析"""
    direction: str  # inflow, outflow, internal
    source_type: str  # exchange, defi, wallet, unknown
    destination_type: str
    amount_usd: float
    impact_score: float
    market_implications: List[str]


class WhaleMonitor:
    """巨鲸监控器"""
    
    def __init__(self):
        self.whale_detector = None
        self.whale_alert_repo = WhaleAlertRepository()
        self.transaction_repo = TransactionRepository()
        self.data_service = None
        
        # 监控规则
        self.monitoring_rules = self._initialize_monitoring_rules()
        
        # 已知地址分类
        self.address_categories = self._load_address_categories()
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.whale_detector = await WhaleDetector().__aenter__()
        self.data_service = await DataService().__aenter__()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.whale_detector:
            await self.whale_detector.__aexit__(exc_type, exc_val, exc_tb)
        if self.data_service:
            await self.data_service.__aexit__(exc_type, exc_val, exc_tb)
    
    def _initialize_monitoring_rules(self) -> List[MonitoringRule]:
        """初始化监控规则"""
        return [
            MonitoringRule(
                name="mega_transaction",
                description="超大额交易监控",
                min_amount_usd=10000000,  # 1000万美元
                alert_level=AlertLevel.CRITICAL,
                conditions={
                    "single_transaction": True,
                    "exclude_known_exchanges": False
                }
            ),
            MonitoringRule(
                name="exchange_outflow",
                description="交易所大额流出",
                min_amount_usd=5000000,  # 500万美元
                alert_level=AlertLevel.HIGH,
                conditions={
                    "from_exchange": True,
                    "to_wallet": True
                }
            ),
            MonitoringRule(
                name="exchange_inflow",
                description="交易所大额流入",
                min_amount_usd=5000000,
                alert_level=AlertLevel.HIGH,
                conditions={
                    "from_wallet": True,
                    "to_exchange": True
                }
            ),
            MonitoringRule(
                name="whale_accumulation",
                description="巨鲸累积行为",
                min_amount_usd=2000000,  # 200万美元
                alert_level=AlertLevel.MEDIUM,
                conditions={
                    "accumulation_pattern": True,
                    "time_window_hours": 24
                }
            ),
            MonitoringRule(
                name="whale_distribution",
                description="巨鲸分发行为",
                min_amount_usd=2000000,
                alert_level=AlertLevel.MEDIUM,
                conditions={
                    "distribution_pattern": True,
                    "time_window_hours": 24
                }
            ),
            MonitoringRule(
                name="dormant_whale_activation",
                description="休眠巨鲸激活",
                min_amount_usd=1000000,  # 100万美元
                alert_level=AlertLevel.HIGH,
                conditions={
                    "dormant_period_days": 180,
                    "first_transaction_after_dormancy": True
                }
            )
        ]
    
    def _load_address_categories(self) -> Dict[str, Dict[str, Any]]:
        """加载地址分类"""
        return {
            'exchanges': {
                '******************************************': {'name': 'Binance', 'type': 'cex'},
                '******************************************': {'name': 'Binance 2', 'type': 'cex'},
                '******************************************': {'name': 'FTX', 'type': 'cex'},
                '******************************************': {'name': 'Crypto.com', 'type': 'cex'},
            },
            'defi': {
                '******************************************': {'name': 'Aave', 'type': 'lending'},
                '******************************************': {'name': 'Uniswap V3', 'type': 'dex'},
            },
            'known_whales': {
                '******************************************': {'name': 'Vitalik Buterin', 'type': 'founder'},
            }
        }
    
    async def monitor_whale_transactions(self, hours: int = 1) -> List[WhaleTransaction]:
        """监控巨鲸交易"""
        try:
            logger.info(f"Monitoring whale transactions for the last {hours} hours")
            
            # 获取大额交易
            large_transactions = self.transaction_repo.get_large_transactions(
                min_value_usd=MONITORING_CONFIG['whale_threshold']['usdt'],
                hours=hours,
                limit=500
            )
            
            whale_transactions = []
            
            for tx in large_transactions:
                try:
                    # 分析交易
                    whale_tx = await self._analyze_whale_transaction(tx)
                    
                    if whale_tx:
                        whale_transactions.append(whale_tx)
                        
                        # 生成预警
                        await self._generate_whale_alert(whale_tx)
                        
                        logger.info(f"Detected whale transaction: {whale_tx.tx_hash} "
                                  f"(${whale_tx.value_usd:,.0f}, level: {whale_tx.alert_level.value})")
                
                except Exception as e:
                    logger.error(f"Failed to analyze transaction {tx.tx_hash}: {e}")
                    continue
            
            logger.info(f"Monitored {len(whale_transactions)} whale transactions")
            return whale_transactions
        
        except Exception as e:
            logger.error(f"Failed to monitor whale transactions: {e}")
            raise
    
    async def _analyze_whale_transaction(self, tx: Any) -> Optional[WhaleTransaction]:
        """分析巨鲸交易"""
        try:
            # 确定交易类型
            transaction_type = await self._classify_transaction_type(tx)
            
            # 计算预警级别
            alert_level = self._calculate_alert_level(tx, transaction_type)
            
            # 计算影响分数
            impact_score = self._calculate_impact_score(tx, transaction_type)
            
            # 获取市场背景
            market_context = await self._get_market_context(tx)
            
            return WhaleTransaction(
                tx_hash=tx.tx_hash,
                from_address=tx.from_address,
                to_address=tx.to_address or '',
                value_usd=tx.value_usd,
                token_symbol=tx.token_symbol or 'ETH',
                transaction_type=transaction_type,
                timestamp=tx.block_timestamp,
                alert_level=alert_level,
                impact_score=impact_score,
                market_context=market_context
            )
        
        except Exception as e:
            logger.error(f"Failed to analyze whale transaction: {e}")
            return None
    
    async def _classify_transaction_type(self, tx: Any) -> str:
        """分类交易类型"""
        try:
            from_category = self._get_address_category(tx.from_address)
            to_category = self._get_address_category(tx.to_address) if tx.to_address else 'unknown'
            
            # 基于地址类型分类
            if from_category == 'exchange' and to_category == 'wallet':
                return 'exchange_withdrawal'
            elif from_category == 'wallet' and to_category == 'exchange':
                return 'exchange_deposit'
            elif from_category == 'exchange' and to_category == 'exchange':
                return 'exchange_transfer'
            elif from_category == 'defi' or to_category == 'defi':
                return 'defi_interaction'
            elif from_category == 'wallet' and to_category == 'wallet':
                return 'wallet_transfer'
            elif tx.to_address is None:
                return 'contract_creation'
            else:
                return 'unknown'
        
        except Exception as e:
            logger.error(f"Failed to classify transaction type: {e}")
            return 'unknown'
    
    def _get_address_category(self, address: str) -> str:
        """获取地址类别"""
        if not address:
            return 'unknown'
        
        address = address.lower()
        
        for category, addresses in self.address_categories.items():
            if address in [addr.lower() for addr in addresses.keys()]:
                return category
        
        return 'wallet'
    
    def _calculate_alert_level(self, tx: Any, transaction_type: str) -> AlertLevel:
        """计算预警级别"""
        try:
            value_usd = tx.value_usd
            
            # 基于金额的基础级别
            if value_usd >= 50000000:  # 5000万美元
                base_level = AlertLevel.CRITICAL
            elif value_usd >= 10000000:  # 1000万美元
                base_level = AlertLevel.HIGH
            elif value_usd >= 5000000:  # 500万美元
                base_level = AlertLevel.MEDIUM
            else:
                base_level = AlertLevel.LOW
            
            # 基于交易类型调整
            type_adjustments = {
                'exchange_withdrawal': 1.2,  # 交易所提币更重要
                'exchange_deposit': 1.1,     # 交易所充币较重要
                'dormant_activation': 1.5,   # 休眠地址激活很重要
                'whale_accumulation': 1.3,   # 巨鲸累积重要
                'whale_distribution': 1.3,   # 巨鲸分发重要
            }
            
            adjustment = type_adjustments.get(transaction_type, 1.0)
            
            # 应用调整（简化处理）
            if adjustment > 1.2 and base_level == AlertLevel.MEDIUM:
                return AlertLevel.HIGH
            elif adjustment > 1.4 and base_level == AlertLevel.HIGH:
                return AlertLevel.CRITICAL
            
            return base_level
        
        except Exception as e:
            logger.error(f"Failed to calculate alert level: {e}")
            return AlertLevel.LOW
    
    def _calculate_impact_score(self, tx: Any, transaction_type: str) -> float:
        """计算影响分数"""
        try:
            # 基于金额的基础分数
            value_score = min(tx.value_usd / 100000000, 1.0)  # 1亿美元为满分
            
            # 基于交易类型的权重
            type_weights = {
                'exchange_withdrawal': 0.9,
                'exchange_deposit': 0.8,
                'whale_accumulation': 0.7,
                'whale_distribution': 0.7,
                'dormant_activation': 1.0,
                'defi_interaction': 0.6,
                'wallet_transfer': 0.5
            }
            
            type_weight = type_weights.get(transaction_type, 0.5)
            
            # 基于时间的权重（市场开盘时间影响更大）
            hour = tx.block_timestamp.hour
            time_weight = 1.2 if 8 <= hour <= 20 else 0.8  # 工作时间权重更高
            
            impact_score = value_score * type_weight * time_weight
            
            return min(impact_score, 1.0)
        
        except Exception as e:
            logger.error(f"Failed to calculate impact score: {e}")
            return 0.0
    
    async def _get_market_context(self, tx: Any) -> Dict[str, Any]:
        """获取市场背景信息"""
        try:
            # 简化的市场背景信息
            context = {
                'timestamp': tx.block_timestamp,
                'market_hours': 'open' if 8 <= tx.block_timestamp.hour <= 20 else 'closed',
                'day_of_week': tx.block_timestamp.strftime('%A'),
                'gas_price_level': 'normal',  # 简化处理
                'market_trend': 'neutral'     # 简化处理
            }
            
            # 可以在这里添加更多市场数据
            # 比如当前价格、交易量、波动率等
            
            return context
        
        except Exception as e:
            logger.error(f"Failed to get market context: {e}")
            return {}
    
    async def _generate_whale_alert(self, whale_tx: WhaleTransaction) -> None:
        """生成巨鲸预警"""
        try:
            # 检查是否已存在相同的预警
            existing_alert = self.whale_alert_repo.get_by_id(whale_tx.tx_hash)  # 需要实现这个方法
            
            if existing_alert:
                return
            
            # 确定预警类型
            alert_type = self._determine_alert_type(whale_tx)
            
            # 确定交易所名称（如果涉及）
            exchange_name = self._get_exchange_name(whale_tx.from_address, whale_tx.to_address)
            
            # 创建预警记录
            alert_data = {
                'tx_hash': whale_tx.tx_hash,
                'alert_type': alert_type,
                'from_address': whale_tx.from_address,
                'to_address': whale_tx.to_address,
                'amount_usd': whale_tx.value_usd,
                'token_symbol': whale_tx.token_symbol,
                'exchange_name': exchange_name,
                'alert_level': whale_tx.alert_level.value,
                'is_processed': False,
                'notification_sent': False
            }
            
            self.whale_alert_repo.create(**alert_data)
            logger.debug(f"Generated whale alert for transaction {whale_tx.tx_hash}")
        
        except Exception as e:
            logger.error(f"Failed to generate whale alert: {e}")
    
    def _determine_alert_type(self, whale_tx: WhaleTransaction) -> str:
        """确定预警类型"""
        if whale_tx.transaction_type == 'exchange_withdrawal':
            return 'exchange_outflow'
        elif whale_tx.transaction_type == 'exchange_deposit':
            return 'exchange_inflow'
        elif whale_tx.value_usd >= 10000000:
            return 'large_transfer'
        else:
            return 'whale_movement'
    
    def _get_exchange_name(self, from_address: str, to_address: str) -> Optional[str]:
        """获取交易所名称"""
        for address, info in self.address_categories.get('exchanges', {}).items():
            if address.lower() in [from_address.lower(), to_address.lower()]:
                return info['name']
        return None
    
    async def analyze_fund_flows(self, hours: int = 24) -> List[FlowAnalysis]:
        """分析资金流向"""
        try:
            logger.info(f"Analyzing fund flows for the last {hours} hours")
            
            # 获取大额交易
            large_transactions = self.transaction_repo.get_large_transactions(
                min_value_usd=1000000,  # 100万美元以上
                hours=hours,
                limit=1000
            )
            
            # 按流向分类
            flows = {
                'exchange_inflows': [],
                'exchange_outflows': [],
                'defi_flows': [],
                'whale_transfers': []
            }
            
            for tx in large_transactions:
                flow_type = self._classify_flow_type(tx)
                if flow_type in flows:
                    flows[flow_type].append(tx)
            
            # 分析每种流向
            flow_analyses = []
            
            for flow_type, transactions in flows.items():
                if transactions:
                    analysis = self._analyze_flow_pattern(flow_type, transactions)
                    flow_analyses.append(analysis)
            
            logger.info(f"Analyzed {len(flow_analyses)} fund flow patterns")
            return flow_analyses
        
        except Exception as e:
            logger.error(f"Failed to analyze fund flows: {e}")
            return []
    
    def _classify_flow_type(self, tx: Any) -> str:
        """分类资金流向类型"""
        from_category = self._get_address_category(tx.from_address)
        to_category = self._get_address_category(tx.to_address) if tx.to_address else 'unknown'
        
        if from_category == 'wallet' and to_category == 'exchange':
            return 'exchange_inflows'
        elif from_category == 'exchange' and to_category == 'wallet':
            return 'exchange_outflows'
        elif from_category == 'defi' or to_category == 'defi':
            return 'defi_flows'
        elif from_category == 'wallet' and to_category == 'wallet':
            return 'whale_transfers'
        else:
            return 'other'
    
    def _analyze_flow_pattern(self, flow_type: str, transactions: List[Any]) -> FlowAnalysis:
        """分析流向模式"""
        try:
            total_amount = sum(tx.value_usd for tx in transactions)
            
            # 确定方向
            direction_map = {
                'exchange_inflows': 'inflow',
                'exchange_outflows': 'outflow',
                'defi_flows': 'defi',
                'whale_transfers': 'internal'
            }
            direction = direction_map.get(flow_type, 'unknown')
            
            # 确定源和目标类型
            if flow_type == 'exchange_inflows':
                source_type = 'wallet'
                destination_type = 'exchange'
            elif flow_type == 'exchange_outflows':
                source_type = 'exchange'
                destination_type = 'wallet'
            elif flow_type == 'defi_flows':
                source_type = 'wallet'
                destination_type = 'defi'
            else:
                source_type = 'wallet'
                destination_type = 'wallet'
            
            # 计算影响分数
            impact_score = min(total_amount / 100000000, 1.0)  # 1亿美元为满分
            
            # 市场含义
            market_implications = self._get_market_implications(flow_type, total_amount, len(transactions))
            
            return FlowAnalysis(
                direction=direction,
                source_type=source_type,
                destination_type=destination_type,
                amount_usd=total_amount,
                impact_score=impact_score,
                market_implications=market_implications
            )
        
        except Exception as e:
            logger.error(f"Failed to analyze flow pattern: {e}")
            return FlowAnalysis('unknown', 'unknown', 'unknown', 0, 0, [])
    
    def _get_market_implications(self, flow_type: str, amount: float, count: int) -> List[str]:
        """获取市场含义"""
        implications = []
        
        if flow_type == 'exchange_inflows' and amount > 50000000:
            implications.append("大量资金流入交易所，可能面临抛售压力")
        elif flow_type == 'exchange_outflows' and amount > 50000000:
            implications.append("大量资金流出交易所，可能表明长期持有意图")
        
        if count > 10:
            implications.append("多笔大额交易，显示机构级别活动")
        
        if amount > 100000000:
            implications.append("超大额资金流动，可能影响市场价格")
        
        return implications
