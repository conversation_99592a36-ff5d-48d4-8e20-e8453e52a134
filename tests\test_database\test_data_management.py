"""
数据管理系统测试
测试数据存储、查询和管理功能
"""
import pytest
import asyncio
from datetime import datetime, timezone, timedelta
from unittest.mock import Mock, patch, MagicMock

from src.database.repositories import (
    AddressRepository, TransactionRepository, TokenRepository,
    MarketDataRepository, SmartMoneyActivityRepository
)
from src.database.data_service import DataService
from src.database.data_manager import DataManager
from src.database.models import Address, Transaction, Token


class TestRepositories:
    """仓库类测试"""
    
    def test_address_repository_creation(self):
        """测试地址仓库创建"""
        repo = AddressRepository()
        assert repo.model_class == Address
    
    @patch('src.database.database.db_manager')
    def test_address_repository_get_by_address(self, mock_db_manager):
        """测试根据地址获取记录"""
        # 模拟数据库会话
        mock_session = MagicMock()
        mock_db_manager.get_session.return_value.__enter__.return_value = mock_session
        
        # 模拟查询结果
        mock_address = Mock()
        mock_address.address = '0x123...'
        mock_session.query.return_value.filter.return_value.first.return_value = mock_address
        
        repo = AddressRepository()
        result = repo.get_by_address('0x123...')
        
        assert result == mock_address
        mock_session.query.assert_called_once()
    
    def test_transaction_repository_creation(self):
        """测试交易仓库创建"""
        repo = TransactionRepository()
        assert repo.model_class == Transaction
    
    def test_token_repository_creation(self):
        """测试代币仓库创建"""
        repo = TokenRepository()
        assert repo.model_class == Token


class TestDataService:
    """数据服务测试"""
    
    @pytest.mark.asyncio
    async def test_data_service_initialization(self):
        """测试数据服务初始化"""
        with patch('src.database.data_service.DataCoordinator'):
            async with DataService() as service:
                assert service.address_repo is not None
                assert service.transaction_repo is not None
                assert service.token_repo is not None
    
    @pytest.mark.asyncio
    async def test_calculate_address_stats(self):
        """测试地址统计计算"""
        service = DataService()
        
        # 模拟数据
        balance_data = {
            'address': '0x123...',
            'balance_eth': 10.5
        }
        
        transactions = [
            {
                'hash': '0xabc...',
                'timestamp': datetime.now(timezone.utc),
                'from_address': '0x123...',
                'to_address': '0x456...',
                'value_eth': 1.0
            },
            {
                'hash': '0xdef...',
                'timestamp': datetime.now(timezone.utc) - timedelta(days=1),
                'from_address': '0x456...',
                'to_address': '0x123...',
                'value_eth': 2.0
            }
        ]
        
        token_transfers = [
            {
                'hash': '0xghi...',
                'token_symbol': 'USDT',
                'value_formatted': 1000.0
            }
        ]
        
        stats = service._calculate_address_stats(balance_data, transactions, token_transfers)
        
        assert stats['total_transactions'] == 2
        assert stats['token_transfer_count'] == 1
        assert stats['avg_tx_value_eth'] == 1.5
        assert 'is_whale' in stats
        assert 'is_smart_money' in stats
    
    @pytest.mark.asyncio
    async def test_sync_address_data_new_address(self):
        """测试同步新地址数据"""
        with patch('src.database.data_service.DataCoordinator') as mock_coordinator_class:
            # 模拟数据协调器
            mock_coordinator = MagicMock()
            mock_coordinator_class.return_value.__aenter__.return_value = mock_coordinator
            
            mock_coordinator.get_address_balance.return_value = {
                'address': '0x123...',
                'balance_eth': 5.0
            }
            mock_coordinator.get_address_transactions.return_value = []
            mock_coordinator.get_token_transfers.return_value = []
            
            # 模拟仓库
            with patch.object(DataService, '__init__', lambda x: None):
                service = DataService()
                service.address_repo = Mock()
                service.transaction_repo = Mock()
                service.token_repo = Mock()
                service.data_coordinator = mock_coordinator
                
                service.address_repo.get_by_address.return_value = None  # 新地址
                service.address_repo.update_address_stats.return_value = Mock()
                
                result = await service.sync_address_data('0x123...')
                
                assert 'address' in result
                assert result['address'] == '0x123...'
                assert 'balance_eth' in result


class TestDataManager:
    """数据管理器测试"""
    
    @pytest.mark.asyncio
    async def test_data_manager_initialization(self):
        """测试数据管理器初始化"""
        with patch('src.database.data_manager.DataService'):
            async with DataManager() as manager:
                assert manager.data_service is not None
    
    @pytest.mark.asyncio
    async def test_sync_watchlist_addresses(self):
        """测试同步监控地址列表"""
        with patch('src.database.data_manager.DataService') as mock_service_class:
            mock_service = MagicMock()
            mock_service_class.return_value.__aenter__.return_value = mock_service
            
            # 模拟同步结果
            mock_service.sync_address_data.return_value = {
                'address': '0x123...',
                'status': 'success'
            }
            
            with patch.object(DataManager, '__init__', lambda x: None):
                manager = DataManager()
                manager.data_service = mock_service
                
                addresses = ['0x123...', '0x456...', '0x789...']
                result = await manager.sync_watchlist_addresses(addresses, batch_size=2)
                
                assert result['total_addresses'] == 3
                assert result['successful_syncs'] >= 0
                assert 'duration_seconds' in result
    
    @pytest.mark.asyncio
    async def test_cleanup_old_data(self):
        """测试清理旧数据"""
        with patch('src.database.data_manager.db_manager') as mock_db_manager:
            mock_db_manager.execute_raw_sql.return_value = []
            
            with patch.object(DataManager, '__init__', lambda x: None):
                manager = DataManager()
                
                retention_days = {
                    'transactions': 30,
                    'market_data': 90
                }
                
                result = await manager.cleanup_old_data(retention_days)
                
                assert 'cleanup_started' in result
                assert 'cleanup_completed' in result
                assert 'tables_cleaned' in result
                assert 'total_deleted_records' in result
    
    @pytest.mark.asyncio
    async def test_get_system_stats(self):
        """测试获取系统统计"""
        with patch('src.database.data_manager.db_manager') as mock_db_manager:
            # 模拟健康检查
            mock_db_manager.health_check.return_value = {
                'database': True,
                'redis': True
            }
            
            # 模拟表统计查询
            mock_db_manager.execute_raw_sql.return_value = [{'count': 100}]
            
            with patch.object(DataManager, '__init__', lambda x: None):
                manager = DataManager()
                
                with patch.object(manager, '_get_recent_activity_stats', return_value={}):
                    stats = await manager.get_system_stats()
                    
                    assert 'timestamp' in stats
                    assert 'database_health' in stats
                    assert 'table_stats' in stats
                    assert 'recent_activity' in stats


class TestIntegration:
    """集成测试"""
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_full_data_flow(self):
        """测试完整数据流程"""
        # 这个测试需要真实的数据库连接，通常在开发环境中跳过
        pytest.skip("Integration test requires real database connection")
        
        async with DataManager() as manager:
            # 测试地址同步
            test_address = '******************************************'  # Vitalik
            
            sync_result = await manager.sync_watchlist_addresses([test_address])
            assert sync_result['total_addresses'] == 1
            
            # 测试市场数据同步
            market_sync_result = await manager.sync_market_data_batch([['bitcoin', 'ethereum']])
            assert market_sync_result['total_batches'] == 1
            
            # 测试系统统计
            stats = await manager.get_system_stats()
            assert 'database_health' in stats
            assert stats['database_health']['database'] == True


# 性能测试
class TestPerformance:
    """性能测试"""
    
    @pytest.mark.performance
    @pytest.mark.asyncio
    async def test_batch_address_sync_performance(self):
        """测试批量地址同步性能"""
        # 生成测试地址列表
        test_addresses = [f'0x{i:040x}' for i in range(100)]
        
        with patch('src.database.data_manager.DataService') as mock_service_class:
            mock_service = MagicMock()
            mock_service_class.return_value.__aenter__.return_value = mock_service
            
            # 模拟快速响应
            mock_service.sync_address_data.return_value = {'status': 'success'}
            
            with patch.object(DataManager, '__init__', lambda x: None):
                manager = DataManager()
                manager.data_service = mock_service
                
                start_time = datetime.now()
                result = await manager.sync_watchlist_addresses(
                    test_addresses, 
                    batch_size=10, 
                    delay_seconds=0.1
                )
                end_time = datetime.now()
                
                duration = (end_time - start_time).total_seconds()
                
                # 性能断言
                assert duration < 30  # 应该在30秒内完成
                assert result['successful_syncs'] == 100
                
                # 计算吞吐量
                throughput = len(test_addresses) / duration
                print(f"Address sync throughput: {throughput:.2f} addresses/second")


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v", "-m", "not integration and not performance"])
