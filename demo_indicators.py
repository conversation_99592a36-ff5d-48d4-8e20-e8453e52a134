"""
技术指标计算引擎演示脚本
演示链上指标、技术指标和综合分析功能
"""
import asyncio
import sys
from pathlib import Path
from datetime import datetime, timezone

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.indicators.onchain_indicators import OnChainIndicatorCalculator
from src.indicators.technical_indicators import TechnicalIndicatorCalculator
from src.indicators.indicator_engine import IndicatorEngine
from src.utils.logger import get_logger

logger = get_logger(__name__)


async def demo_onchain_indicators():
    """演示链上指标计算"""
    print("\n=== 链上指标计算演示 ===")
    
    try:
        async with OnChainIndicatorCalculator() as calculator:
            print("--- 计算链上指标 ---")
            
            # 计算BTC的链上指标
            symbols = ['BTC', 'ETH']
            
            for symbol in symbols:
                try:
                    print(f"\n{symbol} 链上指标:")
                    
                    # 计算最近30天的指标
                    indicators = await calculator.calculate_all_indicators(symbol, days=30)
                    
                    if indicators:
                        latest = indicators[-1]
                        print(f"  日期: {latest.date.strftime('%Y-%m-%d')}")
                        print(f"  价格: ${latest.price_usd:,.2f}")
                        print(f"  市值: ${latest.market_cap:,.0f}")
                        print(f"  活跃地址: {latest.active_addresses:,}")
                        print(f"  新地址: {latest.new_addresses:,}")
                        print(f"  交易数量: {latest.transaction_count:,}")
                        print(f"  交易量: ${latest.transaction_volume:,.0f}")
                        print(f"  MVRV比率: {latest.mvrv_ratio:.3f}")
                        print(f"  NVT比率: {latest.nvt_ratio:.2f}")
                        print(f"  RVT比率: {latest.rvt_ratio:.2f}")
                        print(f"  巨鲸集中度: {latest.whale_concentration:.3f}")
                        print(f"  交易所流动比率: {latest.exchange_flow_ratio:.3f}")
                        print(f"  恐惧贪婪指数: {latest.fear_greed_index:.1f}")
                        
                        # 持有者分布
                        print(f"  持有者分布:")
                        for holder_type, ratio in latest.holder_distribution.items():
                            print(f"    {holder_type}: {ratio:.1%}")
                    else:
                        print(f"  暂无 {symbol} 的链上数据")
                
                except Exception as e:
                    print(f"  计算 {symbol} 链上指标失败: {e}")
            
            # 计算特殊指标
            print(f"\n--- 特殊链上指标 ---")
            
            for symbol in symbols:
                try:
                    # MVRV Z-Score
                    mvrv_z = await calculator.calculate_mvrv_z_score(symbol, days=365)
                    print(f"{symbol} MVRV Z-Score: {mvrv_z:.3f}")
                    
                    if mvrv_z > 7:
                        print(f"  -> 极度高估，考虑卖出")
                    elif mvrv_z > 3.5:
                        print(f"  -> 高估，谨慎操作")
                    elif mvrv_z < -1.5:
                        print(f"  -> 低估，考虑买入")
                    else:
                        print(f"  -> 估值合理")
                    
                    # 网络增长
                    growth = await calculator.calculate_network_growth(symbol, days=30)
                    print(f"{symbol} 网络增长:")
                    print(f"  增长率: {growth['growth_rate']:.2%}")
                    print(f"  趋势: {growth['trend']}")
                    
                    # 流通速度
                    velocity = await calculator.calculate_velocity(symbol, days=30)
                    print(f"{symbol} 流通速度: {velocity:.4f}")
                
                except Exception as e:
                    print(f"  计算 {symbol} 特殊指标失败: {e}")
    
    except Exception as e:
        print(f"链上指标演示失败: {e}")


async def demo_technical_indicators():
    """演示技术指标计算"""
    print("\n=== 技术指标计算演示 ===")
    
    try:
        calculator = TechnicalIndicatorCalculator()
        
        print("--- 计算技术指标 ---")
        
        symbols = ['BTC', 'ETH']
        
        for symbol in symbols:
            try:
                print(f"\n{symbol} 技术指标:")
                
                # 计算最近50天的技术指标
                indicators = await calculator.calculate_all_indicators(symbol, days=50)
                
                if indicators:
                    latest = indicators[-1]
                    print(f"  时间: {latest.timestamp.strftime('%Y-%m-%d %H:%M')}")
                    print(f"  价格: ${latest.price:,.2f}")
                    
                    # 移动平均线
                    print(f"  移动平均线:")
                    print(f"    SMA20: ${latest.sma_20:,.2f}")
                    print(f"    SMA50: ${latest.sma_50:,.2f}")
                    print(f"    SMA200: ${latest.sma_200:,.2f}")
                    print(f"    EMA12: ${latest.ema_12:,.2f}")
                    print(f"    EMA26: ${latest.ema_26:,.2f}")
                    
                    # 动量指标
                    print(f"  动量指标:")
                    print(f"    RSI(14): {latest.rsi_14:.2f}")
                    print(f"    MACD: {latest.macd:.2f}")
                    print(f"    MACD信号: {latest.macd_signal:.2f}")
                    print(f"    MACD柱状图: {latest.macd_histogram:.2f}")
                    
                    # 布林带
                    print(f"  布林带:")
                    print(f"    上轨: ${latest.bollinger_upper:,.2f}")
                    print(f"    中轨: ${latest.bollinger_middle:,.2f}")
                    print(f"    下轨: ${latest.bollinger_lower:,.2f}")
                    
                    # 支撑阻力
                    print(f"  支撑阻力:")
                    print(f"    支撑位: ${latest.support_level:,.2f}")
                    print(f"    阻力位: ${latest.resistance_level:,.2f}")
                    
                    # 信号
                    print(f"  交易信号:")
                    print(f"    趋势信号: {latest.trend_signal}")
                    print(f"    动量信号: {latest.momentum_signal}")
                    print(f"    综合信号: {latest.overall_signal}")
                else:
                    print(f"  暂无 {symbol} 的技术指标数据")
                
                # 获取当前信号
                current_signals = await calculator.get_current_signals(symbol)
                if current_signals:
                    print(f"  当前信号摘要:")
                    print(f"    趋势: {current_signals.get('trend_signal', 'N/A')}")
                    print(f"    动量: {current_signals.get('momentum_signal', 'N/A')}")
                    print(f"    RSI状态: {current_signals.get('rsi', 'N/A')}")
                    print(f"    MACD状态: {current_signals.get('macd_signal', 'N/A')}")
                    print(f"    成交量: {current_signals.get('volume_signal', 'N/A')}")
                
                # 检测技术形态
                patterns = await calculator.detect_patterns(symbol, days=30)
                if patterns:
                    print(f"  检测到的技术形态:")
                    for pattern in patterns[-5:]:  # 显示最近5个形态
                        print(f"    {pattern['type']}: {pattern['signal']} "
                              f"({pattern['timestamp'].strftime('%m-%d %H:%M')})")
                
                # 计算枢轴点
                pivot_points = await calculator.calculate_pivot_points(symbol)
                if pivot_points:
                    print(f"  枢轴点:")
                    print(f"    枢轴: ${pivot_points.get('pivot', 0):,.2f}")
                    print(f"    阻力1: ${pivot_points.get('resistance_1', 0):,.2f}")
                    print(f"    支撑1: ${pivot_points.get('support_1', 0):,.2f}")
            
            except Exception as e:
                print(f"  计算 {symbol} 技术指标失败: {e}")
    
    except Exception as e:
        print(f"技术指标演示失败: {e}")


async def demo_composite_analysis():
    """演示综合分析"""
    print("\n=== 综合指标分析演示 ===")
    
    try:
        async with IndicatorEngine() as engine:
            print("--- 综合分析 ---")
            
            symbols = ['BTC', 'ETH']
            
            # 单个代币分析
            for symbol in symbols:
                try:
                    print(f"\n{symbol} 综合分析:")
                    
                    signal = await engine.analyze_symbol(symbol, days=30)
                    
                    print(f"  时间: {signal.timestamp.strftime('%Y-%m-%d %H:%M')}")
                    print(f"  技术信号: {signal.technical_signal} (强度: {signal.technical_strength:.3f})")
                    print(f"  链上信号: {signal.onchain_signal} (强度: {signal.onchain_strength:.3f})")
                    print(f"  综合信号: {signal.composite_signal} (强度: {signal.composite_strength:.3f})")
                    print(f"  置信度: {signal.confidence:.3f}")
                    print(f"  投资建议: {signal.recommendation}")
                    print(f"  风险级别: {signal.risk_level}")
                    print(f"  时间范围: {signal.time_horizon}")
                    
                    # 技术数据摘要
                    tech_data = signal.technical_data
                    if tech_data:
                        print(f"  技术数据:")
                        print(f"    趋势: {tech_data.get('trend_signal', 'N/A')}")
                        print(f"    RSI: {tech_data.get('rsi', 'N/A')}")
                        print(f"    价格: ${tech_data.get('price', 0):,.2f}")
                        print(f"    支撑: ${tech_data.get('support_level', 0):,.2f}")
                        print(f"    阻力: ${tech_data.get('resistance_level', 0):,.2f}")
                    
                    # 链上数据摘要
                    onchain_data = signal.onchain_data
                    if onchain_data:
                        print(f"  链上数据:")
                        print(f"    MVRV: {onchain_data.get('mvrv_ratio', 'N/A')}")
                        print(f"    NVT: {onchain_data.get('nvt_ratio', 'N/A')}")
                        print(f"    活跃地址: {onchain_data.get('active_addresses', 'N/A'):,}")
                        print(f"    恐惧贪婪: {onchain_data.get('fear_greed_index', 'N/A')}")
                
                except Exception as e:
                    print(f"  {symbol} 综合分析失败: {e}")
            
            # 批量分析
            print(f"\n--- 批量分析 ---")
            try:
                batch_symbols = ['BTC', 'ETH', 'ADA', 'DOT']
                signals = await engine.batch_analyze(batch_symbols, days=7)
                
                print(f"成功分析 {len(signals)} 个代币:")
                
                for signal in signals:
                    print(f"  {signal.symbol}: {signal.composite_signal} "
                          f"(强度: {signal.composite_strength:.3f}, "
                          f"建议: {signal.recommendation})")
            
            except Exception as e:
                print(f"批量分析失败: {e}")
            
            # 市场概览
            print(f"\n--- 市场概览 ---")
            try:
                overview = await engine.get_market_overview(['BTC', 'ETH', 'ADA', 'DOT'])
                
                if overview:
                    print(f"分析代币数: {overview.get('total_symbols', 0)}")
                    print(f"市场情绪: {overview.get('market_sentiment', 'N/A')}")
                    print(f"平均强度: {overview.get('average_strength', 0):.3f}")
                    print(f"平均置信度: {overview.get('average_confidence', 0):.3f}")
                    
                    # 信号分布
                    signal_dist = overview.get('signal_distribution', {})
                    print(f"信号分布:")
                    for signal_type, count in signal_dist.items():
                        print(f"  {signal_type}: {count}")
                    
                    # 建议分布
                    rec_dist = overview.get('recommendation_distribution', {})
                    print(f"建议分布:")
                    for rec_type, count in rec_dist.items():
                        print(f"  {rec_type}: {count}")
                    
                    # 顶级看涨
                    top_bullish = overview.get('top_bullish', [])
                    if top_bullish:
                        print(f"顶级看涨:")
                        for signal in top_bullish[:3]:
                            print(f"  {signal.symbol}: {signal.recommendation} "
                                  f"(强度: {signal.composite_strength:.3f})")
                    
                    # 顶级看跌
                    top_bearish = overview.get('top_bearish', [])
                    if top_bearish:
                        print(f"顶级看跌:")
                        for signal in top_bearish[:3]:
                            print(f"  {signal.symbol}: {signal.recommendation} "
                                  f"(强度: {signal.composite_strength:.3f})")
            
            except Exception as e:
                print(f"市场概览失败: {e}")
    
    except Exception as e:
        print(f"综合分析演示失败: {e}")


async def demo_indicator_interpretation():
    """演示指标解读"""
    print("\n=== 指标解读演示 ===")
    
    try:
        print("--- 技术指标解读 ---")
        
        # RSI解读
        rsi_values = [25, 45, 65, 85]
        for rsi in rsi_values:
            if rsi < 30:
                interpretation = "超卖，可能反弹"
            elif rsi > 70:
                interpretation = "超买，可能回调"
            else:
                interpretation = "正常范围"
            print(f"RSI {rsi}: {interpretation}")
        
        # MVRV解读
        print(f"\n--- 链上指标解读 ---")
        mvrv_values = [0.8, 1.2, 2.5, 4.0]
        for mvrv in mvrv_values:
            if mvrv < 1.0:
                interpretation = "低估，历史底部区域"
            elif mvrv < 2.0:
                interpretation = "合理估值"
            elif mvrv < 3.5:
                interpretation = "高估，注意风险"
            else:
                interpretation = "严重高估，历史顶部区域"
            print(f"MVRV {mvrv}: {interpretation}")
        
        # NVT解读
        nvt_values = [30, 80, 120, 200]
        for nvt in nvt_values:
            if nvt < 50:
                interpretation = "网络价值合理，看涨"
            elif nvt < 100:
                interpretation = "网络价值适中"
            elif nvt < 150:
                interpretation = "网络价值偏高，谨慎"
            else:
                interpretation = "网络价值过高，看跌"
            print(f"NVT {nvt}: {interpretation}")
        
        print(f"\n--- 综合信号解读 ---")
        
        scenarios = [
            ("技术看涨 + 链上看涨", "强烈看涨，高置信度"),
            ("技术看涨 + 链上中性", "温和看涨，中等置信度"),
            ("技术看涨 + 链上看跌", "信号冲突，低置信度，谨慎操作"),
            ("技术中性 + 链上看涨", "温和看涨，关注链上数据"),
            ("技术看跌 + 链上看跌", "强烈看跌，高置信度"),
        ]
        
        for scenario, interpretation in scenarios:
            print(f"{scenario}: {interpretation}")
    
    except Exception as e:
        print(f"指标解读演示失败: {e}")


async def main():
    """主演示函数"""
    print("📊 技术指标计算引擎演示")
    print("=" * 50)
    
    # 检查配置
    try:
        from config.settings import TRADING_CONFIG
        print("✅ 配置文件加载成功")
        
        # 检查指标配置
        indicator_config = TRADING_CONFIG.get('indicators', {})
        print(f"指标配置:")
        print(f"  RSI周期: {indicator_config.get('rsi_period', 14)}")
        print(f"  MACD快线: {indicator_config.get('macd_fast', 12)}")
        print(f"  MACD慢线: {indicator_config.get('macd_slow', 26)}")
        print(f"  布林带周期: {indicator_config.get('bollinger_period', 20)}")
    
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        print("某些功能可能无法正常工作")
    
    # 运行演示
    demos = [
        ("链上指标计算", demo_onchain_indicators),
        ("技术指标计算", demo_technical_indicators),
        ("综合指标分析", demo_composite_analysis),
        ("指标解读", demo_indicator_interpretation),
    ]
    
    for demo_name, demo_func in demos:
        try:
            print(f"\n🔄 开始 {demo_name} 演示...")
            await demo_func()
            print(f"✅ {demo_name} 演示完成")
        except Exception as e:
            print(f"❌ {demo_name} 演示失败: {e}")
            logger.error(f"{demo_name} demo failed", exc_info=True)
        
        # 在演示之间添加延迟
        await asyncio.sleep(1)
    
    print("\n🎉 所有演示完成!")
    print("=" * 50)
    print("\n💡 提示:")
    print("- 技术指标需要足够的历史数据才能准确计算")
    print("- 链上指标反映网络基本面，适合中长期分析")
    print("- 综合分析结合两种指标，提供更全面的视角")
    print("- 指标只是辅助工具，投资决策需要综合考虑多种因素")


if __name__ == "__main__":
    # 运行演示
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 演示被用户中断")
    except Exception as e:
        print(f"\n💥 演示过程中发生错误: {e}")
        logger.error("Demo failed", exc_info=True)
