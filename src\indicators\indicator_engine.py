"""
指标分析引擎
整合链上指标和技术指标，提供综合分析
"""
import asyncio
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timezone, timedelta
from dataclasses import dataclass

from .onchain_indicators import OnChainIndicatorCalculator, OnChainMetrics
from .technical_indicators import TechnicalIndicatorCalculator, TechnicalIndicators
from src.database.repositories import TradingSignalRepository
from src.utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class CompositeSignal:
    """综合信号"""
    symbol: str
    timestamp: datetime
    
    # 技术分析信号
    technical_signal: str
    technical_strength: float
    
    # 链上分析信号
    onchain_signal: str
    onchain_strength: float
    
    # 综合信号
    composite_signal: str
    composite_strength: float
    confidence: float
    
    # 支持数据
    technical_data: Dict[str, Any]
    onchain_data: Dict[str, Any]
    
    # 建议
    recommendation: str
    risk_level: str
    time_horizon: str


class IndicatorEngine:
    """指标分析引擎"""
    
    def __init__(self):
        self.onchain_calculator = OnChainIndicatorCalculator()
        self.technical_calculator = TechnicalIndicatorCalculator()
        self.signal_repo = TradingSignalRepository()
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.onchain_calculator.__aenter__()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.onchain_calculator.__aexit__(exc_type, exc_val, exc_tb)
    
    async def analyze_symbol(self, symbol: str, days: int = 30) -> CompositeSignal:
        """分析单个代币"""
        try:
            logger.info(f"Analyzing {symbol} with composite indicators")
            
            # 并行计算技术指标和链上指标
            technical_task = self.technical_calculator.calculate_all_indicators(symbol, days)
            onchain_task = self.onchain_calculator.calculate_all_indicators(symbol, days)
            
            technical_indicators, onchain_indicators = await asyncio.gather(
                technical_task, onchain_task, return_exceptions=True
            )
            
            # 处理异常
            if isinstance(technical_indicators, Exception):
                logger.error(f"Technical analysis failed: {technical_indicators}")
                technical_indicators = []
            
            if isinstance(onchain_indicators, Exception):
                logger.error(f"On-chain analysis failed: {onchain_indicators}")
                onchain_indicators = []
            
            # 生成综合信号
            composite_signal = await self._generate_composite_signal(
                symbol, technical_indicators, onchain_indicators
            )
            
            # 存储信号
            await self._store_composite_signal(composite_signal)
            
            return composite_signal
        
        except Exception as e:
            logger.error(f"Failed to analyze {symbol}: {e}")
            raise
    
    async def _generate_composite_signal(self, symbol: str, 
                                       technical_indicators: List[TechnicalIndicators],
                                       onchain_indicators: List[OnChainMetrics]) -> CompositeSignal:
        """生成综合信号"""
        try:
            timestamp = datetime.now(timezone.utc)
            
            # 分析技术指标
            technical_analysis = self._analyze_technical_indicators(technical_indicators)
            
            # 分析链上指标
            onchain_analysis = self._analyze_onchain_indicators(onchain_indicators)
            
            # 生成综合信号
            composite_signal, composite_strength, confidence = self._combine_signals(
                technical_analysis, onchain_analysis
            )
            
            # 生成建议
            recommendation, risk_level, time_horizon = self._generate_recommendation(
                composite_signal, composite_strength, confidence, technical_analysis, onchain_analysis
            )
            
            return CompositeSignal(
                symbol=symbol,
                timestamp=timestamp,
                technical_signal=technical_analysis['signal'],
                technical_strength=technical_analysis['strength'],
                onchain_signal=onchain_analysis['signal'],
                onchain_strength=onchain_analysis['strength'],
                composite_signal=composite_signal,
                composite_strength=composite_strength,
                confidence=confidence,
                technical_data=technical_analysis['data'],
                onchain_data=onchain_analysis['data'],
                recommendation=recommendation,
                risk_level=risk_level,
                time_horizon=time_horizon
            )
        
        except Exception as e:
            logger.error(f"Failed to generate composite signal: {e}")
            raise
    
    def _analyze_technical_indicators(self, indicators: List[TechnicalIndicators]) -> Dict[str, Any]:
        """分析技术指标"""
        try:
            if not indicators:
                return {
                    'signal': 'neutral',
                    'strength': 0.0,
                    'data': {}
                }
            
            latest = indicators[-1]
            
            # 计算技术信号强度
            signal_scores = []
            
            # 趋势信号评分
            if latest.trend_signal == 'bullish':
                signal_scores.append(1.0)
            elif latest.trend_signal == 'bearish':
                signal_scores.append(-1.0)
            else:
                signal_scores.append(0.0)
            
            # 动量信号评分
            if latest.momentum_signal == 'bullish':
                signal_scores.append(0.8)
            elif latest.momentum_signal == 'bearish':
                signal_scores.append(-0.8)
            else:
                signal_scores.append(0.0)
            
            # RSI评分
            if latest.rsi_14 < 30:
                signal_scores.append(0.6)  # 超卖，看涨
            elif latest.rsi_14 > 70:
                signal_scores.append(-0.6)  # 超买，看跌
            else:
                signal_scores.append(0.0)
            
            # MACD评分
            if latest.macd > latest.macd_signal:
                signal_scores.append(0.5)
            else:
                signal_scores.append(-0.5)
            
            # 计算平均信号强度
            avg_score = np.mean(signal_scores)
            strength = abs(avg_score)
            
            # 确定信号方向
            if avg_score > 0.3:
                signal = 'bullish'
            elif avg_score < -0.3:
                signal = 'bearish'
            else:
                signal = 'neutral'
            
            return {
                'signal': signal,
                'strength': strength,
                'data': {
                    'trend_signal': latest.trend_signal,
                    'momentum_signal': latest.momentum_signal,
                    'rsi': latest.rsi_14,
                    'macd': latest.macd,
                    'macd_signal': latest.macd_signal,
                    'price': latest.price,
                    'sma_20': latest.sma_20,
                    'sma_50': latest.sma_50,
                    'bollinger_upper': latest.bollinger_upper,
                    'bollinger_lower': latest.bollinger_lower,
                    'support_level': latest.support_level,
                    'resistance_level': latest.resistance_level
                }
            }
        
        except Exception as e:
            logger.error(f"Failed to analyze technical indicators: {e}")
            return {'signal': 'neutral', 'strength': 0.0, 'data': {}}
    
    def _analyze_onchain_indicators(self, indicators: List[OnChainMetrics]) -> Dict[str, Any]:
        """分析链上指标"""
        try:
            if not indicators:
                return {
                    'signal': 'neutral',
                    'strength': 0.0,
                    'data': {}
                }
            
            latest = indicators[-1]
            
            # 计算链上信号强度
            signal_scores = []
            
            # MVRV评分
            if latest.mvrv_ratio < 1.0:
                signal_scores.append(0.8)  # 低估，看涨
            elif latest.mvrv_ratio > 3.0:
                signal_scores.append(-0.8)  # 高估，看跌
            else:
                signal_scores.append(0.0)
            
            # NVT评分
            if latest.nvt_ratio < 50:
                signal_scores.append(0.6)  # 网络价值合理，看涨
            elif latest.nvt_ratio > 150:
                signal_scores.append(-0.6)  # 网络价值过高，看跌
            else:
                signal_scores.append(0.0)
            
            # 活跃地址趋势
            if len(indicators) >= 7:
                recent_addresses = [ind.active_addresses for ind in indicators[-7:]]
                earlier_addresses = [ind.active_addresses for ind in indicators[-14:-7]] if len(indicators) >= 14 else recent_addresses
                
                recent_avg = np.mean(recent_addresses)
                earlier_avg = np.mean(earlier_addresses)
                
                if recent_avg > earlier_avg * 1.1:
                    signal_scores.append(0.5)  # 活跃度增长，看涨
                elif recent_avg < earlier_avg * 0.9:
                    signal_scores.append(-0.5)  # 活跃度下降，看跌
                else:
                    signal_scores.append(0.0)
            
            # 交易所流动比率
            if latest.exchange_flow_ratio < 0.1:
                signal_scores.append(0.4)  # 流出多，看涨
            elif latest.exchange_flow_ratio > 0.3:
                signal_scores.append(-0.4)  # 流入多，看跌
            else:
                signal_scores.append(0.0)
            
            # 计算平均信号强度
            avg_score = np.mean(signal_scores) if signal_scores else 0.0
            strength = abs(avg_score)
            
            # 确定信号方向
            if avg_score > 0.3:
                signal = 'bullish'
            elif avg_score < -0.3:
                signal = 'bearish'
            else:
                signal = 'neutral'
            
            return {
                'signal': signal,
                'strength': strength,
                'data': {
                    'mvrv_ratio': latest.mvrv_ratio,
                    'nvt_ratio': latest.nvt_ratio,
                    'active_addresses': latest.active_addresses,
                    'transaction_count': latest.transaction_count,
                    'exchange_flow_ratio': latest.exchange_flow_ratio,
                    'whale_concentration': latest.whale_concentration,
                    'fear_greed_index': latest.fear_greed_index
                }
            }
        
        except Exception as e:
            logger.error(f"Failed to analyze on-chain indicators: {e}")
            return {'signal': 'neutral', 'strength': 0.0, 'data': {}}
    
    def _combine_signals(self, technical_analysis: Dict[str, Any], 
                        onchain_analysis: Dict[str, Any]) -> Tuple[str, float, float]:
        """合并技术和链上信号"""
        try:
            tech_signal = technical_analysis['signal']
            tech_strength = technical_analysis['strength']
            onchain_signal = onchain_analysis['signal']
            onchain_strength = onchain_analysis['strength']
            
            # 权重配置
            tech_weight = 0.6  # 技术分析权重
            onchain_weight = 0.4  # 链上分析权重
            
            # 信号转换为数值
            signal_values = {'bullish': 1, 'neutral': 0, 'bearish': -1}
            
            tech_value = signal_values[tech_signal] * tech_strength
            onchain_value = signal_values[onchain_signal] * onchain_strength
            
            # 加权平均
            composite_value = tech_value * tech_weight + onchain_value * onchain_weight
            composite_strength = abs(composite_value)
            
            # 确定综合信号
            if composite_value > 0.3:
                composite_signal = 'bullish'
            elif composite_value < -0.3:
                composite_signal = 'bearish'
            else:
                composite_signal = 'neutral'
            
            # 计算置信度
            # 当技术和链上信号一致时，置信度更高
            if tech_signal == onchain_signal and tech_signal != 'neutral':
                confidence = min(0.9, 0.6 + (tech_strength + onchain_strength) / 2)
            elif tech_signal != 'neutral' and onchain_signal != 'neutral' and tech_signal != onchain_signal:
                confidence = max(0.3, 0.5 - abs(tech_strength - onchain_strength) / 2)
            else:
                confidence = 0.5
            
            return composite_signal, composite_strength, confidence
        
        except Exception as e:
            logger.error(f"Failed to combine signals: {e}")
            return 'neutral', 0.0, 0.5
    
    def _generate_recommendation(self, signal: str, strength: float, confidence: float,
                               technical_analysis: Dict[str, Any], 
                               onchain_analysis: Dict[str, Any]) -> Tuple[str, str, str]:
        """生成投资建议"""
        try:
            # 基于信号强度和置信度生成建议
            if signal == 'bullish' and strength > 0.6 and confidence > 0.7:
                recommendation = 'strong_buy'
                risk_level = 'medium'
                time_horizon = 'medium_term'
            elif signal == 'bullish' and strength > 0.4:
                recommendation = 'buy'
                risk_level = 'medium'
                time_horizon = 'short_term'
            elif signal == 'bearish' and strength > 0.6 and confidence > 0.7:
                recommendation = 'strong_sell'
                risk_level = 'high'
                time_horizon = 'medium_term'
            elif signal == 'bearish' and strength > 0.4:
                recommendation = 'sell'
                risk_level = 'high'
                time_horizon = 'short_term'
            else:
                recommendation = 'hold'
                risk_level = 'low'
                time_horizon = 'long_term'
            
            # 根据技术和链上数据调整风险级别
            tech_data = technical_analysis.get('data', {})
            onchain_data = onchain_analysis.get('data', {})
            
            # 高波动率增加风险
            rsi = tech_data.get('rsi', 50)
            if rsi > 80 or rsi < 20:
                if risk_level == 'low':
                    risk_level = 'medium'
                elif risk_level == 'medium':
                    risk_level = 'high'
            
            # MVRV过高增加风险
            mvrv = onchain_data.get('mvrv_ratio', 1.0)
            if mvrv > 4.0:
                if risk_level == 'low':
                    risk_level = 'medium'
                elif risk_level == 'medium':
                    risk_level = 'high'
            
            return recommendation, risk_level, time_horizon
        
        except Exception as e:
            logger.error(f"Failed to generate recommendation: {e}")
            return 'hold', 'medium', 'medium_term'
    
    async def _store_composite_signal(self, signal: CompositeSignal) -> None:
        """存储综合信号"""
        try:
            signal_data = {
                'signal_type': 'composite_analysis',
                'symbol': signal.symbol,
                'action': signal.recommendation,
                'strength': signal.composite_strength,
                'confidence': signal.confidence,
                'time_horizon': signal.time_horizon,
                'source_data': {
                    'technical_signal': signal.technical_signal,
                    'onchain_signal': signal.onchain_signal,
                    'technical_data': signal.technical_data,
                    'onchain_data': signal.onchain_data
                },
                'is_active': True
            }
            
            self.signal_repo.create(**signal_data)
            logger.debug(f"Stored composite signal for {signal.symbol}")
        
        except Exception as e:
            logger.error(f"Failed to store composite signal: {e}")
    
    async def batch_analyze(self, symbols: List[str], days: int = 30) -> List[CompositeSignal]:
        """批量分析多个代币"""
        try:
            logger.info(f"Batch analyzing {len(symbols)} symbols")
            
            # 并行分析
            tasks = [self.analyze_symbol(symbol, days) for symbol in symbols]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 过滤异常结果
            signals = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"Failed to analyze {symbols[i]}: {result}")
                else:
                    signals.append(result)
            
            # 按综合强度排序
            signals.sort(key=lambda x: x.composite_strength, reverse=True)
            
            logger.info(f"Successfully analyzed {len(signals)} symbols")
            return signals
        
        except Exception as e:
            logger.error(f"Failed to batch analyze: {e}")
            return []
    
    async def get_market_overview(self, symbols: List[str]) -> Dict[str, Any]:
        """获取市场概览"""
        try:
            signals = await self.batch_analyze(symbols, days=7)
            
            if not signals:
                return {}
            
            # 统计信号分布
            signal_counts = {'bullish': 0, 'bearish': 0, 'neutral': 0}
            recommendation_counts = {}
            
            total_strength = 0
            total_confidence = 0
            
            for signal in signals:
                signal_counts[signal.composite_signal] += 1
                
                rec = signal.recommendation
                recommendation_counts[rec] = recommendation_counts.get(rec, 0) + 1
                
                total_strength += signal.composite_strength
                total_confidence += signal.confidence
            
            avg_strength = total_strength / len(signals)
            avg_confidence = total_confidence / len(signals)
            
            # 市场情绪
            bullish_ratio = signal_counts['bullish'] / len(signals)
            bearish_ratio = signal_counts['bearish'] / len(signals)
            
            if bullish_ratio > 0.6:
                market_sentiment = 'bullish'
            elif bearish_ratio > 0.6:
                market_sentiment = 'bearish'
            else:
                market_sentiment = 'mixed'
            
            return {
                'total_symbols': len(signals),
                'market_sentiment': market_sentiment,
                'signal_distribution': signal_counts,
                'recommendation_distribution': recommendation_counts,
                'average_strength': avg_strength,
                'average_confidence': avg_confidence,
                'top_bullish': [s for s in signals if s.composite_signal == 'bullish'][:5],
                'top_bearish': [s for s in signals if s.composite_signal == 'bearish'][:5],
                'timestamp': datetime.now(timezone.utc)
            }
        
        except Exception as e:
            logger.error(f"Failed to get market overview: {e}")
            return {}
