#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日本大宗商品价格变动深度研究报告 (1990-2024)
分析日本从90年代泡沫破裂后这三十年来大宗商品价格的变动情况
包含历史背景、经济影响和未来趋势分析
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import yfinance as yf
import requests
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class JapanCommodityResearch:
    """日本大宗商品深度研究分析器"""
    
    def __init__(self):
        self.start_date = "1985-01-01"  # 从泡沫开始前开始分析
        self.end_date = "2024-12-31"
        
        # 定义关键历史时期
        self.historical_periods = {
            "泡沫经济期": ("1985-01-01", "1989-12-31"),
            "泡沫破裂期": ("1990-01-01", "1995-12-31"),
            "失落的十年": ("1996-01-01", "2005-12-31"),
            "复苏期": ("2006-01-01", "2012-12-31"),
            "安倍经济学": ("2013-01-01", "2020-12-31"),
            "疫情后时期": ("2021-01-01", "2024-12-31")
        }
        
        # 主要大宗商品
        self.commodities = {
            "能源": {
                "WTI原油": "CL=F",
                "布伦特原油": "BZ=F",
                "天然气": "NG=F"
            },
            "贵金属": {
                "黄金": "GC=F",
                "白银": "SI=F",
                "铂金": "PL=F"
            },
            "工业金属": {
                "铜": "HG=F",
                "铝": "ALI=F",
                "镍": "NID=F",
                "锌": "ZNC=F"
            },
            "农产品": {
                "大豆": "ZS=F",
                "玉米": "ZC=F",
                "小麦": "ZW=F",
                "棉花": "CT=F",
                "糖": "SB=F"
            },
            "软商品": {
                "咖啡": "KC=F",
                "可可": "CC=F",
                "橙汁": "OJ=F"
            }
        }
        
        # 日本经济指标
        self.japan_indicators = {
            "日经225指数": "^N225",
            "日元兑美元": "JPY=X",
            "日本10年期国债收益率": "^TNX",
            "美元指数": "DX-Y.NYB"
        }
        
        self.data = {}
        
    def fetch_data(self, symbol, start_date=None, end_date=None):
        """获取数据"""
        try:
            if start_date is None:
                start_date = self.start_date
            if end_date is None:
                end_date = self.end_date
                
            ticker = yf.Ticker(symbol)
            data = ticker.history(start=start_date, end=end_date)
            
            if not data.empty:
                return data
            else:
                print(f"警告: 无法获取 {symbol} 的数据")
                return None
        except Exception as e:
            print(f"获取 {symbol} 数据时出错: {e}")
            return None
    
    def fetch_all_data(self):
        """获取所有数据"""
        print("正在获取大宗商品数据...")
        
        # 获取大宗商品数据
        for category, commodities in self.commodities.items():
            self.data[category] = {}
            for commodity_name, symbol in commodities.items():
                data = self.fetch_data(symbol)
                if data is not None:
                    self.data[category][commodity_name] = data
                    print(f"✓ 已获取 {commodity_name} 数据")
        
        # 获取日本经济指标数据
        print("\n正在获取日本经济指标数据...")
        self.data["日本经济指标"] = {}
        for indicator_name, symbol in self.japan_indicators.items():
            data = self.fetch_data(symbol)
            if data is not None:
                self.data["日本经济指标"][indicator_name] = data
                print(f"✓ 已获取 {indicator_name} 数据")
    
    def calculate_period_performance(self, data, period_name, start_date, end_date):
        """计算特定时期的表现"""
        if data is None or data.empty:
            return None
            
        period_data = data[(data.index >= start_date) & (data.index <= end_date)]
        if period_data.empty:
            return None
            
        start_price = period_data['Close'].iloc[0]
        end_price = period_data['Close'].iloc[-1]
        max_price = period_data['High'].max()
        min_price = period_data['Low'].min()
        
        return {
            "起始价格": start_price,
            "结束价格": end_price,
            "最高价格": max_price,
            "最低价格": min_price,
            "价格变化": end_price - start_price,
            "变化率(%)": ((end_price - start_price) / start_price) * 100,
            "波动率": period_data['Close'].std() / period_data['Close'].mean() * 100
        }
    
    def analyze_historical_periods(self):
        """分析历史时期表现"""
        print("\n=== 历史时期分析 ===")
        
        period_analysis = {}
        
        for period_name, (start_date, end_date) in self.historical_periods.items():
            print(f"\n--- {period_name} ({start_date} 至 {end_date}) ---")
            period_analysis[period_name] = {}
            
            for category, commodities in self.data.items():
                if category == "日本经济指标":
                    continue
                    
                period_analysis[period_name][category] = {}
                
                for commodity_name, data in commodities.items():
                    performance = self.calculate_period_performance(data, period_name, start_date, end_date)
                    if performance:
                        period_analysis[period_name][category][commodity_name] = performance
                        
                        print(f"{commodity_name}:")
                        print(f"  变化率: {performance['变化率(%)']:.2f}%")
                        print(f"  波动率: {performance['波动率']:.2f}%")
        
        return period_analysis
    
    def analyze_japan_economy_impact(self):
        """分析日本经济对大宗商品的影响"""
        print("\n=== 日本经济影响分析 ===")
        
        economy_analysis = {}
        
        if "日本经济指标" in self.data:
            for indicator_name, data in self.data["日本经济指标"].items():
                economy_analysis[indicator_name] = {}
                
                print(f"\n{indicator_name}:")
                
                for period_name, (start_date, end_date) in self.historical_periods.items():
                    performance = self.calculate_period_performance(data, period_name, start_date, end_date)
                    if performance:
                        economy_analysis[indicator_name][period_name] = performance
                        print(f"  {period_name}: {performance['变化率(%)']:.2f}%")
        
        return economy_analysis
    
    def create_comprehensive_visualizations(self):
        """创建综合可视化图表"""
        print("\n正在生成综合可视化图表...")
        
        # 1. 大宗商品价格走势对比
        self.create_price_trends_comparison()
        
        # 2. 历史时期表现热力图
        self.create_period_performance_heatmap()
        
        # 3. 日本经济指标与大宗商品相关性
        self.create_correlation_analysis()
        
        # 4. 泡沫破裂前后对比
        self.create_bubble_comparison()
    
    def create_price_trends_comparison(self):
        """创建价格走势对比图"""
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('日本大宗商品价格走势对比 (1985-2024)', fontsize=16, fontweight='bold')
        
        # 能源类
        ax1 = axes[0, 0]
        if "能源" in self.data:
            for commodity_name, data in self.data["能源"].items():
                ax1.plot(data.index, data['Close'], label=commodity_name, linewidth=2)
        ax1.set_title('能源类商品')
        ax1.set_ylabel('价格')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 贵金属
        ax2 = axes[0, 1]
        if "贵金属" in self.data:
            for commodity_name, data in self.data["贵金属"].items():
                ax2.plot(data.index, data['Close'], label=commodity_name, linewidth=2)
        ax2.set_title('贵金属')
        ax2.set_ylabel('价格')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 工业金属
        ax3 = axes[1, 0]
        if "工业金属" in self.data:
            for commodity_name, data in self.data["工业金属"].items():
                ax3.plot(data.index, data['Close'], label=commodity_name, linewidth=2)
        ax3.set_title('工业金属')
        ax3.set_ylabel('价格')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # 农产品
        ax4 = axes[1, 1]
        if "农产品" in self.data:
            for commodity_name, data in self.data["农产品"].items():
                ax4.plot(data.index, data['Close'], label=commodity_name, linewidth=2)
        ax4.set_title('农产品')
        ax4.set_ylabel('价格')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('japan_commodity_trends.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def create_period_performance_heatmap(self):
        """创建历史时期表现热力图"""
        # 收集所有商品在不同时期的表现数据
        heatmap_data = []
        
        for category, commodities in self.data.items():
            if category == "日本经济指标":
                continue
            for commodity_name, data in commodities.items():
                for period_name, (start_date, end_date) in self.historical_periods.items():
                    performance = self.calculate_period_performance(data, period_name, start_date, end_date)
                    if performance:
                        heatmap_data.append({
                            '商品': f"{category}-{commodity_name}",
                            '时期': period_name,
                            '变化率': performance['变化率(%)']
                        })
        
        if heatmap_data:
            df_heatmap = pd.DataFrame(heatmap_data)
            pivot_data = df_heatmap.pivot(index='商品', columns='时期', values='变化率')
            
            plt.figure(figsize=(14, 10))
            sns.heatmap(pivot_data, annot=True, fmt='.1f', cmap='RdYlBu_r', center=0)
            plt.title('日本大宗商品在不同历史时期的表现热力图')
            plt.tight_layout()
            plt.savefig('japan_commodity_period_heatmap.png', dpi=300, bbox_inches='tight')
            plt.show()
    
    def create_correlation_analysis(self):
        """创建相关性分析"""
        if "日本经济指标" not in self.data:
            return
            
        # 计算相关性
        correlation_data = []
        
        for category, commodities in self.data.items():
            if category == "日本经济指标":
                continue
            for commodity_name, commodity_data in commodities.items():
                for indicator_name, indicator_data in self.data["日本经济指标"].items():
                    # 对齐数据
                    common_index = commodity_data.index.intersection(indicator_data.index)
                    if len(common_index) > 30:  # 至少需要30个数据点
                        commodity_prices = commodity_data.loc[common_index, 'Close']
                        indicator_values = indicator_data.loc[common_index, 'Close']
                        
                        correlation = commodity_prices.corr(indicator_values)
                        correlation_data.append({
                            '商品': commodity_name,
                            '类别': category,
                            '经济指标': indicator_name,
                            '相关性': correlation
                        })
        
        if correlation_data:
            df_corr = pd.DataFrame(correlation_data)
            
            # 创建相关性热力图
            pivot_corr = df_corr.pivot_table(index=['类别', '商品'], 
                                           columns='经济指标', 
                                           values='相关性')
            
            plt.figure(figsize=(12, 8))
            sns.heatmap(pivot_corr, annot=True, fmt='.2f', cmap='RdYlBu_r', center=0)
            plt.title('日本大宗商品与经济指标相关性分析')
            plt.tight_layout()
            plt.savefig('japan_commodity_correlation.png', dpi=300, bbox_inches='tight')
            plt.show()
    
    def create_bubble_comparison(self):
        """创建泡沫破裂前后对比"""
        # 分析泡沫破裂前后的表现
        bubble_periods = {
            "泡沫经济期": ("1985-01-01", "1989-12-31"),
            "泡沫破裂后": ("1990-01-01", "2024-12-31")
        }
        
        comparison_data = []
        
        for category, commodities in self.data.items():
            if category == "日本经济指标":
                continue
            for commodity_name, data in commodities.items():
                for period_name, (start_date, end_date) in bubble_periods.items():
                    performance = self.calculate_period_performance(data, period_name, start_date, end_date)
                    if performance:
                        comparison_data.append({
                            '商品': commodity_name,
                            '类别': category,
                            '时期': period_name,
                            '变化率': performance['变化率(%)'],
                            '波动率': performance['波动率']
                        })
        
        if comparison_data:
            df_comp = pd.DataFrame(comparison_data)
            
            # 创建对比图
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))
            
            # 变化率对比
            pivot_change = df_comp.pivot(index=['类别', '商品'], columns='时期', values='变化率')
            pivot_change.plot(kind='bar', ax=ax1)
            ax1.set_title('泡沫破裂前后价格变化对比')
            ax1.set_ylabel('变化率 (%)')
            ax1.legend()
            ax1.tick_params(axis='x', rotation=45)
            
            # 波动率对比
            pivot_vol = df_comp.pivot(index=['类别', '商品'], columns='时期', values='波动率')
            pivot_vol.plot(kind='bar', ax=ax2)
            ax2.set_title('泡沫破裂前后波动率对比')
            ax2.set_ylabel('波动率 (%)')
            ax2.legend()
            ax2.tick_params(axis='x', rotation=45)
            
            plt.tight_layout()
            plt.savefig('japan_bubble_comparison.png', dpi=300, bbox_inches='tight')
            plt.show()
    
    def generate_research_report(self):
        """生成研究报告"""
        print("\n" + "="*80)
        print("日本大宗商品价格变动深度研究报告 (1990-2024)")
        print("="*80)
        
        # 获取数据
        self.fetch_all_data()
        
        # 分析历史时期
        period_analysis = self.analyze_historical_periods()
        
        # 分析经济影响
        economy_analysis = self.analyze_japan_economy_impact()
        
        # 创建可视化
        self.create_comprehensive_visualizations()
        
        # 生成详细报告
        self.generate_detailed_report(period_analysis, economy_analysis)
    
    def generate_detailed_report(self, period_analysis, economy_analysis):
        """生成详细研究报告"""
        print("\n" + "="*80)
        print("详细分析报告")
        print("="*80)
        
        # 1. 历史背景分析
        print("\n1. 历史背景分析")
        print("-" * 40)
        print("日本泡沫经济破裂是20世纪最重要的经济事件之一。")
        print("1985年广场协议后，日元大幅升值，日本央行实施宽松货币政策，")
        print("导致资产价格泡沫膨胀。1990年泡沫破裂后，日本进入长期经济低迷。")
        print("这一时期对大宗商品价格产生了深远影响。")
        
        # 2. 关键发现
        print("\n2. 关键发现")
        print("-" * 40)
        
        # 找出表现最好和最差的商品
        all_performances = []
        for period_name, categories in period_analysis.items():
            for category, commodities in categories.items():
                for commodity_name, performance in commodities.items():
                    all_performances.append({
                        '商品': commodity_name,
                        '类别': category,
                        '时期': period_name,
                        '变化率': performance['变化率(%)']
                    })
        
        if all_performances:
            df_perf = pd.DataFrame(all_performances)
            
            # 泡沫破裂后整体表现
            post_bubble_data = df_perf[df_perf['时期'] == '泡沫破裂后']
            if not post_bubble_data.empty:
                print(f"泡沫破裂后平均变化率: {post_bubble_data['变化率'].mean():.2f}%")
                print(f"泡沫破裂后中位数变化率: {post_bubble_data['变化率'].median():.2f}%")
                
                best_performers = post_bubble_data.nlargest(5, '变化率')
                worst_performers = post_bubble_data.nsmallest(5, '变化率')
                
                print("\n表现最好的商品 (泡沫破裂后):")
                for _, row in best_performers.iterrows():
                    print(f"  {row['商品']} ({row['类别']}): {row['变化率']:.2f}%")
                
                print("\n表现最差的商品 (泡沫破裂后):")
                for _, row in worst_performers.iterrows():
                    print(f"  {row['商品']} ({row['类别']}): {row['变化率']:.2f}%")
        
        # 3. 经济影响分析
        print("\n3. 日本经济影响分析")
        print("-" * 40)
        
        if economy_analysis:
            for indicator_name, periods in economy_analysis.items():
                print(f"\n{indicator_name}:")
                for period_name, performance in periods.items():
                    print(f"  {period_name}: {performance['变化率(%)']:.2f}%")
        
        # 4. 结论和建议
        print("\n4. 结论和建议")
        print("-" * 40)
        print("• 日本泡沫破裂对大宗商品价格产生了复杂影响")
        print("• 不同商品类别表现出显著差异")
        print("• 日元汇率变动是重要影响因素")
        print("• 建议关注地缘政治和货币政策变化")
        print("• 多元化投资组合有助于降低风险")

def main():
    """主函数"""
    researcher = JapanCommodityResearch()
    researcher.generate_research_report()

if __name__ == "__main__":
    main() 