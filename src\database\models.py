"""
数据库模型定义
定义所有数据表的结构
"""
from datetime import datetime
from sqlalchemy import (
    Column, Integer, String, Float, DateTime, Boolean, Text, 
    BigInteger, Index, ForeignKey, UniqueConstraint, JSON
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

Base = declarative_base()


class BaseModel:
    """基础模型类"""
    id = Column(Integer, primary_key=True, autoincrement=True)
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)


class Address(Base, BaseModel):
    """地址表 - 存储所有监控的地址信息"""
    __tablename__ = 'addresses'
    
    address = Column(String(42), unique=True, nullable=False, index=True)
    label = Column(String(100))  # 地址标签
    address_type = Column(String(20))  # 地址类型：wallet, contract, exchange, etc.
    is_smart_money = Column(Boolean, default=False)  # 是否为Smart Money
    is_whale = Column(Boolean, default=False)  # 是否为巨鲸
    first_seen = Column(DateTime)  # 首次发现时间
    last_active = Column(DateTime)  # 最后活跃时间
    total_transactions = Column(Integer, default=0)  # 总交易数
    total_volume_usd = Column(Float, default=0.0)  # 总交易量(USD)
    success_rate = Column(Float)  # 成功率
    avg_holding_period = Column(Integer)  # 平均持有期(小时)
    roi_percentage = Column(Float)  # ROI百分比
    risk_score = Column(Float)  # 风险评分
    tags = Column(JSON)  # 标签数组
    metadata = Column(JSON)  # 额外元数据
    
    # 索引
    __table_args__ = (
        Index('idx_address_type', 'address_type'),
        Index('idx_smart_money', 'is_smart_money'),
        Index('idx_whale', 'is_whale'),
        Index('idx_last_active', 'last_active'),
    )


class Transaction(Base, BaseModel):
    """交易表 - 存储所有监控的交易"""
    __tablename__ = 'transactions'
    
    tx_hash = Column(String(66), unique=True, nullable=False, index=True)
    block_number = Column(BigInteger, nullable=False, index=True)
    block_timestamp = Column(DateTime, nullable=False, index=True)
    from_address = Column(String(42), ForeignKey('addresses.address'), nullable=False, index=True)
    to_address = Column(String(42), ForeignKey('addresses.address'), index=True)
    value_wei = Column(String(78))  # 使用字符串存储大整数
    value_eth = Column(Float)
    value_usd = Column(Float)
    gas_used = Column(Integer)
    gas_price = Column(BigInteger)
    gas_fee_eth = Column(Float)
    gas_fee_usd = Column(Float)
    transaction_type = Column(String(20))  # transfer, swap, mint, burn, etc.
    contract_address = Column(String(42))  # 合约地址
    token_symbol = Column(String(20))  # 代币符号
    token_amount = Column(Float)  # 代币数量
    is_whale_tx = Column(Boolean, default=False)  # 是否为巨鲸交易
    is_smart_money_tx = Column(Boolean, default=False)  # 是否为Smart Money交易
    dex_name = Column(String(50))  # DEX名称
    swap_details = Column(JSON)  # 交换详情
    
    # 关系
    from_addr = relationship("Address", foreign_keys=[from_address])
    to_addr = relationship("Address", foreign_keys=[to_address])
    
    # 索引
    __table_args__ = (
        Index('idx_block_timestamp', 'block_timestamp'),
        Index('idx_transaction_type', 'transaction_type'),
        Index('idx_whale_tx', 'is_whale_tx'),
        Index('idx_smart_money_tx', 'is_smart_money_tx'),
        Index('idx_value_usd', 'value_usd'),
    )


class Token(Base, BaseModel):
    """代币表 - 存储代币信息"""
    __tablename__ = 'tokens'
    
    contract_address = Column(String(42), unique=True, nullable=False, index=True)
    symbol = Column(String(20), nullable=False, index=True)
    name = Column(String(100), nullable=False)
    decimals = Column(Integer, nullable=False)
    total_supply = Column(String(78))  # 总供应量
    market_cap_usd = Column(Float)  # 市值
    price_usd = Column(Float)  # 价格
    volume_24h_usd = Column(Float)  # 24小时交易量
    holders_count = Column(Integer)  # 持有者数量
    is_verified = Column(Boolean, default=False)  # 是否验证
    risk_level = Column(String(10))  # 风险等级：low, medium, high
    tags = Column(JSON)  # 标签
    social_links = Column(JSON)  # 社交链接
    
    # 索引
    __table_args__ = (
        Index('idx_symbol', 'symbol'),
        Index('idx_market_cap', 'market_cap_usd'),
        Index('idx_volume_24h', 'volume_24h_usd'),
    )


class SmartMoneyActivity(Base, BaseModel):
    """Smart Money活动表"""
    __tablename__ = 'smart_money_activities'
    
    address = Column(String(42), ForeignKey('addresses.address'), nullable=False, index=True)
    tx_hash = Column(String(66), ForeignKey('transactions.tx_hash'), nullable=False)
    action_type = Column(String(20), nullable=False)  # buy, sell, transfer
    token_address = Column(String(42), ForeignKey('tokens.contract_address'))
    token_amount = Column(Float)
    usd_amount = Column(Float)
    price_at_action = Column(Float)  # 行动时的价格
    current_price = Column(Float)  # 当前价格
    pnl_usd = Column(Float)  # 盈亏
    pnl_percentage = Column(Float)  # 盈亏百分比
    holding_period_hours = Column(Integer)  # 持有期
    confidence_score = Column(Float)  # 置信度评分
    
    # 关系
    address_info = relationship("Address")
    transaction = relationship("Transaction")
    token = relationship("Token")
    
    # 索引
    __table_args__ = (
        Index('idx_action_type', 'action_type'),
        Index('idx_usd_amount', 'usd_amount'),
        Index('idx_pnl_percentage', 'pnl_percentage'),
    )


class WhaleAlert(Base, BaseModel):
    """巨鲸预警表"""
    __tablename__ = 'whale_alerts'
    
    tx_hash = Column(String(66), ForeignKey('transactions.tx_hash'), nullable=False)
    alert_type = Column(String(20), nullable=False)  # large_transfer, exchange_inflow, exchange_outflow
    from_address = Column(String(42), nullable=False)
    to_address = Column(String(42))
    amount_usd = Column(Float, nullable=False)
    token_symbol = Column(String(20))
    exchange_name = Column(String(50))  # 交易所名称
    alert_level = Column(String(10))  # 预警级别：low, medium, high, critical
    is_processed = Column(Boolean, default=False)  # 是否已处理
    notification_sent = Column(Boolean, default=False)  # 是否已发送通知
    
    # 关系
    transaction = relationship("Transaction")
    
    # 索引
    __table_args__ = (
        Index('idx_alert_type', 'alert_type'),
        Index('idx_amount_usd', 'amount_usd'),
        Index('idx_alert_level', 'alert_level'),
        Index('idx_is_processed', 'is_processed'),
    )


class MarketData(Base, BaseModel):
    """市场数据表"""
    __tablename__ = 'market_data'
    
    symbol = Column(String(20), nullable=False, index=True)
    timestamp = Column(DateTime, nullable=False, index=True)
    price_usd = Column(Float, nullable=False)
    volume_24h = Column(Float)
    market_cap = Column(Float)
    circulating_supply = Column(Float)
    total_supply = Column(Float)
    price_change_24h = Column(Float)  # 24小时价格变化百分比
    volume_change_24h = Column(Float)  # 24小时交易量变化百分比
    market_cap_rank = Column(Integer)
    fear_greed_index = Column(Integer)  # 恐惧贪婪指数
    
    # 唯一约束
    __table_args__ = (
        UniqueConstraint('symbol', 'timestamp', name='uq_market_data_symbol_timestamp'),
        Index('idx_symbol_timestamp', 'symbol', 'timestamp'),
    )


class OnChainMetrics(Base, BaseModel):
    """链上指标表"""
    __tablename__ = 'onchain_metrics'
    
    symbol = Column(String(20), nullable=False, index=True)
    date = Column(DateTime, nullable=False, index=True)
    active_addresses = Column(Integer)  # 活跃地址数
    new_addresses = Column(Integer)  # 新地址数
    transaction_count = Column(Integer)  # 交易数量
    transaction_volume = Column(Float)  # 交易量
    average_transaction_value = Column(Float)  # 平均交易价值
    median_transaction_value = Column(Float)  # 中位数交易价值
    large_transactions = Column(Integer)  # 大额交易数量
    whale_transactions = Column(Integer)  # 巨鲸交易数量
    exchange_inflow = Column(Float)  # 交易所流入
    exchange_outflow = Column(Float)  # 交易所流出
    exchange_netflow = Column(Float)  # 交易所净流入
    hodl_waves = Column(JSON)  # HODL波段数据
    mvrv_ratio = Column(Float)  # MVRV比率
    nvt_ratio = Column(Float)  # NVT比率
    realized_cap = Column(Float)  # 已实现市值
    
    # 唯一约束
    __table_args__ = (
        UniqueConstraint('symbol', 'date', name='uq_onchain_metrics_symbol_date'),
        Index('idx_symbol_date', 'symbol', 'date'),
    )


class TradingSignal(Base, BaseModel):
    """交易信号表"""
    __tablename__ = 'trading_signals'
    
    signal_type = Column(String(20), nullable=False)  # smart_money_follow, whale_movement, technical
    symbol = Column(String(20), nullable=False, index=True)
    action = Column(String(10), nullable=False)  # buy, sell, hold
    strength = Column(Float, nullable=False)  # 信号强度 0-1
    confidence = Column(Float, nullable=False)  # 置信度 0-1
    price_target = Column(Float)  # 目标价格
    stop_loss = Column(Float)  # 止损价格
    time_horizon = Column(String(20))  # 时间范围：short, medium, long
    source_data = Column(JSON)  # 源数据
    is_active = Column(Boolean, default=True)  # 是否活跃
    executed_at = Column(DateTime)  # 执行时间
    result = Column(String(20))  # 结果：pending, success, failed
    pnl_percentage = Column(Float)  # 盈亏百分比
    
    # 索引
    __table_args__ = (
        Index('idx_signal_type', 'signal_type'),
        Index('idx_symbol_action', 'symbol', 'action'),
        Index('idx_strength', 'strength'),
        Index('idx_is_active', 'is_active'),
    )


class BacktestResult(Base, BaseModel):
    """回测结果表"""
    __tablename__ = 'backtest_results'
    
    strategy_name = Column(String(100), nullable=False, index=True)
    symbol = Column(String(20), nullable=False)
    start_date = Column(DateTime, nullable=False)
    end_date = Column(DateTime, nullable=False)
    initial_capital = Column(Float, nullable=False)
    final_capital = Column(Float, nullable=False)
    total_return = Column(Float, nullable=False)  # 总收益率
    annual_return = Column(Float)  # 年化收益率
    max_drawdown = Column(Float)  # 最大回撤
    sharpe_ratio = Column(Float)  # 夏普比率
    sortino_ratio = Column(Float)  # 索提诺比率
    win_rate = Column(Float)  # 胜率
    profit_factor = Column(Float)  # 盈利因子
    total_trades = Column(Integer)  # 总交易数
    winning_trades = Column(Integer)  # 盈利交易数
    losing_trades = Column(Integer)  # 亏损交易数
    avg_win = Column(Float)  # 平均盈利
    avg_loss = Column(Float)  # 平均亏损
    parameters = Column(JSON)  # 策略参数
    trades_detail = Column(JSON)  # 交易详情
    
    # 索引
    __table_args__ = (
        Index('idx_strategy_symbol', 'strategy_name', 'symbol'),
        Index('idx_total_return', 'total_return'),
        Index('idx_sharpe_ratio', 'sharpe_ratio'),
    )
