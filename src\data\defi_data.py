"""
DeFi数据获取模块
提供DeFi协议数据、流动性池信息、收益率等数据获取功能
"""
import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime, timezone
import aiohttp

from config.settings import API_CONFIG
from src.utils.logger import get_logger, log_api_call
from src.utils.helpers import retry_on_failure

logger = get_logger(__name__)


class DefiLlamaAPI:
    """DeFiLlama API客户端"""
    
    def __init__(self):
        config = API_CONFIG['defillama']
        self.base_url = config['base_url']
        self.rate_limit_per_second = config['rate_limit']
        self.session = None
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    @retry_on_failure(max_attempts=3, delay=1.0)
    async def _make_request(self, endpoint: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """发送API请求"""
        if not self.session:
            raise RuntimeError("Session not initialized. Use async context manager.")
        
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        
        try:
            # 应用速率限制
            await asyncio.sleep(1.0 / self.rate_limit_per_second)
            
            async with self.session.get(url, params=params) as response:
                response.raise_for_status()
                return await response.json()
                
        except aiohttp.ClientError as e:
            logger.error(f"HTTP error for {url}: {e}")
            raise
        except Exception as e:
            logger.error(f"Request error for {url}: {e}")
            raise
    
    @log_api_call("DefiLlama", "get_protocols")
    async def get_protocols(self) -> List[Dict[str, Any]]:
        """获取所有DeFi协议列表"""
        response = await self._make_request('protocols')
        
        processed_protocols = []
        for protocol in response:
            processed_protocol = {
                'id': protocol.get('id'),
                'name': protocol.get('name'),
                'address': protocol.get('address'),
                'symbol': protocol.get('symbol'),
                'url': protocol.get('url'),
                'description': protocol.get('description'),
                'chain': protocol.get('chain'),
                'logo': protocol.get('logo'),
                'audits': protocol.get('audits'),
                'audit_note': protocol.get('audit_note'),
                'gecko_id': protocol.get('gecko_id'),
                'cmcId': protocol.get('cmcId'),
                'category': protocol.get('category'),
                'chains': protocol.get('chains', []),
                'module': protocol.get('module'),
                'twitter': protocol.get('twitter'),
                'forked_from': protocol.get('forkedFrom', []),
                'oracles': protocol.get('oracles', []),
                'language': protocol.get('language'),
                'listed_at': protocol.get('listedAt'),
                'slug': protocol.get('slug'),
                'tvl': protocol.get('tvl'),
                'chain_tvls': protocol.get('chainTvls', {}),
                'change_1h': protocol.get('change_1h'),
                'change_1d': protocol.get('change_1d'),
                'change_7d': protocol.get('change_7d'),
                'token_breakdowns': protocol.get('tokenBreakdowns', {}),
                'mcap': protocol.get('mcap')
            }
            processed_protocols.append(processed_protocol)
        
        return processed_protocols
    
    @log_api_call("DefiLlama", "get_protocol_tvl")
    async def get_protocol_tvl(self, protocol: str) -> Dict[str, Any]:
        """获取特定协议的TVL历史数据"""
        endpoint = f'protocol/{protocol}'
        response = await self._make_request(endpoint)
        
        return {
            'protocol': protocol,
            'name': response.get('name'),
            'address': response.get('address'),
            'symbol': response.get('symbol'),
            'url': response.get('url'),
            'description': response.get('description'),
            'chain': response.get('chain'),
            'logo': response.get('logo'),
            'audits': response.get('audits'),
            'audit_note': response.get('audit_note'),
            'gecko_id': response.get('gecko_id'),
            'cmcId': response.get('cmcId'),
            'category': response.get('category'),
            'chains': response.get('chains', []),
            'tvl': response.get('tvl', []),
            'chain_tvls': response.get('chainTvls', {}),
            'tokens': response.get('tokens', {}),
            'token_breakdowns': response.get('tokenBreakdowns', {}),
            'methodology': response.get('methodology', {}),
            'misrepresentedTokens': response.get('misrepresentedTokens', False),
            'hallmarks': response.get('hallmarks', []),
            'timestamp': datetime.now(timezone.utc)
        }
    
    @log_api_call("DefiLlama", "get_chain_tvl")
    async def get_chain_tvl(self, chain: str) -> Dict[str, Any]:
        """获取特定链的TVL数据"""
        endpoint = f'charts/{chain}'
        response = await self._make_request(endpoint)
        
        return {
            'chain': chain,
            'tvl_history': response,
            'timestamp': datetime.now(timezone.utc)
        }
    
    @log_api_call("DefiLlama", "get_yields")
    async def get_yields(self) -> List[Dict[str, Any]]:
        """获取收益率数据"""
        response = await self._make_request('yields')
        
        processed_yields = []
        for pool in response.get('data', []):
            processed_yield = {
                'chain': pool.get('chain'),
                'project': pool.get('project'),
                'symbol': pool.get('symbol'),
                'tvl_usd': pool.get('tvlUsd'),
                'apy': pool.get('apy'),
                'apy_base': pool.get('apyBase'),
                'apy_reward': pool.get('apyReward'),
                'apy_pct1d': pool.get('apyPct1D'),
                'apy_pct7d': pool.get('apyPct7D'),
                'apy_pct30d': pool.get('apyPct30D'),
                'stable_coin': pool.get('stablecoin'),
                'il_risk': pool.get('ilRisk'),
                'exposure': pool.get('exposure'),
                'predictions': pool.get('predictions', {}),
                'pool_meta': pool.get('poolMeta'),
                'mu': pool.get('mu'),
                'sigma': pool.get('sigma'),
                'count': pool.get('count'),
                'outlier': pool.get('outlier'),
                'underlyingTokens': pool.get('underlyingTokens', []),
                'url': pool.get('url'),
                'category': pool.get('category')
            }
            processed_yields.append(processed_yield)
        
        return processed_yields
    
    @log_api_call("DefiLlama", "get_stablecoins")
    async def get_stablecoins(self) -> List[Dict[str, Any]]:
        """获取稳定币数据"""
        response = await self._make_request('stablecoins')
        
        processed_stablecoins = []
        for stablecoin in response.get('peggedAssets', []):
            processed_stablecoin = {
                'id': stablecoin.get('id'),
                'name': stablecoin.get('name'),
                'address': stablecoin.get('address'),
                'symbol': stablecoin.get('symbol'),
                'url': stablecoin.get('url'),
                'description': stablecoin.get('description'),
                'chain': stablecoin.get('chain'),
                'logo': stablecoin.get('logo'),
                'audits': stablecoin.get('audits'),
                'audit_note': stablecoin.get('audit_note'),
                'gecko_id': stablecoin.get('gecko_id'),
                'cmcId': stablecoin.get('cmcId'),
                'category': stablecoin.get('category'),
                'chains': stablecoin.get('chains', []),
                'peg_type': stablecoin.get('pegType'),
                'peg_mechanism': stablecoin.get('pegMechanism'),
                'circulating': stablecoin.get('circulating', {}),
                'unreleased': stablecoin.get('unreleased', {}),
                'price': stablecoin.get('price'),
                'price_source': stablecoin.get('priceSource'),
                'peg_deviation': stablecoin.get('pegDeviation'),
                'peg_deviation_info': stablecoin.get('pegDeviationInfo', {}),
                'delisted': stablecoin.get('delisted', False)
            }
            processed_stablecoins.append(processed_stablecoin)
        
        return processed_stablecoins
    
    @log_api_call("DefiLlama", "get_fees_revenue")
    async def get_fees_revenue(self) -> List[Dict[str, Any]]:
        """获取协议费用和收入数据"""
        response = await self._make_request('overview/fees')
        
        processed_data = []
        for protocol in response.get('protocols', []):
            processed_protocol = {
                'name': protocol.get('name'),
                'logo': protocol.get('logo'),
                'defillamaId': protocol.get('defillamaId'),
                'disabled': protocol.get('disabled', False),
                'display_name': protocol.get('displayName'),
                'module': protocol.get('module'),
                'category': protocol.get('category'),
                'logo': protocol.get('logo'),
                'change_1d': protocol.get('change_1d'),
                'change_7d': protocol.get('change_7d'),
                'change_1m': protocol.get('change_1m'),
                'total24h': protocol.get('total24h'),
                'total7d': protocol.get('total7d'),
                'total30d': protocol.get('total30d'),
                'totalAllTime': protocol.get('totalAllTime'),
                'breakdown24h': protocol.get('breakdown24h', {}),
                'chains': protocol.get('chains', []),
                'protocolType': protocol.get('protocolType'),
                'methodologyURL': protocol.get('methodologyURL'),
                'methodology': protocol.get('methodology', {}),
                'latestFetchIsOk': protocol.get('latestFetchIsOk', True),
                'parentProtocol': protocol.get('parentProtocol')
            }
            processed_data.append(processed_protocol)
        
        return processed_data
