"""
真实BTC分析框架 - 需要接入实时数据API
2025年7月26日
"""
import requests
import json
from datetime import datetime, timedelta

class RealBTCAnalyzer:
    """真实BTC分析器 - 接入实时数据"""
    
    def __init__(self):
        self.api_endpoints = {
            'coingecko': 'https://api.coingecko.com/api/v3',
            'coinmarketcap': 'https://pro-api.coinmarketcap.com/v1',
            'binance': 'https://api.binance.com/api/v3',
            'glassnode': 'https://api.glassnode.com/v1'
        }
        
    def get_real_btc_price(self):
        """获取真实BTC价格 - 需要API密钥"""
        try:
            # 使用CoinGecko免费API获取基础价格数据
            url = f"{self.api_endpoints['coingecko']}/simple/price"
            params = {
                'ids': 'bitcoin',
                'vs_currencies': 'usd',
                'include_24hr_change': 'true',
                'include_24hr_vol': 'true',
                'include_market_cap': 'true'
            }
            
            response = requests.get(url, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                btc_data = data['bitcoin']
                
                return {
                    'price': btc_data['usd'],
                    'change_24h': btc_data.get('usd_24h_change', 0),
                    'volume_24h': btc_data.get('usd_24h_vol', 0),
                    'market_cap': btc_data.get('usd_market_cap', 0),
                    'timestamp': datetime.now().isoformat()
                }
            else:
                print(f"❌ API请求失败: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ 获取价格数据失败: {e}")
            return None
    
    def get_technical_indicators(self, symbol='BTCUSDT', interval='1d', limit=100):
        """获取技术指标数据"""
        try:
            # 使用Binance API获取K线数据
            url = f"{self.api_endpoints['binance']}/klines"
            params = {
                'symbol': symbol,
                'interval': interval,
                'limit': limit
            }
            
            response = requests.get(url, params=params, timeout=10)
            
            if response.status_code == 200:
                klines = response.json()
                
                # 提取价格数据
                prices = []
                volumes = []
                
                for kline in klines:
                    close_price = float(kline[4])
                    volume = float(kline[5])
                    prices.append(close_price)
                    volumes.append(volume)
                
                # 计算技术指标
                current_price = prices[-1]
                sma_20 = sum(prices[-20:]) / 20 if len(prices) >= 20 else current_price
                sma_50 = sum(prices[-50:]) / 50 if len(prices) >= 50 else current_price
                
                # 简化RSI计算
                if len(prices) >= 14:
                    gains = []
                    losses = []
                    for i in range(1, 15):
                        change = prices[-i] - prices[-i-1]
                        if change > 0:
                            gains.append(change)
                            losses.append(0)
                        else:
                            gains.append(0)
                            losses.append(abs(change))
                    
                    avg_gain = sum(gains) / 14
                    avg_loss = sum(losses) / 14
                    rs = avg_gain / avg_loss if avg_loss != 0 else 0
                    rsi = 100 - (100 / (1 + rs))
                else:
                    rsi = 50
                
                return {
                    'current_price': current_price,
                    'sma_20': sma_20,
                    'sma_50': sma_50,
                    'rsi': rsi,
                    'volume_24h': volumes[-1],
                    'prices': prices[-30:],  # 最近30天价格
                    'timestamp': datetime.now().isoformat()
                }
            else:
                print(f"❌ 技术指标API请求失败: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ 获取技术指标失败: {e}")
            return None
    
    def analyze_with_real_data(self):
        """使用真实数据进行分析"""
        print("🚀 OnChain Analytics Platform - 真实BTC分析")
        print("=" * 60)
        print(f"📅 分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 获取实时价格数据
        print("\n📊 获取实时市场数据...")
        price_data = self.get_real_btc_price()
        
        if price_data:
            print(f"✅ 价格数据获取成功")
            print(f"当前价格: ${price_data['price']:,.2f}")
            print(f"24h变化: {price_data['change_24h']:+.2f}%")
            print(f"24h成交量: ${price_data['volume_24h']:,.0f}")
            print(f"市值: ${price_data['market_cap']:,.0f}")
        else:
            print("❌ 无法获取实时价格数据")
            return
        
        # 获取技术指标
        print("\n📈 获取技术指标数据...")
        tech_data = self.get_technical_indicators()
        
        if tech_data:
            print(f"✅ 技术指标获取成功")
            print(f"20日均线: ${tech_data['sma_20']:,.2f}")
            print(f"50日均线: ${tech_data['sma_50']:,.2f}")
            print(f"RSI: {tech_data['rsi']:.1f}")
            
            # 技术分析
            current_price = tech_data['current_price']
            signals = []
            
            if current_price > tech_data['sma_20']:
                signals.append("✅ 价格在20日均线上方")
            else:
                signals.append("❌ 价格在20日均线下方")
                
            if current_price > tech_data['sma_50']:
                signals.append("✅ 价格在50日均线上方")
            else:
                signals.append("❌ 价格在50日均线下方")
            
            if tech_data['rsi'] > 70:
                signals.append("⚠️ RSI超买")
            elif tech_data['rsi'] < 30:
                signals.append("⚠️ RSI超卖")
            else:
                signals.append("✅ RSI中性")
            
            print(f"\n📊 技术信号:")
            for signal in signals:
                print(f"  {signal}")
                
        else:
            print("❌ 无法获取技术指标数据")
        
        # 生成基于真实数据的建议
        self.generate_real_recommendation(price_data, tech_data)
    
    def generate_real_recommendation(self, price_data, tech_data):
        """基于真实数据生成建议"""
        print(f"\n💡 基于真实数据的分析建议")
        print("-" * 40)
        
        if not price_data or not tech_data:
            print("❌ 数据不足，无法生成可靠建议")
            return
        
        current_price = price_data['price']
        change_24h = price_data['change_24h']
        rsi = tech_data['rsi']
        
        # 简单的信号评分
        score = 0
        
        if change_24h > 0:
            score += 1
        if current_price > tech_data['sma_20']:
            score += 1
        if current_price > tech_data['sma_50']:
            score += 1
        if 30 < rsi < 70:
            score += 1
        
        if score >= 3:
            recommendation = "🟢 看涨"
            action = "考虑买入"
        elif score >= 2:
            recommendation = "🟡 中性"
            action = "观望为主"
        else:
            recommendation = "🔴 看跌"
            action = "谨慎操作"
        
        print(f"综合评分: {score}/4")
        print(f"市场判断: {recommendation}")
        print(f"操作建议: {action}")
        
        # 风险提示
        print(f"\n⚠️ 重要说明:")
        print(f"• 本分析基于有限的技术指标")
        print(f"• 缺少Smart Money和巨鲸数据")
        print(f"• 需要更多链上数据支持")
        print(f"• 投资有风险，决策需谨慎")

def main():
    """主函数"""
    print("🔧 OnChain Analytics Platform - 数据源说明")
    print("=" * 60)
    print("为了提供真实准确的BTC分析，系统需要接入以下数据源：")
    print()
    print("📊 价格数据:")
    print("  • CoinGecko API (免费)")
    print("  • CoinMarketCap API (需要密钥)")
    print("  • Binance API (免费)")
    print()
    print("🧠 Smart Money数据:")
    print("  • Glassnode API (付费)")
    print("  • IntoTheBlock API (付费)")
    print("  • Nansen API (付费)")
    print()
    print("🐋 巨鲸数据:")
    print("  • Whale Alert API (付费)")
    print("  • 区块链浏览器API")
    print("  • 交易所API")
    print()
    print("⛓️ 链上数据:")
    print("  • Glassnode (付费)")
    print("  • CryptoQuant (付费)")
    print("  • Messari API (部分免费)")
    print()
    
    # 尝试运行真实数据分析
    analyzer = RealBTCAnalyzer()
    
    try:
        analyzer.analyze_with_real_data()
    except Exception as e:
        print(f"\n❌ 分析过程中发生错误: {e}")
        print("\n💡 解决方案:")
        print("1. 检查网络连接")
        print("2. 获取必要的API密钥")
        print("3. 安装requests库: pip install requests")
        print("4. 配置API访问权限")

if __name__ == "__main__":
    main()
