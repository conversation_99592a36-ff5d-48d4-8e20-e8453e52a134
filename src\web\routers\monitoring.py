"""
监控API路由
提供实时监控、预警和通知功能
"""
from fastapi import APIRouter, HTTPException, Query
from typing import Dict, List, Optional, Any
from datetime import datetime, timezone, timedelta
import random

from src.monitoring.whale_monitor import WhaleMonitor
from src.monitoring.realtime_monitor import RealtimeMonitor
from src.monitoring.anomaly_detector import AnomalyDetector
from src.monitoring.notification_system import NotificationSystem
from src.utils.logger import get_logger

logger = get_logger(__name__)

router = APIRouter()

# 初始化组件
whale_monitor = WhaleMonitor()
anomaly_detector = AnomalyDetector()
notification_system = NotificationSystem()


@router.get("/whale-activity")
async def get_whale_activity(
    symbol: Optional[str] = None,
    hours: int = Query(default=24, ge=1, le=168),
    min_amount: float = Query(default=1000000, ge=100000)
):
    """获取巨鲸活动"""
    try:
        # 模拟巨鲸活动数据
        activities = []
        
        symbols = [symbol] if symbol else ['BTC', 'ETH', 'ADA', 'DOT']
        
        for _ in range(random.randint(5, 20)):
            activity_symbol = random.choice(symbols)
            amount_usd = random.uniform(min_amount, min_amount * 50)
            
            activity = {
                'id': f"whale_{random.randint(10000, 99999)}",
                'symbol': activity_symbol,
                'timestamp': (datetime.now(timezone.utc) - timedelta(hours=random.uniform(0, hours))).isoformat(),
                'transaction_type': random.choice(['exchange_deposit', 'exchange_withdrawal', 'wallet_transfer', 'dex_trade']),
                'amount_usd': amount_usd,
                'from_address': f"0x{random.randint(10**15, 10**16-1):016x}",
                'to_address': f"0x{random.randint(10**15, 10**16-1):016x}",
                'tx_hash': f"0x{random.randint(10**15, 10**16-1):016x}",
                'alert_level': 'high' if amount_usd > min_amount * 10 else 'medium',
                'impact_score': random.uniform(0.5, 1.0),
                'exchange': random.choice(['Binance', 'Coinbase', 'Kraken', 'Unknown']) if 'exchange' in activity['transaction_type'] else None
            }
            
            activities.append(activity)
        
        # 按时间排序
        activities.sort(key=lambda x: x['timestamp'], reverse=True)
        
        # 统计信息
        total_volume = sum(a['amount_usd'] for a in activities)
        deposit_volume = sum(a['amount_usd'] for a in activities if a['transaction_type'] == 'exchange_deposit')
        withdrawal_volume = sum(a['amount_usd'] for a in activities if a['transaction_type'] == 'exchange_withdrawal')
        
        return {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'period_hours': hours,
            'filter': {
                'symbol': symbol,
                'min_amount': min_amount
            },
            'summary': {
                'total_activities': len(activities),
                'total_volume_usd': total_volume,
                'deposit_volume_usd': deposit_volume,
                'withdrawal_volume_usd': withdrawal_volume,
                'net_flow_usd': withdrawal_volume - deposit_volume,
                'avg_transaction_size': total_volume / len(activities) if activities else 0
            },
            'activities': activities
        }
    
    except Exception as e:
        logger.error(f"Failed to get whale activity: {e}")
        raise HTTPException(status_code=500, detail="Failed to get whale activity")


@router.get("/anomalies/{symbol}")
async def get_anomalies(
    symbol: str,
    hours: int = Query(default=24, ge=1, le=168)
):
    """获取异常检测结果"""
    try:
        anomalies = await anomaly_detector.detect_anomalies(symbol, hours=hours)
        
        # 如果没有真实数据，生成模拟异常
        if not anomalies:
            anomalies = []
            for _ in range(random.randint(0, 5)):
                anomaly = {
                    'id': f"anomaly_{random.randint(10000, 99999)}",
                    'symbol': symbol,
                    'timestamp': (datetime.now(timezone.utc) - timedelta(hours=random.uniform(0, hours))).isoformat(),
                    'anomaly_type': random.choice(['price_spike', 'volume_surge', 'unusual_pattern', 'correlation_break']),
                    'severity': random.choice(['low', 'medium', 'high']),
                    'confidence': random.uniform(0.6, 0.95),
                    'description': f"{symbol} 检测到异常模式",
                    'affected_metrics': random.sample(['price', 'volume', 'volatility', 'correlation'], random.randint(1, 3)),
                    'suggested_actions': random.sample([
                        '密切监控价格变化',
                        '检查相关新闻',
                        '评估风险敞口',
                        '考虑调整仓位',
                        '设置预警通知'
                    ], random.randint(2, 4))
                }
                anomalies.append(anomaly)
        
        # 趋势分析
        trends = await anomaly_detector.analyze_anomaly_trends(symbol, days=7)
        
        return {
            'symbol': symbol,
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'period_hours': hours,
            'anomalies': anomalies,
            'summary': {
                'total_anomalies': len(anomalies),
                'high_severity': len([a for a in anomalies if a.get('severity') == 'high']),
                'medium_severity': len([a for a in anomalies if a.get('severity') == 'medium']),
                'low_severity': len([a for a in anomalies if a.get('severity') == 'low']),
                'avg_confidence': sum(a.get('confidence', 0) for a in anomalies) / len(anomalies) if anomalies else 0
            },
            'trends': trends
        }
    
    except Exception as e:
        logger.error(f"Failed to get anomalies for {symbol}: {e}")
        raise HTTPException(status_code=500, detail="Failed to get anomalies")


@router.get("/alerts")
async def get_active_alerts(
    symbol: Optional[str] = None,
    severity: Optional[str] = None,
    limit: int = Query(default=50, ge=1, le=200)
):
    """获取活跃预警"""
    try:
        # 模拟预警数据
        alerts = []
        
        symbols = [symbol] if symbol else ['BTC', 'ETH', 'ADA', 'DOT', 'LINK', 'UNI']
        severities = [severity] if severity else ['low', 'medium', 'high']
        
        alert_types = [
            'price_threshold',
            'volume_spike',
            'whale_activity',
            'smart_money_signal',
            'technical_signal',
            'anomaly_detected',
            'correlation_change'
        ]
        
        for i in range(min(limit, random.randint(10, 30))):
            alert_symbol = random.choice(symbols)
            alert_severity = random.choice(severities)
            alert_type = random.choice(alert_types)
            
            alert = {
                'id': f"alert_{random.randint(10000, 99999)}",
                'symbol': alert_symbol,
                'type': alert_type,
                'severity': alert_severity,
                'timestamp': (datetime.now(timezone.utc) - timedelta(minutes=random.randint(1, 1440))).isoformat(),
                'title': f"{alert_symbol} {alert_type.replace('_', ' ').title()}",
                'message': f"{alert_symbol} 触发 {alert_type.replace('_', ' ')} 预警",
                'is_active': random.choice([True, True, True, False]),  # 75%概率为活跃
                'acknowledged': random.choice([True, False]),
                'data': {
                    'current_value': random.uniform(1000, 100000),
                    'threshold': random.uniform(500, 50000),
                    'change_pct': random.uniform(-20, 20)
                }
            }
            
            alerts.append(alert)
        
        # 过滤和排序
        if symbol:
            alerts = [a for a in alerts if a['symbol'] == symbol]
        if severity:
            alerts = [a for a in alerts if a['severity'] == severity]
        
        alerts.sort(key=lambda x: x['timestamp'], reverse=True)
        
        return {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'filters': {
                'symbol': symbol,
                'severity': severity,
                'limit': limit
            },
            'summary': {
                'total_alerts': len(alerts),
                'active_alerts': len([a for a in alerts if a['is_active']]),
                'unacknowledged_alerts': len([a for a in alerts if not a['acknowledged']]),
                'severity_breakdown': {
                    'high': len([a for a in alerts if a['severity'] == 'high']),
                    'medium': len([a for a in alerts if a['severity'] == 'medium']),
                    'low': len([a for a in alerts if a['severity'] == 'low'])
                }
            },
            'alerts': alerts
        }
    
    except Exception as e:
        logger.error(f"Failed to get active alerts: {e}")
        raise HTTPException(status_code=500, detail="Failed to get active alerts")


@router.post("/alerts/{alert_id}/acknowledge")
async def acknowledge_alert(alert_id: str):
    """确认预警"""
    try:
        # 模拟确认操作
        return {
            'alert_id': alert_id,
            'acknowledged': True,
            'acknowledged_at': datetime.now(timezone.utc).isoformat(),
            'acknowledged_by': 'user',
            'status': 'success'
        }
    
    except Exception as e:
        logger.error(f"Failed to acknowledge alert {alert_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to acknowledge alert")


@router.get("/system-health")
async def get_system_health():
    """获取系统健康状态"""
    try:
        health_data = {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'overall_status': 'healthy',
            'components': {
                'database': {
                    'status': 'healthy',
                    'response_time_ms': random.uniform(10, 50),
                    'connection_pool': {
                        'active': random.randint(5, 15),
                        'idle': random.randint(10, 20),
                        'max': 50
                    }
                },
                'api_server': {
                    'status': 'healthy',
                    'uptime_hours': random.uniform(100, 1000),
                    'requests_per_minute': random.randint(50, 200),
                    'error_rate': random.uniform(0, 0.05)
                },
                'websocket': {
                    'status': 'healthy',
                    'active_connections': random.randint(10, 100),
                    'messages_per_minute': random.randint(100, 500)
                },
                'monitoring_services': {
                    'whale_monitor': {
                        'status': 'running',
                        'last_update': (datetime.now(timezone.utc) - timedelta(minutes=random.randint(1, 5))).isoformat(),
                        'monitored_addresses': random.randint(1000, 5000)
                    },
                    'anomaly_detector': {
                        'status': 'running',
                        'last_analysis': (datetime.now(timezone.utc) - timedelta(minutes=random.randint(1, 10))).isoformat(),
                        'detection_accuracy': random.uniform(0.85, 0.95)
                    },
                    'notification_system': {
                        'status': 'running',
                        'notifications_sent_24h': random.randint(50, 200),
                        'delivery_success_rate': random.uniform(0.95, 0.99)
                    }
                }
            },
            'metrics': {
                'cpu_usage_percent': random.uniform(20, 80),
                'memory_usage_percent': random.uniform(40, 85),
                'disk_usage_percent': random.uniform(30, 70),
                'network_io_mbps': random.uniform(10, 100)
            },
            'alerts': {
                'active_system_alerts': random.randint(0, 3),
                'last_system_alert': (datetime.now(timezone.utc) - timedelta(hours=random.randint(1, 24))).isoformat() if random.choice([True, False]) else None
            }
        }
        
        return health_data
    
    except Exception as e:
        logger.error(f"Failed to get system health: {e}")
        raise HTTPException(status_code=500, detail="Failed to get system health")


@router.get("/notification-stats")
async def get_notification_stats(hours: int = Query(default=24, ge=1, le=168)):
    """获取通知统计"""
    try:
        stats = notification_system.get_notification_stats(hours=hours)
        
        # 如果没有真实数据，生成模拟统计
        if stats.get('no_data'):
            stats = {
                'period_hours': hours,
                'total_notifications': random.randint(50, 200),
                'successful_notifications': random.randint(45, 190),
                'failed_notifications': random.randint(0, 10),
                'success_rate': random.uniform(0.9, 0.99),
                'channel_stats': {
                    'email': {
                        'total': random.randint(20, 80),
                        'success': random.randint(18, 78),
                        'success_rate': random.uniform(0.9, 0.98)
                    },
                    'telegram': {
                        'total': random.randint(15, 60),
                        'success': random.randint(14, 58),
                        'success_rate': random.uniform(0.92, 0.99)
                    },
                    'discord': {
                        'total': random.randint(10, 40),
                        'success': random.randint(9, 38),
                        'success_rate': random.uniform(0.9, 0.95)
                    }
                },
                'notification_types': {
                    'price_alert': random.randint(20, 60),
                    'whale_alert': random.randint(10, 30),
                    'smart_money_signal': random.randint(5, 20),
                    'system_alert': random.randint(0, 5),
                    'anomaly_alert': random.randint(3, 15)
                }
            }
        
        return {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'stats': stats
        }
    
    except Exception as e:
        logger.error(f"Failed to get notification stats: {e}")
        raise HTTPException(status_code=500, detail="Failed to get notification stats")
