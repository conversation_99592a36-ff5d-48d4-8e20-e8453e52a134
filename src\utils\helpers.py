"""
辅助函数模块
提供通用的工具函数
"""
import time
import hashlib
import json
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, Union
from functools import wraps
import requests
from tenacity import retry, stop_after_attempt, wait_exponential

from .logger import get_logger

logger = get_logger(__name__)


def retry_on_failure(max_attempts: int = 3, delay: float = 1.0):
    """
    重试装饰器
    
    Args:
        max_attempts: 最大重试次数
        delay: 重试延迟（秒）
    """
    return retry(
        stop=stop_after_attempt(max_attempts),
        wait=wait_exponential(multiplier=delay, min=delay, max=60)
    )


def rate_limit(calls_per_second: float):
    """
    速率限制装饰器
    
    Args:
        calls_per_second: 每秒允许的调用次数
    """
    min_interval = 1.0 / calls_per_second
    last_called = [0.0]
    
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            elapsed = time.time() - last_called[0]
            left_to_wait = min_interval - elapsed
            if left_to_wait > 0:
                time.sleep(left_to_wait)
            ret = func(*args, **kwargs)
            last_called[0] = time.time()
            return ret
        return wrapper
    return decorator


def to_checksum_address(address: str) -> str:
    """
    转换为校验和地址格式
    
    Args:
        address: 以太坊地址
        
    Returns:
        校验和格式的地址
    """
    if not address.startswith('0x'):
        address = '0x' + address
    
    address = address.lower()
    address_hash = hashlib.sha3_256(address[2:].encode()).hexdigest()
    
    checksum_address = '0x'
    for i, char in enumerate(address[2:]):
        if int(address_hash[i], 16) >= 8:
            checksum_address += char.upper()
        else:
            checksum_address += char
    
    return checksum_address


def is_valid_address(address: str) -> bool:
    """
    验证以太坊地址是否有效
    
    Args:
        address: 以太坊地址
        
    Returns:
        是否有效
    """
    if not isinstance(address, str):
        return False
    
    if not address.startswith('0x'):
        return False
    
    if len(address) != 42:
        return False
    
    try:
        int(address[2:], 16)
        return True
    except ValueError:
        return False


def wei_to_ether(wei: Union[int, str]) -> float:
    """
    将Wei转换为Ether
    
    Args:
        wei: Wei数量
        
    Returns:
        Ether数量
    """
    return int(wei) / 10**18


def ether_to_wei(ether: Union[int, float]) -> int:
    """
    将Ether转换为Wei
    
    Args:
        ether: Ether数量
        
    Returns:
        Wei数量
    """
    return int(float(ether) * 10**18)


def format_large_number(number: Union[int, float], precision: int = 2) -> str:
    """
    格式化大数字显示
    
    Args:
        number: 数字
        precision: 小数位数
        
    Returns:
        格式化后的字符串
    """
    if number >= 1e9:
        return f"{number/1e9:.{precision}f}B"
    elif number >= 1e6:
        return f"{number/1e6:.{precision}f}M"
    elif number >= 1e3:
        return f"{number/1e3:.{precision}f}K"
    else:
        return f"{number:.{precision}f}"


def timestamp_to_datetime(timestamp: Union[int, float]) -> datetime:
    """
    将时间戳转换为datetime对象
    
    Args:
        timestamp: Unix时间戳
        
    Returns:
        datetime对象
    """
    return datetime.fromtimestamp(timestamp, tz=timezone.utc)


def datetime_to_timestamp(dt: datetime) -> int:
    """
    将datetime对象转换为时间戳
    
    Args:
        dt: datetime对象
        
    Returns:
        Unix时间戳
    """
    return int(dt.timestamp())


def calculate_percentage_change(old_value: float, new_value: float) -> float:
    """
    计算百分比变化
    
    Args:
        old_value: 旧值
        new_value: 新值
        
    Returns:
        百分比变化
    """
    if old_value == 0:
        return 0.0
    return ((new_value - old_value) / old_value) * 100


def safe_divide(numerator: float, denominator: float, default: float = 0.0) -> float:
    """
    安全除法，避免除零错误
    
    Args:
        numerator: 分子
        denominator: 分母
        default: 默认值
        
    Returns:
        除法结果
    """
    if denominator == 0:
        return default
    return numerator / denominator


def chunk_list(lst: List[Any], chunk_size: int) -> List[List[Any]]:
    """
    将列表分块
    
    Args:
        lst: 原始列表
        chunk_size: 块大小
        
    Returns:
        分块后的列表
    """
    return [lst[i:i + chunk_size] for i in range(0, len(lst), chunk_size)]


def flatten_dict(d: Dict[str, Any], parent_key: str = '', sep: str = '.') -> Dict[str, Any]:
    """
    扁平化嵌套字典
    
    Args:
        d: 嵌套字典
        parent_key: 父键名
        sep: 分隔符
        
    Returns:
        扁平化后的字典
    """
    items = []
    for k, v in d.items():
        new_key = f"{parent_key}{sep}{k}" if parent_key else k
        if isinstance(v, dict):
            items.extend(flatten_dict(v, new_key, sep=sep).items())
        else:
            items.append((new_key, v))
    return dict(items)


def deep_merge_dicts(dict1: Dict[str, Any], dict2: Dict[str, Any]) -> Dict[str, Any]:
    """
    深度合并字典
    
    Args:
        dict1: 字典1
        dict2: 字典2
        
    Returns:
        合并后的字典
    """
    result = dict1.copy()
    for key, value in dict2.items():
        if key in result and isinstance(result[key], dict) and isinstance(value, dict):
            result[key] = deep_merge_dicts(result[key], value)
        else:
            result[key] = value
    return result


def calculate_hash(data: Union[str, Dict, List]) -> str:
    """
    计算数据的哈希值
    
    Args:
        data: 要计算哈希的数据
        
    Returns:
        SHA256哈希值
    """
    if isinstance(data, (dict, list)):
        data = json.dumps(data, sort_keys=True)
    
    return hashlib.sha256(data.encode()).hexdigest()


def is_contract_address(address: str, web3_instance=None) -> bool:
    """
    检查地址是否为合约地址
    
    Args:
        address: 以太坊地址
        web3_instance: Web3实例
        
    Returns:
        是否为合约地址
    """
    if not is_valid_address(address):
        return False
    
    if web3_instance is None:
        # 如果没有Web3实例，无法检查
        return False
    
    try:
        code = web3_instance.eth.get_code(address)
        return len(code) > 0
    except Exception as e:
        logger.error(f"Error checking contract address {address}: {e}")
        return False


def format_duration(seconds: float) -> str:
    """
    格式化持续时间
    
    Args:
        seconds: 秒数
        
    Returns:
        格式化后的时间字符串
    """
    if seconds < 60:
        return f"{seconds:.1f}s"
    elif seconds < 3600:
        return f"{seconds/60:.1f}m"
    elif seconds < 86400:
        return f"{seconds/3600:.1f}h"
    else:
        return f"{seconds/86400:.1f}d"


def validate_config(config: Dict[str, Any], required_keys: List[str]) -> bool:
    """
    验证配置是否包含必需的键
    
    Args:
        config: 配置字典
        required_keys: 必需的键列表
        
    Returns:
        是否有效
    """
    missing_keys = [key for key in required_keys if key not in config]
    if missing_keys:
        logger.error(f"Missing required config keys: {missing_keys}")
        return False
    return True
