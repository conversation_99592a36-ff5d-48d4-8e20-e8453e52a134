"""
数据管理器
提供高级数据管理功能，包括数据同步、清理、备份等
"""
import asyncio
import json
from typing import Dict, List, Optional, Any
from datetime import datetime, timezone, timedelta
from pathlib import Path

from .data_service import DataService
from .database import db_manager
from src.utils.logger import get_logger
from config.settings import DATA_DIRS

logger = get_logger(__name__)


class DataManager:
    """数据管理器"""
    
    def __init__(self):
        self.data_service = None
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.data_service = await DataService().__aenter__()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.data_service:
            await self.data_service.__aexit__(exc_type, exc_val, exc_tb)
    
    async def sync_watchlist_addresses(self, addresses: List[str], 
                                     batch_size: int = 10, 
                                     delay_seconds: float = 1.0) -> Dict[str, Any]:
        """同步监控地址列表"""
        try:
            logger.info(f"Starting sync for {len(addresses)} addresses")
            
            results = {
                'total_addresses': len(addresses),
                'successful_syncs': 0,
                'failed_syncs': 0,
                'errors': [],
                'start_time': datetime.now(timezone.utc)
            }
            
            # 分批处理地址
            for i in range(0, len(addresses), batch_size):
                batch = addresses[i:i + batch_size]
                logger.info(f"Processing batch {i//batch_size + 1}: {len(batch)} addresses")
                
                # 并行处理批次中的地址
                tasks = [
                    self.data_service.sync_address_data(address)
                    for address in batch
                ]
                
                batch_results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # 处理批次结果
                for address, result in zip(batch, batch_results):
                    if isinstance(result, Exception):
                        results['failed_syncs'] += 1
                        results['errors'].append({
                            'address': address,
                            'error': str(result)
                        })
                        logger.error(f"Failed to sync address {address}: {result}")
                    else:
                        results['successful_syncs'] += 1
                        logger.debug(f"Successfully synced address {address}")
                
                # 批次间延迟
                if i + batch_size < len(addresses):
                    await asyncio.sleep(delay_seconds)
            
            results['end_time'] = datetime.now(timezone.utc)
            results['duration_seconds'] = (results['end_time'] - results['start_time']).total_seconds()
            
            logger.info(f"Sync completed: {results['successful_syncs']} successful, {results['failed_syncs']} failed")
            return results
        
        except Exception as e:
            logger.error(f"Failed to sync watchlist addresses: {e}")
            raise
    
    async def sync_market_data_batch(self, symbol_batches: List[List[str]], 
                                   delay_seconds: float = 2.0) -> Dict[str, Any]:
        """批量同步市场数据"""
        try:
            logger.info(f"Starting market data sync for {len(symbol_batches)} batches")
            
            results = {
                'total_batches': len(symbol_batches),
                'successful_batches': 0,
                'failed_batches': 0,
                'total_records': 0,
                'errors': [],
                'start_time': datetime.now(timezone.utc)
            }
            
            for i, symbols in enumerate(symbol_batches):
                try:
                    logger.info(f"Processing market data batch {i + 1}: {symbols}")
                    
                    sync_result = await self.data_service.sync_market_data(symbols)
                    results['successful_batches'] += 1
                    results['total_records'] += sync_result['inserted_records']
                    
                    # 批次间延迟
                    if i + 1 < len(symbol_batches):
                        await asyncio.sleep(delay_seconds)
                
                except Exception as e:
                    results['failed_batches'] += 1
                    results['errors'].append({
                        'batch': i + 1,
                        'symbols': symbols,
                        'error': str(e)
                    })
                    logger.error(f"Failed to sync market data batch {i + 1}: {e}")
            
            results['end_time'] = datetime.now(timezone.utc)
            results['duration_seconds'] = (results['end_time'] - results['start_time']).total_seconds()
            
            logger.info(f"Market data sync completed: {results['successful_batches']} successful batches")
            return results
        
        except Exception as e:
            logger.error(f"Failed to sync market data batches: {e}")
            raise
    
    async def cleanup_old_data(self, retention_days: Dict[str, int] = None) -> Dict[str, Any]:
        """清理旧数据"""
        try:
            if retention_days is None:
                retention_days = {
                    'transactions': 90,      # 保留90天的交易数据
                    'market_data': 365,      # 保留1年的市场数据
                    'whale_alerts': 30,      # 保留30天的巨鲸预警
                    'trading_signals': 60    # 保留60天的交易信号
                }
            
            logger.info(f"Starting data cleanup with retention policy: {retention_days}")
            
            results = {
                'cleanup_started': datetime.now(timezone.utc),
                'tables_cleaned': {},
                'total_deleted_records': 0
            }
            
            # 清理交易数据
            if 'transactions' in retention_days:
                cutoff_date = datetime.now(timezone.utc) - timedelta(days=retention_days['transactions'])
                deleted_count = await self._cleanup_table('transactions', 'block_timestamp', cutoff_date)
                results['tables_cleaned']['transactions'] = deleted_count
                results['total_deleted_records'] += deleted_count
            
            # 清理市场数据
            if 'market_data' in retention_days:
                cutoff_date = datetime.now(timezone.utc) - timedelta(days=retention_days['market_data'])
                deleted_count = await self._cleanup_table('market_data', 'timestamp', cutoff_date)
                results['tables_cleaned']['market_data'] = deleted_count
                results['total_deleted_records'] += deleted_count
            
            # 清理巨鲸预警
            if 'whale_alerts' in retention_days:
                cutoff_date = datetime.now(timezone.utc) - timedelta(days=retention_days['whale_alerts'])
                deleted_count = await self._cleanup_table('whale_alerts', 'created_at', cutoff_date)
                results['tables_cleaned']['whale_alerts'] = deleted_count
                results['total_deleted_records'] += deleted_count
            
            # 清理交易信号
            if 'trading_signals' in retention_days:
                cutoff_date = datetime.now(timezone.utc) - timedelta(days=retention_days['trading_signals'])
                deleted_count = await self._cleanup_table('trading_signals', 'created_at', cutoff_date)
                results['tables_cleaned']['trading_signals'] = deleted_count
                results['total_deleted_records'] += deleted_count
            
            results['cleanup_completed'] = datetime.now(timezone.utc)
            results['duration_seconds'] = (results['cleanup_completed'] - results['cleanup_started']).total_seconds()
            
            logger.info(f"Data cleanup completed: {results['total_deleted_records']} records deleted")
            return results
        
        except Exception as e:
            logger.error(f"Failed to cleanup old data: {e}")
            raise
    
    async def _cleanup_table(self, table_name: str, timestamp_column: str, cutoff_date: datetime) -> int:
        """清理指定表的旧数据"""
        try:
            sql = f"""
                DELETE FROM {table_name} 
                WHERE {timestamp_column} < :cutoff_date
            """
            
            result = db_manager.execute_raw_sql(sql, {'cutoff_date': cutoff_date})
            deleted_count = len(result) if result else 0
            
            logger.info(f"Deleted {deleted_count} records from {table_name} older than {cutoff_date}")
            return deleted_count
        
        except Exception as e:
            logger.error(f"Failed to cleanup table {table_name}: {e}")
            raise
    
    async def export_data(self, export_config: Dict[str, Any]) -> Dict[str, Any]:
        """导出数据"""
        try:
            logger.info(f"Starting data export with config: {export_config}")
            
            export_dir = DATA_DIRS['processed'] / 'exports' / datetime.now().strftime('%Y%m%d_%H%M%S')
            export_dir.mkdir(parents=True, exist_ok=True)
            
            results = {
                'export_started': datetime.now(timezone.utc),
                'export_dir': str(export_dir),
                'exported_files': [],
                'total_records': 0
            }
            
            # 导出地址数据
            if export_config.get('addresses', False):
                addresses_file = await self._export_addresses(export_dir, export_config)
                results['exported_files'].append(addresses_file)
            
            # 导出交易数据
            if export_config.get('transactions', False):
                transactions_file = await self._export_transactions(export_dir, export_config)
                results['exported_files'].append(transactions_file)
            
            # 导出市场数据
            if export_config.get('market_data', False):
                market_data_file = await self._export_market_data(export_dir, export_config)
                results['exported_files'].append(market_data_file)
            
            results['export_completed'] = datetime.now(timezone.utc)
            results['duration_seconds'] = (results['export_completed'] - results['export_started']).total_seconds()
            
            logger.info(f"Data export completed: {len(results['exported_files'])} files exported")
            return results
        
        except Exception as e:
            logger.error(f"Failed to export data: {e}")
            raise
    
    async def _export_addresses(self, export_dir: Path, config: Dict[str, Any]) -> Dict[str, Any]:
        """导出地址数据"""
        try:
            addresses = self.data_service.address_repo.get_all(limit=config.get('address_limit', 10000))
            
            export_data = []
            for addr in addresses:
                export_data.append({
                    'address': addr.address,
                    'label': addr.label,
                    'address_type': addr.address_type,
                    'is_smart_money': addr.is_smart_money,
                    'is_whale': addr.is_whale,
                    'total_transactions': addr.total_transactions,
                    'total_volume_usd': addr.total_volume_usd,
                    'roi_percentage': addr.roi_percentage,
                    'created_at': addr.created_at.isoformat() if addr.created_at else None,
                    'updated_at': addr.updated_at.isoformat() if addr.updated_at else None
                })
            
            file_path = export_dir / 'addresses.json'
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)
            
            return {
                'file_path': str(file_path),
                'record_count': len(export_data),
                'file_size_bytes': file_path.stat().st_size
            }
        
        except Exception as e:
            logger.error(f"Failed to export addresses: {e}")
            raise
    
    async def _export_transactions(self, export_dir: Path, config: Dict[str, Any]) -> Dict[str, Any]:
        """导出交易数据"""
        try:
            # 获取指定时间范围的交易
            days = config.get('transaction_days', 7)
            since = datetime.now(timezone.utc) - timedelta(days=days)
            
            sql = """
                SELECT tx_hash, block_number, block_timestamp, from_address, to_address,
                       value_eth, value_usd, gas_fee_eth, transaction_type, token_symbol
                FROM transactions 
                WHERE block_timestamp >= :since_date
                ORDER BY block_timestamp DESC
                LIMIT :limit
            """
            
            transactions = db_manager.execute_raw_sql(sql, {
                'since_date': since,
                'limit': config.get('transaction_limit', 50000)
            })
            
            file_path = export_dir / 'transactions.json'
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(transactions, f, indent=2, ensure_ascii=False, default=str)
            
            return {
                'file_path': str(file_path),
                'record_count': len(transactions),
                'file_size_bytes': file_path.stat().st_size
            }
        
        except Exception as e:
            logger.error(f"Failed to export transactions: {e}")
            raise
    
    async def _export_market_data(self, export_dir: Path, config: Dict[str, Any]) -> Dict[str, Any]:
        """导出市场数据"""
        try:
            # 获取指定时间范围的市场数据
            days = config.get('market_data_days', 30)
            since = datetime.now(timezone.utc) - timedelta(days=days)
            
            sql = """
                SELECT symbol, timestamp, price_usd, volume_24h, market_cap, price_change_24h
                FROM market_data 
                WHERE timestamp >= :since_date
                ORDER BY timestamp DESC
                LIMIT :limit
            """
            
            market_data = db_manager.execute_raw_sql(sql, {
                'since_date': since,
                'limit': config.get('market_data_limit', 100000)
            })
            
            file_path = export_dir / 'market_data.json'
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(market_data, f, indent=2, ensure_ascii=False, default=str)
            
            return {
                'file_path': str(file_path),
                'record_count': len(market_data),
                'file_size_bytes': file_path.stat().st_size
            }
        
        except Exception as e:
            logger.error(f"Failed to export market data: {e}")
            raise
    
    async def get_system_stats(self) -> Dict[str, Any]:
        """获取系统统计信息"""
        try:
            stats = {
                'timestamp': datetime.now(timezone.utc),
                'database_health': db_manager.health_check(),
                'table_stats': {}
            }
            
            # 获取各表的记录数
            tables = [
                'addresses', 'transactions', 'tokens', 'market_data',
                'smart_money_activities', 'whale_alerts', 'trading_signals'
            ]
            
            for table in tables:
                try:
                    result = db_manager.execute_raw_sql(f"SELECT COUNT(*) as count FROM {table}")
                    stats['table_stats'][table] = result[0]['count'] if result else 0
                except Exception as e:
                    logger.error(f"Failed to get count for table {table}: {e}")
                    stats['table_stats'][table] = -1
            
            # 获取最近24小时的活动统计
            stats['recent_activity'] = await self._get_recent_activity_stats()
            
            return stats
        
        except Exception as e:
            logger.error(f"Failed to get system stats: {e}")
            raise
    
    async def _get_recent_activity_stats(self) -> Dict[str, Any]:
        """获取最近活动统计"""
        try:
            since = datetime.now(timezone.utc) - timedelta(hours=24)
            
            # 最近24小时的交易数
            tx_result = db_manager.execute_raw_sql(
                "SELECT COUNT(*) as count FROM transactions WHERE block_timestamp >= :since",
                {'since': since}
            )
            recent_transactions = tx_result[0]['count'] if tx_result else 0
            
            # 最近24小时的巨鲸预警数
            alert_result = db_manager.execute_raw_sql(
                "SELECT COUNT(*) as count FROM whale_alerts WHERE created_at >= :since",
                {'since': since}
            )
            recent_whale_alerts = alert_result[0]['count'] if alert_result else 0
            
            # 最近24小时的Smart Money活动数
            sm_result = db_manager.execute_raw_sql(
                "SELECT COUNT(*) as count FROM smart_money_activities WHERE created_at >= :since",
                {'since': since}
            )
            recent_smart_money_activities = sm_result[0]['count'] if sm_result else 0
            
            return {
                'recent_transactions': recent_transactions,
                'recent_whale_alerts': recent_whale_alerts,
                'recent_smart_money_activities': recent_smart_money_activities,
                'period_hours': 24
            }
        
        except Exception as e:
            logger.error(f"Failed to get recent activity stats: {e}")
            return {}
