"""
风险评估引擎
评估投资组合风险、计算风险指标和提供风险建议
"""
import asyncio
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timezone, timedelta
from dataclasses import dataclass
from enum import Enum

from src.database.repositories import MarketDataRepository, TransactionRepository
from src.utils.logger import get_logger

logger = get_logger(__name__)


class RiskLevel(Enum):
    """风险等级"""
    VERY_LOW = "very_low"
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    VERY_HIGH = "very_high"


@dataclass
class RiskMetrics:
    """风险指标"""
    symbol: str
    timestamp: datetime
    
    # 价格风险
    volatility: float
    beta: float
    var_95: float
    var_99: float
    cvar_95: float
    cvar_99: float
    
    # 流动性风险
    liquidity_score: float
    bid_ask_spread: float
    market_depth: float
    
    # 信用风险
    credit_score: float
    counterparty_risk: float
    
    # 操作风险
    operational_risk: float
    smart_contract_risk: float
    
    # 综合风险
    overall_risk_score: float
    risk_level: RiskLevel


@dataclass
class PortfolioRisk:
    """投资组合风险"""
    timestamp: datetime
    total_value: float
    
    # 风险指标
    portfolio_volatility: float
    portfolio_var: float
    portfolio_cvar: float
    max_drawdown: float
    sharpe_ratio: float
    
    # 集中度风险
    concentration_risk: float
    largest_position_pct: float
    top_5_positions_pct: float
    
    # 相关性风险
    avg_correlation: float
    max_correlation: float
    
    # 流动性风险
    liquidity_risk: float
    
    # 综合评估
    overall_risk_level: RiskLevel
    risk_recommendations: List[str]


class RiskEngine:
    """风险评估引擎"""
    
    def __init__(self):
        self.market_data_repo = MarketDataRepository()
        self.transaction_repo = TransactionRepository()
        
        # 风险参数
        self.risk_params = {
            'volatility_threshold': {
                'very_low': 0.10,
                'low': 0.20,
                'medium': 0.35,
                'high': 0.50,
                'very_high': float('inf')
            },
            'var_threshold': {
                'very_low': 0.02,
                'low': 0.05,
                'medium': 0.10,
                'high': 0.15,
                'very_high': float('inf')
            },
            'concentration_threshold': {
                'very_low': 0.10,
                'low': 0.20,
                'medium': 0.35,
                'high': 0.50,
                'very_high': float('inf')
            }
        }
    
    async def assess_asset_risk(self, symbol: str, days: int = 30) -> RiskMetrics:
        """评估单个资产风险"""
        try:
            logger.info(f"Assessing risk for {symbol}")
            
            # 获取历史价格数据
            price_history = self.market_data_repo.get_price_history(symbol, days=days)
            
            if not price_history:
                logger.warning(f"No price history found for {symbol}")
                return self._create_default_risk_metrics(symbol)
            
            # 转换为DataFrame
            df = pd.DataFrame([
                {
                    'timestamp': p.timestamp,
                    'price': p.price_usd,
                    'volume': p.volume_24h or 0,
                    'market_cap': p.market_cap or 0
                }
                for p in price_history
            ])
            
            df = df.sort_values('timestamp').reset_index(drop=True)
            
            # 计算收益率
            df['returns'] = df['price'].pct_change().dropna()
            
            # 计算各种风险指标
            volatility = self._calculate_volatility(df['returns'])
            beta = self._calculate_beta(df['returns'])
            var_95, var_99 = self._calculate_var(df['returns'])
            cvar_95, cvar_99 = self._calculate_cvar(df['returns'])
            
            # 流动性风险
            liquidity_score = self._calculate_liquidity_score(df)
            bid_ask_spread = self._estimate_bid_ask_spread(df)
            market_depth = self._calculate_market_depth(df)
            
            # 信用风险
            credit_score = self._calculate_credit_score(symbol)
            counterparty_risk = self._calculate_counterparty_risk(symbol)
            
            # 操作风险
            operational_risk = self._calculate_operational_risk(symbol)
            smart_contract_risk = self._calculate_smart_contract_risk(symbol)
            
            # 综合风险评分
            overall_risk_score = self._calculate_overall_risk_score(
                volatility, var_95, liquidity_score, credit_score
            )
            
            risk_level = self._determine_risk_level(overall_risk_score)
            
            return RiskMetrics(
                symbol=symbol,
                timestamp=datetime.now(timezone.utc),
                volatility=volatility,
                beta=beta,
                var_95=var_95,
                var_99=var_99,
                cvar_95=cvar_95,
                cvar_99=cvar_99,
                liquidity_score=liquidity_score,
                bid_ask_spread=bid_ask_spread,
                market_depth=market_depth,
                credit_score=credit_score,
                counterparty_risk=counterparty_risk,
                operational_risk=operational_risk,
                smart_contract_risk=smart_contract_risk,
                overall_risk_score=overall_risk_score,
                risk_level=risk_level
            )
        
        except Exception as e:
            logger.error(f"Failed to assess risk for {symbol}: {e}")
            return self._create_default_risk_metrics(symbol)
    
    async def assess_portfolio_risk(self, positions: Dict[str, Dict[str, float]], 
                                  days: int = 30) -> PortfolioRisk:
        """评估投资组合风险"""
        try:
            logger.info("Assessing portfolio risk")
            
            if not positions:
                return self._create_default_portfolio_risk()
            
            # 计算总价值
            total_value = sum(pos['value'] for pos in positions.values())
            
            # 获取各资产的收益率数据
            returns_data = {}
            weights = {}
            
            for symbol, position in positions.items():
                weight = position['value'] / total_value
                weights[symbol] = weight
                
                # 获取收益率数据
                price_history = self.market_data_repo.get_price_history(symbol, days=days)
                if price_history:
                    prices = [p.price_usd for p in price_history]
                    returns = pd.Series(prices).pct_change().dropna()
                    returns_data[symbol] = returns
            
            if not returns_data:
                return self._create_default_portfolio_risk()
            
            # 构建收益率矩阵
            returns_df = pd.DataFrame(returns_data).dropna()
            
            # 计算投资组合收益率
            portfolio_returns = (returns_df * pd.Series(weights)).sum(axis=1)
            
            # 计算风险指标
            portfolio_volatility = portfolio_returns.std() * np.sqrt(252)
            portfolio_var = np.percentile(portfolio_returns, 5)
            portfolio_cvar = portfolio_returns[portfolio_returns <= portfolio_var].mean()
            
            # 计算最大回撤
            cumulative_returns = (1 + portfolio_returns).cumprod()
            peak = cumulative_returns.expanding().max()
            drawdown = (cumulative_returns - peak) / peak
            max_drawdown = drawdown.min()
            
            # 夏普比率
            risk_free_rate = 0.02 / 252  # 日无风险利率
            excess_returns = portfolio_returns - risk_free_rate
            sharpe_ratio = excess_returns.mean() / portfolio_returns.std() * np.sqrt(252)
            
            # 集中度风险
            position_weights = list(weights.values())
            concentration_risk = self._calculate_concentration_risk(position_weights)
            largest_position_pct = max(position_weights)
            top_5_positions_pct = sum(sorted(position_weights, reverse=True)[:5])
            
            # 相关性风险
            correlation_matrix = returns_df.corr()
            avg_correlation = correlation_matrix.values[np.triu_indices_from(correlation_matrix.values, k=1)].mean()
            max_correlation = correlation_matrix.values[np.triu_indices_from(correlation_matrix.values, k=1)].max()
            
            # 流动性风险
            liquidity_risk = await self._calculate_portfolio_liquidity_risk(positions)
            
            # 综合风险等级
            overall_risk_level = self._determine_portfolio_risk_level(
                portfolio_volatility, concentration_risk, avg_correlation
            )
            
            # 风险建议
            risk_recommendations = self._generate_risk_recommendations(
                portfolio_volatility, concentration_risk, avg_correlation, 
                largest_position_pct, liquidity_risk
            )
            
            return PortfolioRisk(
                timestamp=datetime.now(timezone.utc),
                total_value=total_value,
                portfolio_volatility=portfolio_volatility,
                portfolio_var=portfolio_var,
                portfolio_cvar=portfolio_cvar,
                max_drawdown=max_drawdown,
                sharpe_ratio=sharpe_ratio,
                concentration_risk=concentration_risk,
                largest_position_pct=largest_position_pct,
                top_5_positions_pct=top_5_positions_pct,
                avg_correlation=avg_correlation,
                max_correlation=max_correlation,
                liquidity_risk=liquidity_risk,
                overall_risk_level=overall_risk_level,
                risk_recommendations=risk_recommendations
            )
        
        except Exception as e:
            logger.error(f"Failed to assess portfolio risk: {e}")
            return self._create_default_portfolio_risk()
    
    def _calculate_volatility(self, returns: pd.Series) -> float:
        """计算年化波动率"""
        return returns.std() * np.sqrt(252)
    
    def _calculate_beta(self, returns: pd.Series) -> float:
        """计算Beta值（相对于市场）"""
        # 简化处理，返回1.0
        # 在实际实现中，需要市场基准数据
        return 1.0
    
    def _calculate_var(self, returns: pd.Series) -> Tuple[float, float]:
        """计算VaR (Value at Risk)"""
        var_95 = np.percentile(returns, 5)
        var_99 = np.percentile(returns, 1)
        return var_95, var_99
    
    def _calculate_cvar(self, returns: pd.Series) -> Tuple[float, float]:
        """计算CVaR (Conditional Value at Risk)"""
        var_95, var_99 = self._calculate_var(returns)
        cvar_95 = returns[returns <= var_95].mean()
        cvar_99 = returns[returns <= var_99].mean()
        return cvar_95, cvar_99
    
    def _calculate_liquidity_score(self, df: pd.DataFrame) -> float:
        """计算流动性评分"""
        try:
            # 基于成交量和价格稳定性计算流动性
            avg_volume = df['volume'].mean()
            volume_stability = 1 - df['volume'].std() / avg_volume if avg_volume > 0 else 0
            
            price_stability = 1 - df['returns'].std() if 'returns' in df.columns else 0.5
            
            # 综合流动性评分 (0-1)
            liquidity_score = (volume_stability * 0.6 + price_stability * 0.4)
            return max(0, min(1, liquidity_score))
        
        except Exception as e:
            logger.error(f"Failed to calculate liquidity score: {e}")
            return 0.5
    
    def _estimate_bid_ask_spread(self, df: pd.DataFrame) -> float:
        """估算买卖价差"""
        # 简化处理：基于价格波动估算
        if 'returns' in df.columns:
            return df['returns'].std() * 0.1  # 假设价差为波动率的10%
        return 0.001  # 默认0.1%
    
    def _calculate_market_depth(self, df: pd.DataFrame) -> float:
        """计算市场深度"""
        # 简化处理：基于成交量
        avg_volume = df['volume'].mean()
        return min(1.0, avg_volume / 1000000)  # 标准化到0-1
    
    def _calculate_credit_score(self, symbol: str) -> float:
        """计算信用评分"""
        # 简化处理：基于资产类型
        if symbol in ['BTC', 'ETH']:
            return 0.9  # 主流币信用评分高
        elif symbol in ['BNB', 'ADA', 'DOT']:
            return 0.8  # 知名币种
        else:
            return 0.6  # 其他币种
    
    def _calculate_counterparty_risk(self, symbol: str) -> float:
        """计算交易对手风险"""
        # 简化处理：去中心化程度
        decentralization_scores = {
            'BTC': 0.1,  # 高度去中心化
            'ETH': 0.2,
            'ADA': 0.3,
            'DOT': 0.3,
            'BNB': 0.5   # 相对中心化
        }
        return decentralization_scores.get(symbol, 0.4)
    
    def _calculate_operational_risk(self, symbol: str) -> float:
        """计算操作风险"""
        # 基于技术成熟度和开发活跃度
        return 0.2  # 简化处理
    
    def _calculate_smart_contract_risk(self, symbol: str) -> float:
        """计算智能合约风险"""
        # 基于智能合约审计和历史漏洞
        if symbol in ['BTC']:
            return 0.0  # 无智能合约
        elif symbol in ['ETH']:
            return 0.1  # 成熟的智能合约平台
        else:
            return 0.3  # 其他智能合约平台
    
    def _calculate_overall_risk_score(self, volatility: float, var_95: float, 
                                    liquidity_score: float, credit_score: float) -> float:
        """计算综合风险评分"""
        # 标准化各指标
        vol_score = min(1.0, volatility / 0.5)  # 50%波动率为满分
        var_score = min(1.0, abs(var_95) / 0.1)  # 10% VaR为满分
        liquidity_risk = 1 - liquidity_score
        credit_risk = 1 - credit_score
        
        # 加权平均
        weights = [0.3, 0.3, 0.2, 0.2]  # 波动率、VaR、流动性、信用
        overall_score = (vol_score * weights[0] + var_score * weights[1] + 
                        liquidity_risk * weights[2] + credit_risk * weights[3])
        
        return min(1.0, overall_score)
    
    def _determine_risk_level(self, risk_score: float) -> RiskLevel:
        """确定风险等级"""
        if risk_score < 0.2:
            return RiskLevel.VERY_LOW
        elif risk_score < 0.4:
            return RiskLevel.LOW
        elif risk_score < 0.6:
            return RiskLevel.MEDIUM
        elif risk_score < 0.8:
            return RiskLevel.HIGH
        else:
            return RiskLevel.VERY_HIGH
    
    def _calculate_concentration_risk(self, weights: List[float]) -> float:
        """计算集中度风险（赫芬达尔指数）"""
        return sum(w**2 for w in weights)
    
    async def _calculate_portfolio_liquidity_risk(self, positions: Dict[str, Dict[str, float]]) -> float:
        """计算投资组合流动性风险"""
        try:
            total_value = sum(pos['value'] for pos in positions.values())
            weighted_liquidity_risk = 0
            
            for symbol, position in positions.items():
                weight = position['value'] / total_value
                # 获取资产流动性评分
                asset_risk = await self.assess_asset_risk(symbol, days=7)
                liquidity_risk = 1 - asset_risk.liquidity_score
                weighted_liquidity_risk += weight * liquidity_risk
            
            return weighted_liquidity_risk
        
        except Exception as e:
            logger.error(f"Failed to calculate portfolio liquidity risk: {e}")
            return 0.5
    
    def _determine_portfolio_risk_level(self, volatility: float, concentration: float, 
                                      correlation: float) -> RiskLevel:
        """确定投资组合风险等级"""
        risk_factors = [
            volatility / 0.3,  # 30%波动率为高风险
            concentration / 0.5,  # 50%集中度为高风险
            max(0, correlation) / 0.8  # 80%相关性为高风险
        ]
        
        avg_risk = np.mean(risk_factors)
        
        if avg_risk < 0.3:
            return RiskLevel.LOW
        elif avg_risk < 0.6:
            return RiskLevel.MEDIUM
        elif avg_risk < 0.8:
            return RiskLevel.HIGH
        else:
            return RiskLevel.VERY_HIGH
    
    def _generate_risk_recommendations(self, volatility: float, concentration: float,
                                     correlation: float, largest_position: float,
                                     liquidity_risk: float) -> List[str]:
        """生成风险管理建议"""
        recommendations = []
        
        if volatility > 0.3:
            recommendations.append("投资组合波动率较高，建议降低风险资产配置")
        
        if concentration > 0.5:
            recommendations.append("投资组合集中度过高，建议增加资产多样化")
        
        if largest_position > 0.4:
            recommendations.append("单一资产占比过大，建议分散投资")
        
        if correlation > 0.7:
            recommendations.append("资产间相关性过高，建议选择相关性较低的资产")
        
        if liquidity_risk > 0.6:
            recommendations.append("投资组合流动性风险较高，建议增加流动性较好的资产")
        
        if not recommendations:
            recommendations.append("当前风险水平可接受，建议继续监控")
        
        return recommendations
    
    def _create_default_risk_metrics(self, symbol: str) -> RiskMetrics:
        """创建默认风险指标"""
        return RiskMetrics(
            symbol=symbol,
            timestamp=datetime.now(timezone.utc),
            volatility=0.3,
            beta=1.0,
            var_95=-0.05,
            var_99=-0.08,
            cvar_95=-0.07,
            cvar_99=-0.10,
            liquidity_score=0.5,
            bid_ask_spread=0.001,
            market_depth=0.5,
            credit_score=0.7,
            counterparty_risk=0.3,
            operational_risk=0.2,
            smart_contract_risk=0.2,
            overall_risk_score=0.5,
            risk_level=RiskLevel.MEDIUM
        )
    
    def _create_default_portfolio_risk(self) -> PortfolioRisk:
        """创建默认投资组合风险"""
        return PortfolioRisk(
            timestamp=datetime.now(timezone.utc),
            total_value=0,
            portfolio_volatility=0.3,
            portfolio_var=-0.05,
            portfolio_cvar=-0.07,
            max_drawdown=-0.1,
            sharpe_ratio=0.5,
            concentration_risk=0.5,
            largest_position_pct=0.3,
            top_5_positions_pct=0.8,
            avg_correlation=0.5,
            max_correlation=0.8,
            liquidity_risk=0.3,
            overall_risk_level=RiskLevel.MEDIUM,
            risk_recommendations=["无足够数据进行风险评估"]
        )
