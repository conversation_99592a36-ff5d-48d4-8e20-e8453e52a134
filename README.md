# 链上数据分析交易体系

## 项目简介

这是一个基于链上数据分析的数字货币交易体系，旨在通过分析区块链上的交易数据、资金流向、Smart Money行为等信息，构建具有正向期望值的交易策略。

## 项目结构

```
├── README.md                 # 项目说明文档
├── requirements.txt          # Python依赖包列表
├── config/                   # 配置文件目录
│   ├── __init__.py
│   ├── settings.py          # 主配置文件
│   └── api_keys.py          # API密钥配置
├── src/                     # 源代码目录
│   ├── __init__.py
│   ├── data/                # 数据获取模块
│   │   ├── __init__.py
│   │   ├── blockchain_api.py    # 区块链API接口
│   │   ├── defi_data.py        # DeFi数据获取
│   │   └── market_data.py      # 市场数据获取
│   ├── database/            # 数据库模块
│   │   ├── __init__.py
│   │   ├── models.py        # 数据模型
│   │   └── database.py      # 数据库操作
│   ├── analysis/            # 数据分析模块
│   │   ├── __init__.py
│   │   ├── smart_money.py   # Smart Money分析
│   │   ├── whale_tracking.py # 巨鲸追踪
│   │   └── indicators.py    # 技术指标计算
│   ├── monitoring/          # 监控模块
│   │   ├── __init__.py
│   │   ├── alerts.py        # 预警系统
│   │   └── real_time.py     # 实时监控
│   ├── backtesting/         # 回测模块
│   │   ├── __init__.py
│   │   ├── engine.py        # 回测引擎
│   │   └── strategies.py    # 交易策略
│   ├── risk_management/     # 风险管理模块
│   │   ├── __init__.py
│   │   ├── position_sizing.py # 仓位管理
│   │   └── risk_metrics.py    # 风险指标
│   └── utils/               # 工具模块
│       ├── __init__.py
│       ├── logger.py        # 日志工具
│       └── helpers.py       # 辅助函数
├── tests/                   # 测试目录
│   ├── __init__.py
│   ├── test_data/          # 数据模块测试
│   ├── test_analysis/      # 分析模块测试
│   └── test_backtesting/   # 回测模块测试
├── data/                    # 数据存储目录
│   ├── raw/                # 原始数据
│   ├── processed/          # 处理后数据
│   └── cache/              # 缓存数据
├── logs/                    # 日志目录
├── notebooks/               # Jupyter笔记本
└── web/                     # Web界面
    ├── app.py              # Flask应用
    ├── templates/          # HTML模板
    └── static/             # 静态文件
```

## 功能特性

### 第一阶段 - 基础建设
- [x] 项目初始化和环境配置
- [ ] 数据获取模块开发
- [ ] 数据存储和管理系统
- [ ] Smart Money地址分析模块
- [ ] 巨鲸行为监控系统
- [ ] 技术指标计算引擎
- [ ] 实时监控和预警系统
- [ ] 回测框架开发
- [ ] 风险管理模块
- [ ] 用户界面和可视化

### 核心功能
1. **数据获取**: 从多个数据源获取链上数据
2. **Smart Money追踪**: 识别和跟踪成功交易者
3. **巨鲸监控**: 监控大额交易和资金流向
4. **技术分析**: 计算链上技术指标
5. **实时预警**: 异常情况实时通知
6. **策略回测**: 验证交易策略有效性
7. **风险管理**: 完善的风险控制体系

## 安装和使用

### 环境要求
- Python 3.8+
- PostgreSQL 12+
- Redis 6+

### 安装步骤
1. 克隆项目
2. 安装依赖: `pip install -r requirements.txt`
3. 配置数据库和API密钥
4. 运行初始化脚本
5. 启动服务

## 配置说明

在 `config/api_keys.py` 中配置必要的API密钥：
- Etherscan API Key
- Moralis API Key
- CoinGecko API Key
- 其他数据源API密钥

## 许可证

MIT License

## 贡献指南

欢迎提交Issue和Pull Request来改进项目。

## 联系方式

如有问题，请通过Issue联系。
