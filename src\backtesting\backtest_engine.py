"""
回测引擎核心模块
提供策略回测的核心功能
"""
import asyncio
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Tuple, Callable
from datetime import datetime, timezone, timedelta
from dataclasses import dataclass, field
from enum import Enum
import copy

from src.database.repositories import MarketDataRepository, TransactionRepository
from src.utils.logger import get_logger

logger = get_logger(__name__)


class OrderType(Enum):
    """订单类型"""
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"


class OrderSide(Enum):
    """订单方向"""
    BUY = "buy"
    SELL = "sell"


class OrderStatus(Enum):
    """订单状态"""
    PENDING = "pending"
    FILLED = "filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"


@dataclass
class Order:
    """订单"""
    id: str
    symbol: str
    side: OrderSide
    order_type: OrderType
    quantity: float
    price: Optional[float] = None
    stop_price: Optional[float] = None
    timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    status: OrderStatus = OrderStatus.PENDING
    filled_quantity: float = 0.0
    filled_price: float = 0.0
    commission: float = 0.0


@dataclass
class Trade:
    """交易记录"""
    id: str
    order_id: str
    symbol: str
    side: OrderSide
    quantity: float
    price: float
    timestamp: datetime
    commission: float
    pnl: float = 0.0


@dataclass
class Position:
    """持仓"""
    symbol: str
    quantity: float = 0.0
    avg_price: float = 0.0
    unrealized_pnl: float = 0.0
    realized_pnl: float = 0.0
    
    def update_position(self, trade: Trade) -> None:
        """更新持仓"""
        if trade.side == OrderSide.BUY:
            # 买入
            total_cost = self.quantity * self.avg_price + trade.quantity * trade.price
            self.quantity += trade.quantity
            self.avg_price = total_cost / self.quantity if self.quantity > 0 else 0
        else:
            # 卖出
            if self.quantity > 0:
                self.realized_pnl += (trade.price - self.avg_price) * trade.quantity
            self.quantity -= trade.quantity
            
            if self.quantity <= 0:
                self.quantity = 0
                self.avg_price = 0
    
    def calculate_unrealized_pnl(self, current_price: float) -> float:
        """计算未实现盈亏"""
        if self.quantity > 0:
            self.unrealized_pnl = (current_price - self.avg_price) * self.quantity
        else:
            self.unrealized_pnl = 0
        return self.unrealized_pnl


@dataclass
class Portfolio:
    """投资组合"""
    initial_capital: float
    cash: float
    positions: Dict[str, Position] = field(default_factory=dict)
    trades: List[Trade] = field(default_factory=list)
    equity_curve: List[Tuple[datetime, float]] = field(default_factory=list)
    
    def get_position(self, symbol: str) -> Position:
        """获取持仓"""
        if symbol not in self.positions:
            self.positions[symbol] = Position(symbol=symbol)
        return self.positions[symbol]
    
    def update_portfolio(self, trade: Trade, current_prices: Dict[str, float]) -> None:
        """更新投资组合"""
        # 更新持仓
        position = self.get_position(trade.symbol)
        position.update_position(trade)
        
        # 更新现金
        if trade.side == OrderSide.BUY:
            self.cash -= trade.quantity * trade.price + trade.commission
        else:
            self.cash += trade.quantity * trade.price - trade.commission
        
        # 记录交易
        self.trades.append(trade)
        
        # 更新权益曲线
        total_value = self.calculate_total_value(current_prices)
        self.equity_curve.append((trade.timestamp, total_value))
    
    def calculate_total_value(self, current_prices: Dict[str, float]) -> float:
        """计算总价值"""
        total_value = self.cash
        
        for symbol, position in self.positions.items():
            if position.quantity > 0 and symbol in current_prices:
                position.calculate_unrealized_pnl(current_prices[symbol])
                total_value += position.quantity * current_prices[symbol]
        
        return total_value


@dataclass
class BacktestConfig:
    """回测配置"""
    start_date: datetime
    end_date: datetime
    initial_capital: float = 100000.0
    commission_rate: float = 0.001  # 0.1%
    slippage_rate: float = 0.0005   # 0.05%
    max_positions: int = 10
    position_size_pct: float = 0.1  # 10%
    rebalance_frequency: str = 'daily'  # daily, weekly, monthly


class BacktestEngine:
    """回测引擎"""
    
    def __init__(self, config: BacktestConfig):
        self.config = config
        self.market_data_repo = MarketDataRepository()
        self.transaction_repo = TransactionRepository()
        
        # 回测状态
        self.portfolio = Portfolio(
            initial_capital=config.initial_capital,
            cash=config.initial_capital
        )
        self.orders: List[Order] = []
        self.current_time: Optional[datetime] = None
        self.current_prices: Dict[str, float] = {}
        
        # 策略函数
        self.strategy_func: Optional[Callable] = None
        
        # 回测数据
        self.price_data: Dict[str, pd.DataFrame] = {}
        self.data_index = 0
        
        # 性能统计
        self.stats = {}
    
    def set_strategy(self, strategy_func: Callable) -> None:
        """设置策略函数"""
        self.strategy_func = strategy_func
    
    async def load_data(self, symbols: List[str]) -> None:
        """加载历史数据"""
        try:
            logger.info(f"Loading historical data for {len(symbols)} symbols")
            
            for symbol in symbols:
                # 计算需要的天数
                days = (self.config.end_date - self.config.start_date).days + 1
                
                # 获取价格数据
                price_history = self.market_data_repo.get_price_history(
                    symbol, 
                    days=days,
                    start_date=self.config.start_date,
                    end_date=self.config.end_date
                )
                
                if price_history:
                    # 转换为DataFrame
                    df = pd.DataFrame([
                        {
                            'timestamp': p.timestamp,
                            'open': p.price_usd,  # 简化处理
                            'high': p.price_usd * 1.02,
                            'low': p.price_usd * 0.98,
                            'close': p.price_usd,
                            'volume': p.volume_24h or 0
                        }
                        for p in price_history
                    ])
                    
                    df = df.sort_values('timestamp').reset_index(drop=True)
                    self.price_data[symbol] = df
                    
                    logger.info(f"Loaded {len(df)} data points for {symbol}")
                else:
                    logger.warning(f"No price data found for {symbol}")
            
            logger.info("Historical data loading completed")
        
        except Exception as e:
            logger.error(f"Failed to load historical data: {e}")
            raise
    
    async def run_backtest(self, symbols: List[str]) -> Dict[str, Any]:
        """运行回测"""
        try:
            logger.info("Starting backtest")
            
            # 加载数据
            await self.load_data(symbols)
            
            if not self.price_data:
                raise ValueError("No price data available for backtesting")
            
            # 获取所有时间戳并排序
            all_timestamps = set()
            for df in self.price_data.values():
                all_timestamps.update(df['timestamp'].tolist())
            
            timestamps = sorted(all_timestamps)
            
            # 逐个时间点执行回测
            for i, timestamp in enumerate(timestamps):
                self.current_time = timestamp
                self.data_index = i
                
                # 更新当前价格
                self._update_current_prices(timestamp)
                
                # 处理待执行订单
                self._process_orders()
                
                # 执行策略
                if self.strategy_func:
                    await self._execute_strategy()
                
                # 更新投资组合价值
                if i % 100 == 0:  # 每100个时间点记录一次
                    total_value = self.portfolio.calculate_total_value(self.current_prices)
                    self.portfolio.equity_curve.append((timestamp, total_value))
            
            # 计算最终统计
            self.stats = self._calculate_statistics()
            
            logger.info("Backtest completed successfully")
            return self.stats
        
        except Exception as e:
            logger.error(f"Backtest failed: {e}")
            raise
    
    def _update_current_prices(self, timestamp: datetime) -> None:
        """更新当前价格"""
        for symbol, df in self.price_data.items():
            # 找到最接近的价格数据
            mask = df['timestamp'] <= timestamp
            if mask.any():
                latest_row = df[mask].iloc[-1]
                self.current_prices[symbol] = latest_row['close']
    
    def _process_orders(self) -> None:
        """处理订单"""
        for order in self.orders:
            if order.status == OrderStatus.PENDING:
                if self._should_fill_order(order):
                    self._fill_order(order)
    
    def _should_fill_order(self, order: Order) -> bool:
        """判断订单是否应该成交"""
        if order.symbol not in self.current_prices:
            return False
        
        current_price = self.current_prices[order.symbol]
        
        if order.order_type == OrderType.MARKET:
            return True
        elif order.order_type == OrderType.LIMIT:
            if order.side == OrderSide.BUY:
                return current_price <= order.price
            else:
                return current_price >= order.price
        elif order.order_type == OrderType.STOP:
            if order.side == OrderSide.BUY:
                return current_price >= order.stop_price
            else:
                return current_price <= order.stop_price
        
        return False
    
    def _fill_order(self, order: Order) -> None:
        """成交订单"""
        current_price = self.current_prices[order.symbol]
        
        # 计算成交价格（考虑滑点）
        if order.order_type == OrderType.MARKET:
            if order.side == OrderSide.BUY:
                fill_price = current_price * (1 + self.config.slippage_rate)
            else:
                fill_price = current_price * (1 - self.config.slippage_rate)
        else:
            fill_price = order.price or current_price
        
        # 计算手续费
        commission = order.quantity * fill_price * self.config.commission_rate
        
        # 检查资金是否足够
        if order.side == OrderSide.BUY:
            required_cash = order.quantity * fill_price + commission
            if self.portfolio.cash < required_cash:
                order.status = OrderStatus.REJECTED
                return
        
        # 创建交易记录
        trade = Trade(
            id=f"trade_{len(self.portfolio.trades) + 1}",
            order_id=order.id,
            symbol=order.symbol,
            side=order.side,
            quantity=order.quantity,
            price=fill_price,
            timestamp=self.current_time,
            commission=commission
        )
        
        # 更新订单状态
        order.status = OrderStatus.FILLED
        order.filled_quantity = order.quantity
        order.filled_price = fill_price
        order.commission = commission
        
        # 更新投资组合
        self.portfolio.update_portfolio(trade, self.current_prices)
        
        logger.debug(f"Order filled: {order.side.value} {order.quantity} {order.symbol} @ {fill_price:.4f}")
    
    async def _execute_strategy(self) -> None:
        """执行策略"""
        try:
            if self.strategy_func:
                # 准备策略数据
                strategy_data = {
                    'current_time': self.current_time,
                    'current_prices': self.current_prices.copy(),
                    'portfolio': copy.deepcopy(self.portfolio),
                    'price_data': {symbol: df.iloc[:self.data_index+1].copy() 
                                 for symbol, df in self.price_data.items()},
                    'config': self.config
                }
                
                # 执行策略
                signals = await self.strategy_func(strategy_data)
                
                # 处理策略信号
                if signals:
                    await self._process_strategy_signals(signals)
        
        except Exception as e:
            logger.error(f"Strategy execution failed: {e}")
    
    async def _process_strategy_signals(self, signals: List[Dict[str, Any]]) -> None:
        """处理策略信号"""
        for signal in signals:
            try:
                symbol = signal['symbol']
                action = signal['action']  # buy, sell, hold
                
                if action == 'buy':
                    await self._create_buy_order(symbol, signal)
                elif action == 'sell':
                    await self._create_sell_order(symbol, signal)
            
            except Exception as e:
                logger.error(f"Failed to process signal: {e}")
    
    async def _create_buy_order(self, symbol: str, signal: Dict[str, Any]) -> None:
        """创建买入订单"""
        if symbol not in self.current_prices:
            return
        
        current_price = self.current_prices[symbol]
        
        # 计算仓位大小
        position_value = self.portfolio.cash * self.config.position_size_pct
        quantity = position_value / current_price
        
        # 检查最大持仓数限制
        active_positions = len([p for p in self.portfolio.positions.values() if p.quantity > 0])
        if active_positions >= self.config.max_positions:
            return
        
        # 创建订单
        order = Order(
            id=f"order_{len(self.orders) + 1}",
            symbol=symbol,
            side=OrderSide.BUY,
            order_type=OrderType.MARKET,
            quantity=quantity,
            timestamp=self.current_time
        )
        
        self.orders.append(order)
    
    async def _create_sell_order(self, symbol: str, signal: Dict[str, Any]) -> None:
        """创建卖出订单"""
        position = self.portfolio.get_position(symbol)
        
        if position.quantity <= 0:
            return
        
        # 创建卖出订单
        order = Order(
            id=f"order_{len(self.orders) + 1}",
            symbol=symbol,
            side=OrderSide.SELL,
            order_type=OrderType.MARKET,
            quantity=position.quantity,
            timestamp=self.current_time
        )
        
        self.orders.append(order)
    
    def _calculate_statistics(self) -> Dict[str, Any]:
        """计算回测统计"""
        try:
            if not self.portfolio.equity_curve:
                return {}
            
            # 提取权益曲线数据
            timestamps, values = zip(*self.portfolio.equity_curve)
            equity_series = pd.Series(values, index=timestamps)
            
            # 计算收益率
            returns = equity_series.pct_change().dropna()
            
            # 基本统计
            total_return = (equity_series.iloc[-1] - self.config.initial_capital) / self.config.initial_capital
            annualized_return = (1 + total_return) ** (365 / len(equity_series)) - 1
            
            # 风险指标
            volatility = returns.std() * np.sqrt(252)  # 年化波动率
            sharpe_ratio = annualized_return / volatility if volatility > 0 else 0
            
            # 最大回撤
            peak = equity_series.expanding().max()
            drawdown = (equity_series - peak) / peak
            max_drawdown = drawdown.min()
            
            # 交易统计
            total_trades = len(self.portfolio.trades)
            winning_trades = len([t for t in self.portfolio.trades if t.pnl > 0])
            win_rate = winning_trades / total_trades if total_trades > 0 else 0
            
            # 盈亏统计
            total_pnl = sum(position.realized_pnl for position in self.portfolio.positions.values())
            avg_win = np.mean([t.pnl for t in self.portfolio.trades if t.pnl > 0]) if winning_trades > 0 else 0
            avg_loss = np.mean([t.pnl for t in self.portfolio.trades if t.pnl < 0]) if (total_trades - winning_trades) > 0 else 0
            profit_factor = abs(avg_win / avg_loss) if avg_loss != 0 else float('inf')
            
            return {
                'total_return': total_return,
                'annualized_return': annualized_return,
                'volatility': volatility,
                'sharpe_ratio': sharpe_ratio,
                'max_drawdown': max_drawdown,
                'total_trades': total_trades,
                'winning_trades': winning_trades,
                'win_rate': win_rate,
                'total_pnl': total_pnl,
                'avg_win': avg_win,
                'avg_loss': avg_loss,
                'profit_factor': profit_factor,
                'final_portfolio_value': equity_series.iloc[-1],
                'equity_curve': self.portfolio.equity_curve,
                'trades': [
                    {
                        'symbol': t.symbol,
                        'side': t.side.value,
                        'quantity': t.quantity,
                        'price': t.price,
                        'timestamp': t.timestamp,
                        'pnl': t.pnl
                    }
                    for t in self.portfolio.trades
                ]
            }
        
        except Exception as e:
            logger.error(f"Failed to calculate statistics: {e}")
            return {}
    
    def get_portfolio_summary(self) -> Dict[str, Any]:
        """获取投资组合摘要"""
        total_value = self.portfolio.calculate_total_value(self.current_prices)
        
        return {
            'initial_capital': self.config.initial_capital,
            'current_cash': self.portfolio.cash,
            'total_value': total_value,
            'total_return': (total_value - self.config.initial_capital) / self.config.initial_capital,
            'positions': {
                symbol: {
                    'quantity': pos.quantity,
                    'avg_price': pos.avg_price,
                    'current_price': self.current_prices.get(symbol, 0),
                    'unrealized_pnl': pos.unrealized_pnl,
                    'realized_pnl': pos.realized_pnl
                }
                for symbol, pos in self.portfolio.positions.items()
                if pos.quantity > 0
            },
            'total_trades': len(self.portfolio.trades)
        }
