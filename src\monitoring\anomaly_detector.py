"""
异常检测模块
使用统计方法和机器学习检测异常活动
"""
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timezone, timedelta
from dataclasses import dataclass
from collections import deque
from sklearn.ensemble import IsolationForest
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

from src.database.repositories import TransactionRepository, MarketDataRepository
from src.utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class Anomaly:
    """异常数据"""
    anomaly_type: str
    symbol: str
    timestamp: datetime
    severity: str  # low, medium, high, critical
    confidence: float
    description: str
    data: Dict[str, Any]
    suggested_actions: List[str]


@dataclass
class AnomalyPattern:
    """异常模式"""
    pattern_id: str
    pattern_type: str
    description: str
    detection_rules: Dict[str, Any]
    historical_occurrences: int
    avg_impact: float


class AnomalyDetector:
    """异常检测器"""
    
    def __init__(self):
        self.transaction_repo = TransactionRepository()
        self.market_data_repo = MarketDataRepository()
        
        # 历史数据缓存
        self.price_history = {}
        self.volume_history = {}
        self.transaction_history = {}
        
        # 异常检测模型
        self.isolation_forest = IsolationForest(contamination=0.1, random_state=42)
        self.scaler = StandardScaler()
        
        # 异常模式库
        self.anomaly_patterns = self._initialize_anomaly_patterns()
        
        # 检测阈值
        self.thresholds = {
            'price_volatility': 0.15,      # 15%价格波动
            'volume_spike': 3.0,           # 3倍成交量激增
            'transaction_anomaly': 2.5,    # 2.5倍交易异常
            'network_congestion': 0.8,     # 80%网络拥堵
            'whale_activity': 0.7          # 70%巨鲸活动阈值
        }
    
    def _initialize_anomaly_patterns(self) -> List[AnomalyPattern]:
        """初始化异常模式"""
        return [
            AnomalyPattern(
                pattern_id='flash_crash',
                pattern_type='price_anomaly',
                description='闪崩：短时间内价格急剧下跌',
                detection_rules={
                    'price_drop_threshold': 0.20,
                    'time_window_minutes': 15,
                    'volume_increase_factor': 2.0
                },
                historical_occurrences=5,
                avg_impact=0.8
            ),
            AnomalyPattern(
                pattern_id='pump_and_dump',
                pattern_type='manipulation',
                description='拉高出货：人为操纵价格',
                detection_rules={
                    'price_increase_threshold': 0.30,
                    'volume_spike_factor': 5.0,
                    'subsequent_drop_threshold': 0.25,
                    'time_window_hours': 24
                },
                historical_occurrences=12,
                avg_impact=0.9
            ),
            AnomalyPattern(
                pattern_id='whale_accumulation',
                pattern_type='whale_activity',
                description='巨鲸累积：大户持续买入',
                detection_rules={
                    'large_transaction_threshold': 1000000,
                    'transaction_frequency': 5,
                    'time_window_hours': 6,
                    'same_direction_ratio': 0.8
                },
                historical_occurrences=8,
                avg_impact=0.6
            ),
            AnomalyPattern(
                pattern_id='network_congestion',
                pattern_type='network_anomaly',
                description='网络拥堵：交易费用异常高',
                detection_rules={
                    'gas_price_multiplier': 3.0,
                    'pending_transactions_threshold': 50000,
                    'confirmation_delay_minutes': 30
                },
                historical_occurrences=15,
                avg_impact=0.4
            ),
            AnomalyPattern(
                pattern_id='coordinated_selling',
                pattern_type='market_manipulation',
                description='协调抛售：多个大户同时卖出',
                detection_rules={
                    'concurrent_large_sells': 3,
                    'time_window_minutes': 30,
                    'min_transaction_size': 500000,
                    'price_impact_threshold': 0.10
                },
                historical_occurrences=6,
                avg_impact=0.7
            )
        ]
    
    async def detect_anomalies(self, symbol: str, hours: int = 24) -> List[Anomaly]:
        """检测异常"""
        try:
            logger.info(f"Detecting anomalies for {symbol} over {hours} hours")
            
            # 获取历史数据
            await self._update_historical_data(symbol, hours)
            
            anomalies = []
            
            # 价格异常检测
            price_anomalies = await self._detect_price_anomalies(symbol)
            anomalies.extend(price_anomalies)
            
            # 成交量异常检测
            volume_anomalies = await self._detect_volume_anomalies(symbol)
            anomalies.extend(volume_anomalies)
            
            # 交易异常检测
            transaction_anomalies = await self._detect_transaction_anomalies(symbol)
            anomalies.extend(transaction_anomalies)
            
            # 网络异常检测
            network_anomalies = await self._detect_network_anomalies(symbol)
            anomalies.extend(network_anomalies)
            
            # 模式匹配检测
            pattern_anomalies = await self._detect_pattern_anomalies(symbol)
            anomalies.extend(pattern_anomalies)
            
            # 机器学习异常检测
            ml_anomalies = await self._detect_ml_anomalies(symbol)
            anomalies.extend(ml_anomalies)
            
            # 按严重程度排序
            anomalies.sort(key=lambda x: self._get_severity_score(x.severity), reverse=True)
            
            logger.info(f"Detected {len(anomalies)} anomalies for {symbol}")
            return anomalies
        
        except Exception as e:
            logger.error(f"Failed to detect anomalies for {symbol}: {e}")
            return []
    
    async def _update_historical_data(self, symbol: str, hours: int) -> None:
        """更新历史数据"""
        try:
            # 获取价格历史
            price_history = self.market_data_repo.get_price_history(symbol, days=hours//24 + 1)
            self.price_history[symbol] = price_history
            
            # 获取交易历史
            since = datetime.now(timezone.utc) - timedelta(hours=hours)
            transactions = self.transaction_repo.get_transactions_since(since, limit=10000)
            self.transaction_history[symbol] = transactions
        
        except Exception as e:
            logger.error(f"Failed to update historical data: {e}")
    
    async def _detect_price_anomalies(self, symbol: str) -> List[Anomaly]:
        """检测价格异常"""
        try:
            anomalies = []
            price_history = self.price_history.get(symbol, [])
            
            if len(price_history) < 10:
                return anomalies
            
            # 转换为DataFrame
            df = pd.DataFrame([
                {
                    'timestamp': p.timestamp,
                    'price': p.price_usd,
                    'volume': p.volume_24h or 0
                }
                for p in price_history
            ])
            
            df = df.sort_values('timestamp').reset_index(drop=True)
            
            # 计算价格变化率
            df['price_change'] = df['price'].pct_change()
            df['price_volatility'] = df['price_change'].rolling(window=5).std()
            
            # 检测闪崩
            flash_crash_threshold = -self.thresholds['price_volatility']
            flash_crashes = df[df['price_change'] < flash_crash_threshold]
            
            for _, crash in flash_crashes.iterrows():
                anomaly = Anomaly(
                    anomaly_type='flash_crash',
                    symbol=symbol,
                    timestamp=crash['timestamp'],
                    severity='high',
                    confidence=min(abs(crash['price_change']) / 0.20, 1.0),
                    description=f"检测到闪崩：价格下跌 {abs(crash['price_change']):.2%}",
                    data={
                        'price_change': crash['price_change'],
                        'price': crash['price'],
                        'volume': crash['volume']
                    },
                    suggested_actions=[
                        '检查是否有重大负面新闻',
                        '监控大额卖单',
                        '评估技术支撑位'
                    ]
                )
                anomalies.append(anomaly)
            
            # 检测异常波动
            high_volatility = df[df['price_volatility'] > self.thresholds['price_volatility']]
            
            for _, vol in high_volatility.iterrows():
                if pd.notna(vol['price_volatility']):
                    anomaly = Anomaly(
                        anomaly_type='high_volatility',
                        symbol=symbol,
                        timestamp=vol['timestamp'],
                        severity='medium',
                        confidence=min(vol['price_volatility'] / 0.20, 1.0),
                        description=f"检测到高波动：波动率 {vol['price_volatility']:.2%}",
                        data={
                            'volatility': vol['price_volatility'],
                            'price': vol['price']
                        },
                        suggested_actions=[
                            '增加风险管理',
                            '调整仓位大小',
                            '设置更严格的止损'
                        ]
                    )
                    anomalies.append(anomaly)
            
            return anomalies
        
        except Exception as e:
            logger.error(f"Failed to detect price anomalies: {e}")
            return []
    
    async def _detect_volume_anomalies(self, symbol: str) -> List[Anomaly]:
        """检测成交量异常"""
        try:
            anomalies = []
            price_history = self.price_history.get(symbol, [])
            
            if len(price_history) < 10:
                return anomalies
            
            # 计算成交量统计
            volumes = [p.volume_24h for p in price_history if p.volume_24h]
            if not volumes:
                return anomalies
            
            avg_volume = np.mean(volumes)
            std_volume = np.std(volumes)
            
            # 检测成交量激增
            for price_data in price_history[-5:]:  # 检查最近5个数据点
                if price_data.volume_24h:
                    volume_ratio = price_data.volume_24h / avg_volume
                    
                    if volume_ratio > self.thresholds['volume_spike']:
                        severity = 'high' if volume_ratio > 5.0 else 'medium'
                        
                        anomaly = Anomaly(
                            anomaly_type='volume_spike',
                            symbol=symbol,
                            timestamp=price_data.timestamp,
                            severity=severity,
                            confidence=min(volume_ratio / 5.0, 1.0),
                            description=f"检测到成交量激增：{volume_ratio:.1f}倍于平均水平",
                            data={
                                'current_volume': price_data.volume_24h,
                                'average_volume': avg_volume,
                                'volume_ratio': volume_ratio,
                                'price': price_data.price_usd
                            },
                            suggested_actions=[
                                '检查是否有重大消息',
                                '监控价格走势',
                                '分析买卖盘力量'
                            ]
                        )
                        anomalies.append(anomaly)
            
            return anomalies
        
        except Exception as e:
            logger.error(f"Failed to detect volume anomalies: {e}")
            return []
    
    async def _detect_transaction_anomalies(self, symbol: str) -> List[Anomaly]:
        """检测交易异常"""
        try:
            anomalies = []
            transactions = self.transaction_history.get(symbol, [])
            
            if len(transactions) < 10:
                return anomalies
            
            # 分析交易模式
            large_transactions = [tx for tx in transactions if getattr(tx, 'value_usd', 0) > 100000]
            
            if large_transactions:
                # 检测异常大额交易频率
                recent_large_txs = [
                    tx for tx in large_transactions
                    if (datetime.now(timezone.utc) - tx.block_timestamp).total_seconds() < 3600
                ]
                
                if len(recent_large_txs) > 5:  # 1小时内超过5笔大额交易
                    total_value = sum(getattr(tx, 'value_usd', 0) for tx in recent_large_txs)
                    
                    anomaly = Anomaly(
                        anomaly_type='unusual_large_transactions',
                        symbol=symbol,
                        timestamp=datetime.now(timezone.utc),
                        severity='medium',
                        confidence=min(len(recent_large_txs) / 10, 1.0),
                        description=f"检测到异常大额交易：1小时内{len(recent_large_txs)}笔，总价值${total_value:,.0f}",
                        data={
                            'transaction_count': len(recent_large_txs),
                            'total_value': total_value,
                            'time_window': '1 hour'
                        },
                        suggested_actions=[
                            '分析交易地址',
                            '检查是否为巨鲸活动',
                            '监控价格影响'
                        ]
                    )
                    anomalies.append(anomaly)
            
            return anomalies
        
        except Exception as e:
            logger.error(f"Failed to detect transaction anomalies: {e}")
            return []
    
    async def _detect_network_anomalies(self, symbol: str) -> List[Anomaly]:
        """检测网络异常"""
        try:
            anomalies = []
            
            # 简化的网络异常检测
            # 在实际实现中，这里会检查网络拥堵、Gas费用等
            
            # 模拟网络拥堵检测
            import random
            if random.random() < 0.1:  # 10%概率检测到网络异常
                anomaly = Anomaly(
                    anomaly_type='network_congestion',
                    symbol=symbol,
                    timestamp=datetime.now(timezone.utc),
                    severity='medium',
                    confidence=0.7,
                    description="检测到网络拥堵：交易确认时间延长",
                    data={
                        'avg_confirmation_time': 15,  # 分钟
                        'pending_transactions': 25000,
                        'gas_price_multiplier': 2.5
                    },
                    suggested_actions=[
                        '提高Gas费用',
                        '延迟非紧急交易',
                        '监控网络状态'
                    ]
                )
                anomalies.append(anomaly)
            
            return anomalies
        
        except Exception as e:
            logger.error(f"Failed to detect network anomalies: {e}")
            return []
    
    async def _detect_pattern_anomalies(self, symbol: str) -> List[Anomaly]:
        """检测模式异常"""
        try:
            anomalies = []
            
            # 遍历异常模式进行匹配
            for pattern in self.anomaly_patterns:
                if await self._match_pattern(symbol, pattern):
                    anomaly = Anomaly(
                        anomaly_type=pattern.pattern_id,
                        symbol=symbol,
                        timestamp=datetime.now(timezone.utc),
                        severity='high' if pattern.avg_impact > 0.7 else 'medium',
                        confidence=pattern.avg_impact,
                        description=pattern.description,
                        data={
                            'pattern_type': pattern.pattern_type,
                            'historical_occurrences': pattern.historical_occurrences,
                            'avg_impact': pattern.avg_impact
                        },
                        suggested_actions=[
                            '深入分析模式特征',
                            '制定应对策略',
                            '加强监控'
                        ]
                    )
                    anomalies.append(anomaly)
            
            return anomalies
        
        except Exception as e:
            logger.error(f"Failed to detect pattern anomalies: {e}")
            return []
    
    async def _match_pattern(self, symbol: str, pattern: AnomalyPattern) -> bool:
        """匹配异常模式"""
        try:
            # 简化的模式匹配逻辑
            # 在实际实现中，这里会根据具体的检测规则进行复杂的模式匹配
            
            if pattern.pattern_id == 'flash_crash':
                # 检查是否有快速价格下跌
                price_history = self.price_history.get(symbol, [])
                if len(price_history) >= 2:
                    latest = price_history[-1]
                    previous = price_history[-2]
                    price_change = (latest.price_usd - previous.price_usd) / previous.price_usd
                    return price_change < -pattern.detection_rules['price_drop_threshold']
            
            return False
        
        except Exception as e:
            logger.error(f"Failed to match pattern {pattern.pattern_id}: {e}")
            return False
    
    async def _detect_ml_anomalies(self, symbol: str) -> List[Anomaly]:
        """使用机器学习检测异常"""
        try:
            anomalies = []
            price_history = self.price_history.get(symbol, [])
            
            if len(price_history) < 50:  # 需要足够的数据训练模型
                return anomalies
            
            # 准备特征数据
            features = []
            for i, price_data in enumerate(price_history):
                if i > 0:
                    prev_price = price_history[i-1].price_usd
                    price_change = (price_data.price_usd - prev_price) / prev_price
                    
                    feature_vector = [
                        price_data.price_usd,
                        price_data.volume_24h or 0,
                        price_change,
                        price_data.market_cap or 0
                    ]
                    features.append(feature_vector)
            
            if len(features) < 20:
                return anomalies
            
            # 标准化特征
            features_array = np.array(features)
            features_scaled = self.scaler.fit_transform(features_array)
            
            # 训练异常检测模型
            self.isolation_forest.fit(features_scaled)
            
            # 预测异常
            anomaly_scores = self.isolation_forest.decision_function(features_scaled)
            predictions = self.isolation_forest.predict(features_scaled)
            
            # 找出异常点
            for i, (score, prediction) in enumerate(zip(anomaly_scores, predictions)):
                if prediction == -1:  # 异常点
                    confidence = abs(score)
                    
                    if confidence > 0.1:  # 置信度阈值
                        anomaly = Anomaly(
                            anomaly_type='ml_detected_anomaly',
                            symbol=symbol,
                            timestamp=price_history[i+1].timestamp,
                            severity='medium' if confidence > 0.2 else 'low',
                            confidence=min(confidence, 1.0),
                            description=f"机器学习检测到异常：异常分数 {score:.3f}",
                            data={
                                'anomaly_score': score,
                                'features': features[i],
                                'feature_names': ['price', 'volume', 'price_change', 'market_cap']
                            },
                            suggested_actions=[
                                '人工验证异常',
                                '分析特征重要性',
                                '调整检测参数'
                            ]
                        )
                        anomalies.append(anomaly)
            
            return anomalies
        
        except Exception as e:
            logger.error(f"Failed to detect ML anomalies: {e}")
            return []
    
    def _get_severity_score(self, severity: str) -> int:
        """获取严重程度分数"""
        severity_scores = {
            'critical': 4,
            'high': 3,
            'medium': 2,
            'low': 1
        }
        return severity_scores.get(severity, 0)
    
    async def analyze_anomaly_trends(self, symbol: str, days: int = 30) -> Dict[str, Any]:
        """分析异常趋势"""
        try:
            # 获取历史异常数据
            anomalies = await self.detect_anomalies(symbol, hours=days*24)
            
            if not anomalies:
                return {'no_data': True}
            
            # 按类型统计
            type_counts = {}
            severity_counts = {}
            
            for anomaly in anomalies:
                type_counts[anomaly.anomaly_type] = type_counts.get(anomaly.anomaly_type, 0) + 1
                severity_counts[anomaly.severity] = severity_counts.get(anomaly.severity, 0) + 1
            
            # 计算趋势
            recent_anomalies = [a for a in anomalies if (datetime.now(timezone.utc) - a.timestamp).days <= 7]
            earlier_anomalies = [a for a in anomalies if 7 < (datetime.now(timezone.utc) - a.timestamp).days <= 14]
            
            trend = 'stable'
            if len(recent_anomalies) > len(earlier_anomalies) * 1.5:
                trend = 'increasing'
            elif len(recent_anomalies) < len(earlier_anomalies) * 0.5:
                trend = 'decreasing'
            
            return {
                'total_anomalies': len(anomalies),
                'type_distribution': type_counts,
                'severity_distribution': severity_counts,
                'trend': trend,
                'recent_count': len(recent_anomalies),
                'avg_confidence': np.mean([a.confidence for a in anomalies]),
                'most_common_type': max(type_counts.keys(), key=type_counts.get) if type_counts else None
            }
        
        except Exception as e:
            logger.error(f"Failed to analyze anomaly trends: {e}")
            return {'error': str(e)}
