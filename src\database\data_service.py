"""
数据服务层
协调数据获取、处理和存储
"""
import asyncio
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timezone, timedelta

from src.data.data_coordinator import DataCoordinator
from .repositories import (
    AddressRepository, TransactionRepository, TokenRepository,
    MarketDataRepository, SmartMoneyActivityRepository, WhaleAlertRepository
)
from src.utils.logger import get_logger
from config.settings import MONITORING_CONFIG

logger = get_logger(__name__)


class DataService:
    """数据服务类"""
    
    def __init__(self):
        self.address_repo = AddressRepository()
        self.transaction_repo = TransactionRepository()
        self.token_repo = TokenRepository()
        self.market_data_repo = MarketDataRepository()
        self.smart_money_repo = SmartMoneyActivityRepository()
        self.whale_alert_repo = WhaleAlertRepository()
        self.data_coordinator = None
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.data_coordinator = await DataCoordinator().__aenter__()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.data_coordinator:
            await self.data_coordinator.__aexit__(exc_type, exc_val, exc_tb)
    
    async def sync_address_data(self, address: str, force_update: bool = False) -> Dict[str, Any]:
        """同步地址数据"""
        try:
            logger.info(f"Syncing data for address: {address}")
            
            # 检查地址是否已存在
            existing_address = self.address_repo.get_by_address(address)
            
            # 如果地址不存在或需要强制更新
            if not existing_address or force_update:
                # 获取余额信息
                balance_data = await self.data_coordinator.get_address_balance(address)
                
                # 获取交易历史
                transactions = await self.data_coordinator.get_address_transactions(address, limit=100)
                
                # 获取代币转账记录
                token_transfers = await self.data_coordinator.get_token_transfers(address, limit=100)
                
                # 计算地址统计信息
                stats = self._calculate_address_stats(balance_data, transactions, token_transfers)
                
                # 更新或创建地址记录
                address_record = self.address_repo.update_address_stats(address, stats)
                
                # 存储交易数据
                await self._store_transactions(transactions)
                
                # 存储代币转账数据
                await self._store_token_transfers(token_transfers)
                
                logger.info(f"Successfully synced data for address: {address}")
                
                return {
                    'address': address,
                    'balance_eth': balance_data['balance_eth'],
                    'transaction_count': len(transactions),
                    'token_transfer_count': len(token_transfers),
                    'stats': stats,
                    'updated_at': datetime.now(timezone.utc)
                }
            
            else:
                logger.info(f"Address {address} data is up to date")
                return {
                    'address': address,
                    'status': 'up_to_date',
                    'last_updated': existing_address.updated_at
                }
        
        except Exception as e:
            logger.error(f"Failed to sync address data for {address}: {e}")
            raise
    
    def _calculate_address_stats(self, balance_data: Dict[str, Any], 
                               transactions: List[Dict[str, Any]], 
                               token_transfers: List[Dict[str, Any]]) -> Dict[str, Any]:
        """计算地址统计信息"""
        try:
            # 基本统计
            total_transactions = len(transactions)
            total_token_transfers = len(token_transfers)
            
            # 计算总交易量
            total_volume_eth = sum(tx['value_eth'] for tx in transactions)
            
            # 计算平均交易价值
            avg_tx_value = total_volume_eth / total_transactions if total_transactions > 0 else 0
            
            # 分析交易模式
            incoming_txs = [tx for tx in transactions if tx['to_address'] and tx['to_address'].lower() == balance_data['address'].lower()]
            outgoing_txs = [tx for tx in transactions if tx['from_address'].lower() == balance_data['address'].lower()]
            
            # 计算活跃度指标
            if transactions:
                first_tx_time = min(tx['timestamp'] for tx in transactions)
                last_tx_time = max(tx['timestamp'] for tx in transactions)
                active_days = (last_tx_time - first_tx_time).days + 1
                avg_txs_per_day = total_transactions / active_days if active_days > 0 else 0
            else:
                first_tx_time = None
                last_tx_time = None
                active_days = 0
                avg_txs_per_day = 0
            
            # 判断是否为巨鲸（基于余额和交易量）
            is_whale = (
                balance_data['balance_eth'] >= MONITORING_CONFIG['whale_threshold']['eth'] or
                total_volume_eth >= MONITORING_CONFIG['whale_threshold']['eth'] * 10
            )
            
            # 简单的Smart Money判断（需要更复杂的算法）
            is_smart_money = (
                total_transactions >= 50 and
                avg_tx_value > 1.0 and  # 平均交易价值大于1 ETH
                len(set(tx['to_address'] for tx in outgoing_txs if tx['to_address'])) > 10  # 与多个地址交互
            )
            
            return {
                'total_transactions': total_transactions,
                'total_volume_usd': total_volume_eth * 2000,  # 假设ETH价格为2000 USD
                'first_seen': first_tx_time,
                'last_active': last_tx_time,
                'is_whale': is_whale,
                'is_smart_money': is_smart_money,
                'avg_tx_value_eth': avg_tx_value,
                'active_days': active_days,
                'avg_txs_per_day': avg_txs_per_day,
                'incoming_tx_count': len(incoming_txs),
                'outgoing_tx_count': len(outgoing_txs),
                'token_transfer_count': total_token_transfers
            }
        
        except Exception as e:
            logger.error(f"Failed to calculate address stats: {e}")
            return {}
    
    async def _store_transactions(self, transactions: List[Dict[str, Any]]) -> int:
        """存储交易数据"""
        try:
            stored_count = 0
            for tx_data in transactions:
                # 检查交易是否已存在
                existing_tx = self.transaction_repo.get_by_hash(tx_data['hash'])
                
                if not existing_tx:
                    # 转换数据格式以匹配数据库模型
                    tx_record_data = {
                        'tx_hash': tx_data['hash'],
                        'block_number': tx_data['block_number'],
                        'block_timestamp': tx_data['timestamp'],
                        'from_address': tx_data['from_address'],
                        'to_address': tx_data['to_address'],
                        'value_wei': str(tx_data['value_wei']),
                        'value_eth': tx_data['value_eth'],
                        'value_usd': tx_data['value_eth'] * 2000,  # 假设价格
                        'gas_used': tx_data['gas_used'],
                        'gas_price': tx_data['gas_price'],
                        'gas_fee_eth': tx_data['gas_fee_eth'],
                        'gas_fee_usd': tx_data['gas_fee_eth'] * 2000,
                        'transaction_type': 'transfer',
                        'contract_address': tx_data.get('contract_address'),
                        'is_whale_tx': tx_data['value_eth'] >= MONITORING_CONFIG['whale_threshold']['eth']
                    }
                    
                    self.transaction_repo.create(**tx_record_data)
                    stored_count += 1
            
            logger.info(f"Stored {stored_count} new transactions")
            return stored_count
        
        except Exception as e:
            logger.error(f"Failed to store transactions: {e}")
            raise
    
    async def _store_token_transfers(self, token_transfers: List[Dict[str, Any]]) -> int:
        """存储代币转账数据"""
        try:
            stored_count = 0
            for transfer_data in transfer_transfers:
                # 检查转账是否已存在
                existing_tx = self.transaction_repo.get_by_hash(transfer_data['hash'])
                
                if not existing_tx:
                    # 首先确保代币记录存在
                    token_record = self.token_repo.get_by_contract_address(transfer_data['contract_address'])
                    if not token_record:
                        # 创建代币记录
                        token_data = {
                            'contract_address': transfer_data['contract_address'],
                            'symbol': transfer_data['token_symbol'],
                            'name': transfer_data['token_name'],
                            'decimals': transfer_data['token_decimal']
                        }
                        self.token_repo.create(**token_data)
                    
                    # 创建交易记录
                    tx_record_data = {
                        'tx_hash': transfer_data['hash'],
                        'block_number': transfer_data['block_number'],
                        'block_timestamp': transfer_data['timestamp'],
                        'from_address': transfer_data['from_address'],
                        'to_address': transfer_data['to_address'],
                        'value_wei': '0',  # 代币转账ETH价值为0
                        'value_eth': 0.0,
                        'gas_used': transfer_data['gas_used'],
                        'gas_price': transfer_data['gas_price'],
                        'transaction_type': 'token_transfer',
                        'contract_address': transfer_data['contract_address'],
                        'token_symbol': transfer_data['token_symbol'],
                        'token_amount': transfer_data['value_formatted']
                    }
                    
                    self.transaction_repo.create(**tx_record_data)
                    stored_count += 1
            
            logger.info(f"Stored {stored_count} new token transfers")
            return stored_count
        
        except Exception as e:
            logger.error(f"Failed to store token transfers: {e}")
            raise
    
    async def sync_market_data(self, symbols: List[str]) -> Dict[str, Any]:
        """同步市场数据"""
        try:
            logger.info(f"Syncing market data for symbols: {symbols}")
            
            # 获取价格数据
            price_data = await self.data_coordinator.get_token_price(symbols)
            
            # 存储市场数据
            market_data_records = []
            for symbol, data in price_data.items():
                if 'usd' in data:
                    market_record = {
                        'symbol': symbol.upper(),
                        'timestamp': datetime.now(timezone.utc),
                        'price_usd': data['usd'],
                        'volume_24h': data.get('usd_24h_vol', 0),
                        'market_cap': data.get('usd_market_cap', 0),
                        'price_change_24h': data.get('usd_24h_change', 0)
                    }
                    market_data_records.append(market_record)
            
            # 批量插入市场数据
            inserted_count = self.market_data_repo.bulk_insert_market_data(market_data_records)
            
            logger.info(f"Successfully synced market data, inserted {inserted_count} records")
            
            return {
                'symbols': symbols,
                'inserted_records': inserted_count,
                'timestamp': datetime.now(timezone.utc)
            }
        
        except Exception as e:
            logger.error(f"Failed to sync market data: {e}")
            raise
    
    async def detect_whale_activity(self, hours: int = 1) -> List[Dict[str, Any]]:
        """检测巨鲸活动"""
        try:
            logger.info(f"Detecting whale activity in the last {hours} hours")
            
            # 获取大额交易
            large_transactions = self.transaction_repo.get_large_transactions(
                min_value_usd=MONITORING_CONFIG['whale_threshold']['usdt'],
                hours=hours
            )
            
            whale_activities = []
            for tx in large_transactions:
                # 创建巨鲸预警
                alert_data = {
                    'tx_hash': tx.tx_hash,
                    'alert_type': 'large_transfer',
                    'from_address': tx.from_address,
                    'to_address': tx.to_address,
                    'amount_usd': tx.value_usd,
                    'token_symbol': tx.token_symbol or 'ETH',
                    'alert_level': self._determine_alert_level(tx.value_usd)
                }
                
                # 检查是否已存在相同的预警
                existing_alert = self.whale_alert_repo.get_by_id(tx.id)  # 需要实现这个方法
                if not existing_alert:
                    whale_alert = self.whale_alert_repo.create(**alert_data)
                    whale_activities.append({
                        'alert_id': whale_alert.id,
                        'transaction_hash': tx.tx_hash,
                        'amount_usd': tx.value_usd,
                        'alert_level': alert_data['alert_level'],
                        'timestamp': tx.block_timestamp
                    })
            
            logger.info(f"Detected {len(whale_activities)} whale activities")
            return whale_activities
        
        except Exception as e:
            logger.error(f"Failed to detect whale activity: {e}")
            raise
    
    def _determine_alert_level(self, amount_usd: float) -> str:
        """确定预警级别"""
        if amount_usd >= 10000000:  # 1000万美元
            return 'critical'
        elif amount_usd >= 5000000:  # 500万美元
            return 'high'
        elif amount_usd >= 1000000:  # 100万美元
            return 'medium'
        else:
            return 'low'
