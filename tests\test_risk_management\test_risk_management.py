"""
风险管理模块测试
"""
import pytest
import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timezone, timedelta
from unittest.mock import Mock, patch, MagicMock

from src.risk_management.risk_engine import RiskEngine, RiskMetrics, PortfolioRisk, RiskLevel
from src.risk_management.position_manager import (
    PositionManager, PositionSizeConfig, PositionSizeMethod, PositionRecommendation
)
from src.risk_management.stop_loss_manager import (
    StopLossManager, StopLossConfig, TakeProfitConfig, 
    StopLossType, TakeProfitType, StopOrder
)


class TestRiskEngine:
    """风险评估引擎测试"""
    
    def test_risk_engine_initialization(self):
        """测试风险引擎初始化"""
        engine = RiskEngine()
        
        assert engine.market_data_repo is not None
        assert engine.transaction_repo is not None
        assert 'volatility_threshold' in engine.risk_params
        assert 'var_threshold' in engine.risk_params
    
    @pytest.mark.asyncio
    async def test_assess_asset_risk(self):
        """测试资产风险评估"""
        engine = RiskEngine()
        
        # 模拟价格历史数据
        with patch.object(engine.market_data_repo, 'get_price_history') as mock_get_history:
            mock_price_data = [
                Mock(timestamp=datetime.now(timezone.utc) - timedelta(days=i),
                     price_usd=50000 + np.random.normal(0, 1000),
                     volume_24h=1000000,
                     market_cap=1000000000)
                for i in range(30)
            ]
            mock_get_history.return_value = mock_price_data
            
            risk_metrics = await engine.assess_asset_risk('BTC', days=30)
            
            assert isinstance(risk_metrics, RiskMetrics)
            assert risk_metrics.symbol == 'BTC'
            assert risk_metrics.volatility >= 0
            assert risk_metrics.var_95 <= 0  # VaR应该是负数
            assert risk_metrics.liquidity_score >= 0
            assert risk_metrics.liquidity_score <= 1
            assert isinstance(risk_metrics.risk_level, RiskLevel)
    
    @pytest.mark.asyncio
    async def test_assess_portfolio_risk(self):
        """测试投资组合风险评估"""
        engine = RiskEngine()
        
        # 模拟投资组合
        positions = {
            'BTC': {'value': 50000, 'quantity': 1.0},
            'ETH': {'value': 30000, 'quantity': 10.0},
            'ADA': {'value': 20000, 'quantity': 20000.0}
        }
        
        with patch.object(engine.market_data_repo, 'get_price_history') as mock_get_history:
            # 为每个资产模拟价格历史
            def mock_history_side_effect(symbol, days):
                return [
                    Mock(timestamp=datetime.now(timezone.utc) - timedelta(days=i),
                         price_usd=50000 + np.random.normal(0, 1000),
                         volume_24h=1000000)
                    for i in range(days)
                ]
            
            mock_get_history.side_effect = mock_history_side_effect
            
            portfolio_risk = await engine.assess_portfolio_risk(positions, days=30)
            
            assert isinstance(portfolio_risk, PortfolioRisk)
            assert portfolio_risk.total_value == 100000  # 50000 + 30000 + 20000
            assert portfolio_risk.portfolio_volatility >= 0
            assert portfolio_risk.concentration_risk >= 0
            assert portfolio_risk.concentration_risk <= 1
            assert isinstance(portfolio_risk.overall_risk_level, RiskLevel)
            assert isinstance(portfolio_risk.risk_recommendations, list)
    
    def test_risk_level_determination(self):
        """测试风险等级确定"""
        engine = RiskEngine()
        
        # 测试不同风险分数对应的风险等级
        assert engine._determine_risk_level(0.1) == RiskLevel.VERY_LOW
        assert engine._determine_risk_level(0.3) == RiskLevel.LOW
        assert engine._determine_risk_level(0.5) == RiskLevel.MEDIUM
        assert engine._determine_risk_level(0.7) == RiskLevel.HIGH
        assert engine._determine_risk_level(0.9) == RiskLevel.VERY_HIGH
    
    def test_concentration_risk_calculation(self):
        """测试集中度风险计算"""
        engine = RiskEngine()
        
        # 均匀分布的权重
        equal_weights = [0.2, 0.2, 0.2, 0.2, 0.2]
        concentration_equal = engine._calculate_concentration_risk(equal_weights)
        
        # 集中的权重
        concentrated_weights = [0.8, 0.05, 0.05, 0.05, 0.05]
        concentration_high = engine._calculate_concentration_risk(concentrated_weights)
        
        assert concentration_high > concentration_equal
        assert concentration_equal == 0.2  # 5个相等权重的赫芬达尔指数


class TestPositionManager:
    """仓位管理器测试"""
    
    def test_position_manager_initialization(self):
        """测试仓位管理器初始化"""
        config = PositionSizeConfig(
            method=PositionSizeMethod.FIXED_PERCENTAGE,
            base_size=0.1,
            max_position_size=0.2,
            max_total_exposure=0.8,
            risk_per_trade=0.02
        )
        
        manager = PositionManager(config)
        
        assert manager.config == config
        assert manager.total_capital == 0
        assert manager.available_capital == 0
        assert len(manager.positions) == 0
    
    def test_capital_management(self):
        """测试资金管理"""
        config = PositionSizeConfig(
            method=PositionSizeMethod.FIXED_PERCENTAGE,
            base_size=0.1,
            max_position_size=0.2,
            max_total_exposure=0.8,
            risk_per_trade=0.02
        )
        
        manager = PositionManager(config)
        manager.set_capital(100000, 80000)
        
        assert manager.total_capital == 100000
        assert manager.available_capital == 80000
        
        # 更新持仓
        positions = {
            'BTC': {'value': 20000, 'quantity': 0.4}
        }
        manager.update_positions(positions)
        
        assert manager.available_capital == 80000  # 100000 - 20000
    
    @pytest.mark.asyncio
    async def test_fixed_percentage_position_size(self):
        """测试固定百分比仓位计算"""
        config = PositionSizeConfig(
            method=PositionSizeMethod.FIXED_PERCENTAGE,
            base_size=0.1,  # 10%
            max_position_size=0.2,
            max_total_exposure=0.8,
            risk_per_trade=0.02
        )
        
        manager = PositionManager(config)
        manager.set_capital(100000)
        
        position_size = await manager.calculate_position_size('BTC')
        
        assert position_size == 10000  # 100000 * 0.1
    
    @pytest.mark.asyncio
    async def test_volatility_adjusted_position_size(self):
        """测试波动率调整仓位计算"""
        config = PositionSizeConfig(
            method=PositionSizeMethod.VOLATILITY_ADJUSTED,
            base_size=0.1,
            max_position_size=0.2,
            max_total_exposure=0.8,
            risk_per_trade=0.02
        )
        
        manager = PositionManager(config)
        manager.set_capital(100000)
        
        # 模拟风险引擎
        with patch.object(manager.risk_engine, 'assess_asset_risk') as mock_assess:
            mock_risk = Mock()
            mock_risk.volatility = 0.3  # 30%波动率
            mock_assess.return_value = mock_risk
            
            position_size = await manager.calculate_position_size('BTC', signal_strength=1.0)
            
            # 应该根据波动率调整仓位大小
            assert position_size > 0
            assert position_size <= 20000  # 不超过最大仓位限制
    
    @pytest.mark.asyncio
    async def test_position_recommendations(self):
        """测试仓位建议生成"""
        config = PositionSizeConfig(
            method=PositionSizeMethod.FIXED_PERCENTAGE,
            base_size=0.1,
            max_position_size=0.2,
            max_total_exposure=0.8,
            risk_per_trade=0.02,
            rebalance_threshold=0.05
        )
        
        manager = PositionManager(config)
        manager.set_capital(100000)
        
        # 设置当前持仓
        positions = {
            'BTC': {'value': 5000, 'quantity': 0.1}
        }
        manager.update_positions(positions)
        
        # 模拟交易信号
        signals = [
            {
                'symbol': 'BTC',
                'action': 'buy',
                'confidence': 0.8,
                'reason': 'Strong bullish signal'
            }
        ]
        
        with patch.object(manager.risk_engine, 'assess_asset_risk') as mock_assess:
            mock_risk = Mock()
            mock_risk.risk_level = RiskLevel.MEDIUM
            mock_risk.volatility = 0.2
            mock_assess.return_value = mock_risk
            
            recommendations = await manager.generate_position_recommendations(signals)
            
            assert isinstance(recommendations, list)
            if recommendations:
                rec = recommendations[0]
                assert isinstance(rec, PositionRecommendation)
                assert rec.symbol == 'BTC'
                assert rec.action in ['buy', 'increase', 'hold']
    
    def test_position_summary(self):
        """测试仓位摘要"""
        config = PositionSizeConfig(
            method=PositionSizeMethod.FIXED_PERCENTAGE,
            base_size=0.1,
            max_position_size=0.2,
            max_total_exposure=0.8,
            risk_per_trade=0.02
        )
        
        manager = PositionManager(config)
        manager.set_capital(100000)
        
        positions = {
            'BTC': {'value': 30000, 'quantity': 0.6},
            'ETH': {'value': 20000, 'quantity': 6.67}
        }
        manager.update_positions(positions)
        
        summary = manager.get_position_summary()
        
        assert summary['total_capital'] == 100000
        assert summary['used_capital'] == 50000
        assert summary['capital_utilization'] == 0.5
        assert summary['number_of_positions'] == 2
        assert summary['largest_position'] == 30000
        assert summary['largest_position_pct'] == 0.3


class TestStopLossManager:
    """止损止盈管理器测试"""
    
    def test_stop_loss_manager_initialization(self):
        """测试止损止盈管理器初始化"""
        stop_config = StopLossConfig(
            stop_type=StopLossType.FIXED_PERCENTAGE,
            stop_percentage=0.05
        )
        
        profit_config = TakeProfitConfig(
            profit_type=TakeProfitType.FIXED_PERCENTAGE,
            profit_percentage=0.10
        )
        
        manager = StopLossManager(stop_config, profit_config)
        
        assert manager.stop_config == stop_config
        assert manager.profit_config == profit_config
        assert len(manager.active_orders) == 0
    
    @pytest.mark.asyncio
    async def test_create_stop_orders(self):
        """测试创建止损止盈订单"""
        stop_config = StopLossConfig(
            stop_type=StopLossType.FIXED_PERCENTAGE,
            stop_percentage=0.05
        )
        
        profit_config = TakeProfitConfig(
            profit_type=TakeProfitType.FIXED_PERCENTAGE,
            profit_percentage=0.10
        )
        
        manager = StopLossManager(stop_config, profit_config)
        
        orders = await manager.create_stop_orders(
            symbol='BTC',
            position_id='pos_001',
            entry_price=50000,
            quantity=1.0,
            position_side='long'
        )
        
        assert len(orders) >= 1  # 至少有止损订单
        
        # 检查止损订单
        stop_loss_orders = [o for o in orders if o.order_type == 'stop_loss']
        assert len(stop_loss_orders) == 1
        
        stop_order = stop_loss_orders[0]
        assert stop_order.symbol == 'BTC'
        assert stop_order.trigger_price == 47500  # 50000 * (1 - 0.05)
        assert stop_order.quantity == 1.0
        assert stop_order.is_active == True
        
        # 检查止盈订单
        take_profit_orders = [o for o in orders if o.order_type == 'take_profit']
        if take_profit_orders:
            profit_order = take_profit_orders[0]
            assert profit_order.symbol == 'BTC'
            assert profit_order.trigger_price == 55000  # 50000 * (1 + 0.10)
    
    @pytest.mark.asyncio
    async def test_trailing_stop_loss(self):
        """测试移动止损"""
        stop_config = StopLossConfig(
            stop_type=StopLossType.TRAILING_STOP,
            trailing_percentage=0.03
        )
        
        profit_config = TakeProfitConfig(
            profit_type=TakeProfitType.FIXED_PERCENTAGE,
            profit_percentage=0.10
        )
        
        manager = StopLossManager(stop_config, profit_config)
        
        # 创建移动止损订单
        orders = await manager.create_stop_orders(
            symbol='BTC',
            position_id='pos_001',
            entry_price=50000,
            quantity=1.0,
            position_side='long'
        )
        
        stop_order = [o for o in orders if o.order_type == 'stop_loss'][0]
        original_stop_price = stop_order.trigger_price
        
        # 模拟价格上涨
        current_prices = {'BTC': 52000}
        await manager.update_stop_orders(current_prices)
        
        # 止损价格应该上调
        assert stop_order.trigger_price > original_stop_price
        assert stop_order.highest_price == 52000
    
    @pytest.mark.asyncio
    async def test_stop_order_triggering(self):
        """测试止损止盈触发"""
        stop_config = StopLossConfig(
            stop_type=StopLossType.FIXED_PERCENTAGE,
            stop_percentage=0.05
        )
        
        profit_config = TakeProfitConfig(
            profit_type=TakeProfitType.FIXED_PERCENTAGE,
            profit_percentage=0.10
        )
        
        manager = StopLossManager(stop_config, profit_config)
        
        # 创建订单
        orders = await manager.create_stop_orders(
            symbol='BTC',
            position_id='pos_001',
            entry_price=50000,
            quantity=1.0,
            position_side='long'
        )
        
        # 模拟价格下跌触发止损
        current_prices = {'BTC': 47000}  # 低于止损价47500
        triggered_orders = await manager.update_stop_orders(current_prices)
        
        assert len(triggered_orders) >= 1
        
        triggered_order = triggered_orders[0]
        assert triggered_order['order_type'] == 'stop_loss'
        assert triggered_order['symbol'] == 'BTC'
        assert triggered_order['current_price'] == 47000
    
    def test_order_cancellation(self):
        """测试订单取消"""
        stop_config = StopLossConfig(
            stop_type=StopLossType.FIXED_PERCENTAGE,
            stop_percentage=0.05
        )
        
        profit_config = TakeProfitConfig(
            profit_type=TakeProfitType.FIXED_PERCENTAGE,
            profit_percentage=0.10
        )
        
        manager = StopLossManager(stop_config, profit_config)
        
        # 手动添加一个订单用于测试
        order = StopOrder(
            id='test_order',
            symbol='BTC',
            position_id='pos_001',
            order_type='stop_loss',
            trigger_price=47500,
            quantity=1.0,
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc),
            is_active=True
        )
        
        manager.active_orders['test_order'] = order
        
        # 取消订单
        cancelled_orders = manager.cancel_stop_orders('pos_001')
        
        assert len(cancelled_orders) == 1
        assert cancelled_orders[0] == 'test_order'
        assert not order.is_active
    
    def test_statistics(self):
        """测试统计信息"""
        stop_config = StopLossConfig(
            stop_type=StopLossType.FIXED_PERCENTAGE,
            stop_percentage=0.05
        )
        
        profit_config = TakeProfitConfig(
            profit_type=TakeProfitType.FIXED_PERCENTAGE,
            profit_percentage=0.10
        )
        
        manager = StopLossManager(stop_config, profit_config)
        
        # 模拟一些统计数据
        manager.stats['total_stop_losses'] = 5
        manager.stats['total_take_profits'] = 3
        manager.stats['avg_stop_loss_pct'] = -0.05
        manager.stats['avg_take_profit_pct'] = 0.08
        
        stats = manager.get_statistics()
        
        assert stats['total_stop_losses'] == 5
        assert stats['total_take_profits'] == 3
        assert stats['total_orders'] == 8
        assert stats['stop_profit_ratio'] == 5/3
        assert 'config' in stats


class TestIntegration:
    """集成测试"""
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_risk_management_workflow(self):
        """测试风险管理工作流程"""
        # 这个测试需要真实的数据库连接，通常在开发环境中跳过
        pytest.skip("Integration test requires real database connection")
        
        # 1. 风险评估
        risk_engine = RiskEngine()
        risk_metrics = await risk_engine.assess_asset_risk('BTC')
        
        # 2. 仓位管理
        position_config = PositionSizeConfig(
            method=PositionSizeMethod.VOLATILITY_ADJUSTED,
            base_size=0.1,
            max_position_size=0.2,
            max_total_exposure=0.8,
            risk_per_trade=0.02
        )
        
        position_manager = PositionManager(position_config)
        position_manager.set_capital(100000)
        
        position_size = await position_manager.calculate_position_size('BTC')
        
        # 3. 止损止盈
        stop_config = StopLossConfig(
            stop_type=StopLossType.VOLATILITY_BASED,
            volatility_multiplier=2.0
        )
        
        profit_config = TakeProfitConfig(
            profit_type=TakeProfitType.RISK_REWARD_RATIO,
            risk_reward_ratio=2.0
        )
        
        stop_manager = StopLossManager(stop_config, profit_config)
        
        stop_orders = await stop_manager.create_stop_orders(
            symbol='BTC',
            position_id='test_pos',
            entry_price=50000,
            quantity=position_size / 50000
        )
        
        assert isinstance(risk_metrics, RiskMetrics)
        assert position_size > 0
        assert len(stop_orders) > 0


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v", "-m", "not integration"])
