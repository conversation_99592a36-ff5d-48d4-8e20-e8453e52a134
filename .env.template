# 环境变量配置模板
# 复制此文件为 .env 并填入真实的配置值

# API Keys
ETHERSCAN_API_KEY=your_etherscan_api_key_here
MORALIS_API_KEY=your_moralis_api_key_here
COINGECKO_API_KEY=your_coingecko_api_key_here
DUNE_API_KEY=your_dune_api_key_here
INFURA_PROJECT_ID=your_infura_project_id_here
ALCHEMY_API_KEY=your_alchemy_api_key_here

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=onchain_analysis
DB_USER=postgres
DB_PASSWORD=your_db_password_here
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# RPC URLs
ETH_RPC_URL=https://mainnet.infura.io/v3/YOUR_PROJECT_ID
BSC_RPC_URL=https://bsc-dataseed.binance.org/
POLYGON_RPC_URL=https://polygon-rpc.com/

# Alert Configuration
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
TELEGRAM_CHAT_ID=your_telegram_chat_id
DISCORD_WEBHOOK_URL=your_discord_webhook_url
ALERT_EMAIL=<EMAIL>

# Web Application
WEB_HOST=0.0.0.0
WEB_PORT=5000
DEBUG=True
SECRET_KEY=your-very-secret-key-change-in-production

# Logging
LOG_LEVEL=INFO
DEBUG_SQL=False

# Development
MOCK_APIS=False
WORKERS=4
TIMEOUT=30
KEEPALIVE=2
