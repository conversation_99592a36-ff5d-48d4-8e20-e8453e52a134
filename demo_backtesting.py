"""
回测框架演示脚本
演示策略回测、性能分析和结果可视化功能
"""
import asyncio
import sys
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime, timezone, timedelta

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.backtesting.backtest_engine import BacktestEngine, BacktestConfig
from src.backtesting.strategies import (
    MovingAverageCrossoverStrategy, RSIMeanReversionStrategy, 
    BollingerBandsStrategy, StrategyFactory, CompositeStrategy
)
from src.backtesting.performance_analyzer import PerformanceAnalyzer
from src.utils.logger import get_logger

logger = get_logger(__name__)


async def demo_strategy_development():
    """演示策略开发"""
    print("\n=== 策略开发演示 ===")
    
    try:
        print("--- 可用策略 ---")
        available_strategies = StrategyFactory.get_available_strategies()
        
        for i, strategy_name in enumerate(available_strategies, 1):
            print(f"{i}. {strategy_name}")
        
        print(f"\n--- 创建策略实例 ---")
        
        # 移动平均线策略
        ma_strategy = StrategyFactory.create_strategy(
            'ma_crossover',
            fast_period=10,
            slow_period=20
        )
        
        print(f"✅ 移动平均线策略: {ma_strategy.name}")
        print(f"   参数: {ma_strategy.params}")
        
        # RSI策略
        rsi_strategy = StrategyFactory.create_strategy(
            'rsi_mean_reversion',
            rsi_period=14,
            oversold_threshold=30,
            overbought_threshold=70
        )
        
        print(f"✅ RSI策略: {rsi_strategy.name}")
        print(f"   参数: {rsi_strategy.params}")
        
        # 布林带策略
        bb_strategy = StrategyFactory.create_strategy(
            'bollinger_bands',
            period=20,
            std_dev=2.0
        )
        
        print(f"✅ 布林带策略: {bb_strategy.name}")
        print(f"   参数: {bb_strategy.params}")
        
        # 综合策略
        print(f"\n--- 创建综合策略 ---")
        
        composite_strategy = CompositeStrategy(
            strategies=[ma_strategy, rsi_strategy, bb_strategy],
            weights=[0.4, 0.3, 0.3]
        )
        
        print(f"✅ 综合策略: {composite_strategy.name}")
        print(f"   子策略: {composite_strategy.params['strategies']}")
        print(f"   权重: {composite_strategy.params['weights']}")
        
        # 测试策略信号生成
        print(f"\n--- 测试策略信号生成 ---")
        
        # 创建模拟数据
        dates = pd.date_range('2023-01-01', periods=50, freq='D')
        np.random.seed(42)
        prices = 50000 + np.cumsum(np.random.randn(50) * 100)
        
        test_data = pd.DataFrame({
            'timestamp': dates,
            'close': prices,
            'open': prices,
            'high': prices * 1.02,
            'low': prices * 0.98,
            'volume': [1000000] * 50
        })
        
        strategy_data = {
            'current_prices': {'BTC': prices[-1]},
            'price_data': {'BTC': test_data},
            'current_time': dates[-1]
        }
        
        # 测试各个策略
        strategies_to_test = [
            ('移动平均线', ma_strategy),
            ('RSI', rsi_strategy),
            ('布林带', bb_strategy),
            ('综合策略', composite_strategy)
        ]
        
        for strategy_name, strategy in strategies_to_test:
            try:
                signals = await strategy.generate_signals(strategy_data)
                print(f"   {strategy_name}: 生成 {len(signals)} 个信号")
                
                for signal in signals[:2]:  # 显示前2个信号
                    print(f"     - {signal['action']} {signal['symbol']}: {signal.get('reason', 'N/A')}")
            
            except Exception as e:
                print(f"   {strategy_name}: 信号生成失败 - {e}")
    
    except Exception as e:
        print(f"策略开发演示失败: {e}")


async def demo_backtest_execution():
    """演示回测执行"""
    print("\n=== 回测执行演示 ===")
    
    try:
        print("--- 回测配置 ---")
        
        # 创建回测配置
        config = BacktestConfig(
            start_date=datetime(2023, 1, 1, tzinfo=timezone.utc),
            end_date=datetime(2023, 6, 30, tzinfo=timezone.utc),
            initial_capital=100000.0,
            commission_rate=0.001,
            slippage_rate=0.0005,
            position_size_pct=0.2
        )
        
        print(f"   回测期间: {config.start_date.date()} 到 {config.end_date.date()}")
        print(f"   初始资金: ${config.initial_capital:,.0f}")
        print(f"   手续费率: {config.commission_rate:.1%}")
        print(f"   滑点率: {config.slippage_rate:.2%}")
        print(f"   单仓位大小: {config.position_size_pct:.1%}")
        
        print(f"\n--- 创建回测引擎 ---")
        
        # 创建回测引擎
        engine = BacktestEngine(config)
        
        # 创建策略
        strategy = MovingAverageCrossoverStrategy(fast_period=10, slow_period=30)
        
        # 策略包装函数
        async def strategy_wrapper(data):
            return await strategy.generate_signals(data)
        
        engine.set_strategy(strategy_wrapper)
        
        print(f"   ✅ 回测引擎创建成功")
        print(f"   ✅ 策略设置完成: {strategy.name}")
        
        print(f"\n--- 模拟回测执行 ---")
        
        # 由于需要真实数据，这里模拟回测结果
        print("   📊 正在加载历史数据...")
        print("   🔄 正在执行回测...")
        
        # 模拟回测结果
        dates = pd.date_range(config.start_date, config.end_date, freq='D')
        np.random.seed(42)
        
        # 生成模拟权益曲线
        daily_returns = np.random.normal(0.0008, 0.02, len(dates))  # 日收益率
        equity_values = [config.initial_capital]
        
        for ret in daily_returns:
            equity_values.append(equity_values[-1] * (1 + ret))
        
        equity_curve = list(zip(dates, equity_values[1:]))
        
        # 生成模拟交易记录
        trades = []
        for i in range(20):  # 20笔交易
            trade_date = dates[i * 8]  # 每8天一笔交易
            pnl = np.random.normal(200, 500)  # 随机盈亏
            
            trades.append({
                'symbol': 'BTC' if i % 2 == 0 else 'ETH',
                'side': 'buy' if i % 4 < 2 else 'sell',
                'quantity': 1.0,
                'price': 50000 + np.random.normal(0, 2000),
                'timestamp': trade_date,
                'pnl': pnl
            })
        
        # 模拟回测结果
        mock_results = {
            'total_return': 0.15,
            'annualized_return': 0.32,
            'volatility': 0.18,
            'sharpe_ratio': 1.78,
            'max_drawdown': -0.08,
            'total_trades': len(trades),
            'winning_trades': 13,
            'win_rate': 0.65,
            'profit_factor': 1.85,
            'final_portfolio_value': equity_values[-1],
            'equity_curve': equity_curve,
            'trades': trades
        }
        
        print(f"   ✅ 回测执行完成")
        print(f"   📈 总收益率: {mock_results['total_return']:.2%}")
        print(f"   📊 年化收益率: {mock_results['annualized_return']:.2%}")
        print(f"   📉 最大回撤: {mock_results['max_drawdown']:.2%}")
        print(f"   🎯 夏普比率: {mock_results['sharpe_ratio']:.2f}")
        print(f"   💼 总交易数: {mock_results['total_trades']}")
        print(f"   🎲 胜率: {mock_results['win_rate']:.2%}")
        
        return mock_results
    
    except Exception as e:
        print(f"回测执行演示失败: {e}")
        return {}


async def demo_performance_analysis():
    """演示性能分析"""
    print("\n=== 性能分析演示 ===")
    
    try:
        # 使用前面的模拟回测结果
        print("--- 获取回测结果 ---")
        results = await demo_backtest_execution()
        
        if not results:
            print("   ❌ 无回测结果可分析")
            return
        
        print(f"\n--- 创建性能分析器 ---")
        
        analyzer = PerformanceAnalyzer()
        
        print(f"   ✅ 性能分析器创建成功")
        
        print(f"\n--- 执行详细分析 ---")
        
        # 执行分析
        analysis = analyzer.analyze_backtest_results(results)
        
        if analysis:
            print(f"   ✅ 分析完成")
            
            # 显示基本指标
            basic_metrics = analysis.get('basic_metrics', {})
            if basic_metrics:
                print(f"\n   📊 基本指标:")
                print(f"     总收益率: {basic_metrics.get('total_return', 0):.2%}")
                print(f"     年化收益率: {basic_metrics.get('annualized_return', 0):.2%}")
                print(f"     年化波动率: {basic_metrics.get('volatility', 0):.2%}")
                print(f"     夏普比率: {basic_metrics.get('sharpe_ratio', 0):.3f}")
                print(f"     索提诺比率: {basic_metrics.get('sortino_ratio', 0):.3f}")
                print(f"     卡尔马比率: {basic_metrics.get('calmar_ratio', 0):.3f}")
                print(f"     最大回撤: {basic_metrics.get('max_drawdown', 0):.2%}")
            
            # 显示风险指标
            risk_metrics = analysis.get('risk_metrics', {})
            if risk_metrics:
                print(f"\n   ⚠️ 风险指标:")
                print(f"     VaR (95%): {risk_metrics.get('var_95', 0):.2%}")
                print(f"     CVaR (95%): {risk_metrics.get('cvar_95', 0):.2%}")
                print(f"     最大连续亏损: {risk_metrics.get('max_consecutive_losses', 0)} 天")
                print(f"     最大连续盈利: {risk_metrics.get('max_consecutive_wins', 0)} 天")
                print(f"     偏度: {risk_metrics.get('skewness', 0):.3f}")
                print(f"     峰度: {risk_metrics.get('kurtosis', 0):.3f}")
            
            # 显示交易分析
            trade_analysis = analysis.get('trade_analysis', {})
            if trade_analysis:
                print(f"\n   📈 交易分析:")
                print(f"     总交易数: {trade_analysis.get('total_trades', 0)}")
                print(f"     盈利交易: {trade_analysis.get('winning_trades', 0)}")
                print(f"     亏损交易: {trade_analysis.get('losing_trades', 0)}")
                print(f"     胜率: {trade_analysis.get('win_rate', 0):.2%}")
                print(f"     平均盈利: ${trade_analysis.get('avg_win', 0):.2f}")
                print(f"     平均亏损: ${trade_analysis.get('avg_loss', 0):.2f}")
                print(f"     盈亏比: {trade_analysis.get('profit_factor', 0):.2f}")
                print(f"     最大盈利: ${trade_analysis.get('max_win', 0):.2f}")
                print(f"     最大亏损: ${trade_analysis.get('max_loss', 0):.2f}")
            
            # 显示回撤分析
            drawdown_analysis = analysis.get('drawdown_analysis', {})
            if drawdown_analysis:
                print(f"\n   📉 回撤分析:")
                print(f"     最大回撤: {drawdown_analysis.get('max_drawdown', 0):.2%}")
                print(f"     最大回撤日期: {drawdown_analysis.get('max_drawdown_date', 'N/A')}")
                print(f"     平均回撤持续: {drawdown_analysis.get('avg_drawdown_duration', 0):.1f} 天")
                print(f"     最长回撤持续: {drawdown_analysis.get('max_drawdown_duration', 0)} 天")
                print(f"     回撤频率: {drawdown_analysis.get('drawdown_frequency', 0):.2f} 次/年")
            
            # 显示月度收益
            monthly_returns = analysis.get('monthly_returns', {})
            if monthly_returns and 'monthly_stats' in monthly_returns:
                monthly_stats = monthly_returns['monthly_stats']
                print(f"\n   📅 月度分析:")
                print(f"     平均月收益: {monthly_stats.get('avg_monthly_return', 0):.2%}")
                print(f"     月度波动率: {monthly_stats.get('monthly_volatility', 0):.2%}")
                print(f"     最佳月份: {monthly_stats.get('best_month', 0):.2%}")
                print(f"     最差月份: {monthly_stats.get('worst_month', 0):.2%}")
                print(f"     盈利月份: {monthly_stats.get('positive_months', 0)}")
                print(f"     亏损月份: {monthly_stats.get('negative_months', 0)}")
                print(f"     月胜率: {monthly_stats.get('monthly_win_rate', 0):.2%}")
            
            # 显示资产表现
            performance_attribution = analysis.get('performance_attribution', {})
            if performance_attribution and 'asset_contribution' in performance_attribution:
                print(f"\n   🎯 资产贡献:")
                asset_contrib = performance_attribution['asset_contribution']
                for asset, contrib in asset_contrib.items():
                    print(f"     {asset}: {contrib['contribution']:.2%} "
                          f"(${contrib['pnl']:.0f}, {contrib['trade_count']} 笔)")
        
        print(f"\n--- 生成分析报告 ---")
        
        # 生成文本报告
        report = analyzer.generate_report(analysis)
        
        print(f"   ✅ 报告生成完成")
        print(f"\n{report}")
        
        return analysis
    
    except Exception as e:
        print(f"性能分析演示失败: {e}")
        return {}


async def demo_strategy_comparison():
    """演示策略比较"""
    print("\n=== 策略比较演示 ===")
    
    try:
        print("--- 多策略回测比较 ---")
        
        # 定义要比较的策略
        strategies_to_compare = [
            ('移动平均线 (10,20)', 'ma_crossover', {'fast_period': 10, 'slow_period': 20}),
            ('移动平均线 (5,15)', 'ma_crossover', {'fast_period': 5, 'slow_period': 15}),
            ('RSI均值回归', 'rsi_mean_reversion', {'rsi_period': 14}),
            ('布林带策略', 'bollinger_bands', {'period': 20, 'std_dev': 2.0}),
            ('买入持有', 'buy_and_hold', {})
        ]
        
        comparison_results = {}
        
        for strategy_name, strategy_type, params in strategies_to_compare:
            print(f"\n   🔄 回测策略: {strategy_name}")
            
            # 模拟每个策略的回测结果
            np.random.seed(hash(strategy_name) % 2**32)  # 为每个策略设置不同的随机种子
            
            # 生成不同的收益特征
            if 'buy_and_hold' in strategy_type:
                # 买入持有：稳定上涨但波动较大
                daily_returns = np.random.normal(0.0005, 0.025, 180)
            elif 'ma_crossover' in strategy_type:
                # 移动平均：中等收益，中等波动
                daily_returns = np.random.normal(0.0008, 0.018, 180)
            elif 'rsi' in strategy_type:
                # RSI：较高收益，较高波动
                daily_returns = np.random.normal(0.0012, 0.022, 180)
            elif 'bollinger' in strategy_type:
                # 布林带：稳定收益，低波动
                daily_returns = np.random.normal(0.0006, 0.015, 180)
            else:
                daily_returns = np.random.normal(0.0007, 0.020, 180)
            
            # 计算权益曲线
            equity_values = [100000]
            for ret in daily_returns:
                equity_values.append(equity_values[-1] * (1 + ret))
            
            # 计算统计指标
            total_return = (equity_values[-1] - equity_values[0]) / equity_values[0]
            volatility = np.std(daily_returns) * np.sqrt(252)
            sharpe_ratio = (total_return * 2) / volatility if volatility > 0 else 0  # 简化计算
            
            # 计算最大回撤
            equity_series = pd.Series(equity_values)
            peak = equity_series.expanding().max()
            drawdown = (equity_series - peak) / peak
            max_drawdown = drawdown.min()
            
            comparison_results[strategy_name] = {
                'total_return': total_return,
                'annualized_return': total_return * 2,  # 简化年化
                'volatility': volatility,
                'sharpe_ratio': sharpe_ratio,
                'max_drawdown': max_drawdown,
                'final_value': equity_values[-1]
            }
            
            print(f"     总收益: {total_return:.2%}")
            print(f"     年化收益: {total_return * 2:.2%}")
            print(f"     夏普比率: {sharpe_ratio:.2f}")
            print(f"     最大回撤: {max_drawdown:.2%}")
        
        print(f"\n--- 策略排名 ---")
        
        # 按夏普比率排序
        sorted_strategies = sorted(
            comparison_results.items(),
            key=lambda x: x[1]['sharpe_ratio'],
            reverse=True
        )
        
        print(f"   按夏普比率排名:")
        for i, (name, results) in enumerate(sorted_strategies, 1):
            print(f"     {i}. {name}: {results['sharpe_ratio']:.2f}")
        
        # 按总收益排序
        sorted_by_return = sorted(
            comparison_results.items(),
            key=lambda x: x[1]['total_return'],
            reverse=True
        )
        
        print(f"\n   按总收益排名:")
        for i, (name, results) in enumerate(sorted_by_return, 1):
            print(f"     {i}. {name}: {results['total_return']:.2%}")
        
        # 风险调整后收益
        print(f"\n   风险调整分析:")
        for name, results in comparison_results.items():
            risk_adj_return = results['total_return'] / abs(results['max_drawdown']) if results['max_drawdown'] != 0 else 0
            print(f"     {name}: 风险调整收益 = {risk_adj_return:.2f}")
        
        return comparison_results
    
    except Exception as e:
        print(f"策略比较演示失败: {e}")
        return {}


async def demo_optimization_suggestions():
    """演示优化建议"""
    print("\n=== 策略优化建议演示 ===")
    
    try:
        print("--- 基于回测结果的优化建议 ---")
        
        # 获取前面的分析结果
        analysis = await demo_performance_analysis()
        
        if not analysis:
            print("   ❌ 无分析结果可用于优化建议")
            return
        
        suggestions = []
        
        # 基于基本指标的建议
        basic_metrics = analysis.get('basic_metrics', {})
        if basic_metrics:
            sharpe_ratio = basic_metrics.get('sharpe_ratio', 0)
            max_drawdown = basic_metrics.get('max_drawdown', 0)
            volatility = basic_metrics.get('volatility', 0)
            
            if sharpe_ratio < 1.0:
                suggestions.append("📈 夏普比率偏低，建议优化风险收益比")
                suggestions.append("   - 考虑增加止损机制")
                suggestions.append("   - 优化仓位管理")
                suggestions.append("   - 调整策略参数以减少无效交易")
            
            if abs(max_drawdown) > 0.15:
                suggestions.append("📉 最大回撤过大，建议加强风险控制")
                suggestions.append("   - 实施动态仓位调整")
                suggestions.append("   - 添加市场状态过滤器")
                suggestions.append("   - 考虑分散投资")
            
            if volatility > 0.25:
                suggestions.append("📊 波动率较高，建议平滑收益曲线")
                suggestions.append("   - 增加信号过滤条件")
                suggestions.append("   - 使用多时间框架确认")
                suggestions.append("   - 考虑组合多个策略")
        
        # 基于交易分析的建议
        trade_analysis = analysis.get('trade_analysis', {})
        if trade_analysis:
            win_rate = trade_analysis.get('win_rate', 0)
            profit_factor = trade_analysis.get('profit_factor', 0)
            
            if win_rate < 0.5:
                suggestions.append("🎯 胜率偏低，建议提高信号质量")
                suggestions.append("   - 增加技术指标确认")
                suggestions.append("   - 优化入场时机")
                suggestions.append("   - 考虑市场环境过滤")
            
            if profit_factor < 1.5:
                suggestions.append("💰 盈亏比有待改善")
                suggestions.append("   - 优化止盈止损比例")
                suggestions.append("   - 改进出场策略")
                suggestions.append("   - 减少小额亏损交易")
        
        # 基于风险指标的建议
        risk_metrics = analysis.get('risk_metrics', {})
        if risk_metrics:
            max_consecutive_losses = risk_metrics.get('max_consecutive_losses', 0)
            
            if max_consecutive_losses > 5:
                suggestions.append("⚠️ 连续亏损次数过多")
                suggestions.append("   - 添加暂停交易机制")
                suggestions.append("   - 实施资金管理规则")
                suggestions.append("   - 考虑市场状态识别")
        
        # 通用优化建议
        suggestions.extend([
            "\n🔧 通用优化建议:",
            "   - 定期重新训练和验证策略参数",
            "   - 使用滚动窗口回测验证稳定性",
            "   - 考虑交易成本对策略的影响",
            "   - 实施实时监控和预警机制",
            "   - 建立策略表现基准和评估体系"
        ])
        
        print("\n".join(suggestions))
        
        print(f"\n--- 参数优化建议 ---")
        
        optimization_params = {
            '移动平均线策略': {
                'fast_period': [5, 10, 15, 20],
                'slow_period': [20, 30, 50, 100],
                '建议': '测试不同周期组合，寻找最佳参数'
            },
            'RSI策略': {
                'rsi_period': [10, 14, 21, 28],
                'oversold_threshold': [20, 25, 30, 35],
                'overbought_threshold': [65, 70, 75, 80],
                '建议': '根据市场特性调整超买超卖阈值'
            },
            '布林带策略': {
                'period': [15, 20, 25, 30],
                'std_dev': [1.5, 2.0, 2.5, 3.0],
                '建议': '平衡信号频率和准确性'
            }
        }
        
        for strategy, params in optimization_params.items():
            print(f"\n   {strategy}:")
            for param, values in params.items():
                if param != '建议':
                    print(f"     {param}: {values}")
            print(f"     💡 {params['建议']}")
        
        print(f"\n--- 风险管理建议 ---")
        
        risk_management_tips = [
            "🛡️ 仓位管理:",
            "   - 单笔交易风险不超过总资金的2%",
            "   - 总持仓不超过总资金的80%",
            "   - 根据波动率动态调整仓位大小",
            "",
            "🚫 止损策略:",
            "   - 设置固定百分比止损 (如5-10%)",
            "   - 使用技术指标止损 (如跌破支撑位)",
            "   - 实施时间止损 (如持仓超过N天)",
            "",
            "💎 止盈策略:",
            "   - 分批止盈，锁定部分利润",
            "   - 使用移动止盈跟踪趋势",
            "   - 基于技术指标确定止盈点"
        ]
        
        print("\n".join(risk_management_tips))
    
    except Exception as e:
        print(f"优化建议演示失败: {e}")


async def main():
    """主演示函数"""
    print("📊 回测框架演示")
    print("=" * 50)
    
    # 检查配置
    try:
        from config.settings import TRADING_CONFIG
        print("✅ 配置文件加载成功")
        
        # 检查回测配置
        backtest_config = TRADING_CONFIG.get('backtesting', {})
        print(f"回测配置:")
        print(f"  默认初始资金: ${backtest_config.get('initial_capital', 100000):,}")
        print(f"  默认手续费率: {backtest_config.get('commission_rate', 0.001):.1%}")
        print(f"  默认仓位大小: {backtest_config.get('position_size_pct', 0.1):.1%}")
    
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        print("使用默认配置继续演示")
    
    # 运行演示
    demos = [
        ("策略开发", demo_strategy_development),
        ("回测执行", demo_backtest_execution),
        ("性能分析", demo_performance_analysis),
        ("策略比较", demo_strategy_comparison),
        ("优化建议", demo_optimization_suggestions),
    ]
    
    for demo_name, demo_func in demos:
        try:
            print(f"\n🔄 开始 {demo_name} 演示...")
            await demo_func()
            print(f"✅ {demo_name} 演示完成")
        except Exception as e:
            print(f"❌ {demo_name} 演示失败: {e}")
            logger.error(f"{demo_name} demo failed", exc_info=True)
        
        # 在演示之间添加延迟
        await asyncio.sleep(1)
    
    print("\n🎉 所有演示完成!")
    print("=" * 50)
    print("\n💡 提示:")
    print("- 回测结果仅供参考，实际交易可能存在差异")
    print("- 建议使用多种策略和时间段进行验证")
    print("- 注意过拟合风险，避免过度优化历史数据")
    print("- 实盘前请进行充分的模拟交易测试")
    print("- 定期评估和调整策略参数")


if __name__ == "__main__":
    # 运行演示
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 演示被用户中断")
    except Exception as e:
        print(f"\n💥 演示过程中发生错误: {e}")
        logger.error("Demo failed", exc_info=True)
