#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日本大宗商品价格变动分析测试脚本
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import yfinance as yf
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def test_data_fetching():
    """测试数据获取"""
    print("开始测试数据获取...")
    
    # 测试几个主要商品
    test_symbols = {
        "黄金": "GC=F",
        "原油": "CL=F", 
        "铜": "HG=F",
        "日经225": "^N225",
        "日元汇率": "JPY=X"
    }
    
    results = {}
    
    for name, symbol in test_symbols.items():
        try:
            print(f"正在获取 {name} ({symbol}) 数据...")
            ticker = yf.Ticker(symbol)
            data = ticker.history(start="1990-01-01", end="2024-12-31")
            
            if not data.empty:
                results[name] = data
                print(f"✓ 成功获取 {name} 数据，共 {len(data)} 条记录")
                
                # 计算基本统计
                start_price = data['Close'].iloc[0]
                end_price = data['Close'].iloc[-1]
                change_pct = ((end_price - start_price) / start_price) * 100
                print(f"  起始价格: {start_price:.2f}")
                print(f"  结束价格: {end_price:.2f}")
                print(f"  变化率: {change_pct:.2f}%")
            else:
                print(f"✗ 无法获取 {name} 数据")
                
        except Exception as e:
            print(f"✗ 获取 {name} 数据时出错: {e}")
    
    return results

def analyze_japan_impact(data_dict):
    """分析日本经济影响"""
    print("\n=== 日本经济影响分析 ===")
    
    if "日经225" in data_dict and "日元汇率" in data_dict:
        nikkei = data_dict["日经225"]
        jpy = data_dict["日元汇率"]
        
        # 计算相关性
        common_index = nikkei.index.intersection(jpy.index)
        if len(common_index) > 100:
            nikkei_aligned = nikkei.loc[common_index, 'Close']
            jpy_aligned = jpy.loc[common_index, 'Close']
            
            correlation = nikkei_aligned.corr(jpy_aligned)
            print(f"日经225与日元汇率相关性: {correlation:.3f}")
            
            # 分析泡沫破裂前后
            pre_bubble = nikkei_aligned[nikkei_aligned.index < '1990-01-01']
            post_bubble = nikkei_aligned[nikkei_aligned.index >= '1990-01-01']
            
            if not pre_bubble.empty and not post_bubble.empty:
                pre_avg = pre_bubble.mean()
                post_avg = post_bubble.mean()
                print(f"泡沫破裂前日经225平均值: {pre_avg:.2f}")
                print(f"泡沫破裂后日经225平均值: {post_avg:.2f}")
                print(f"变化率: {((post_avg - pre_avg) / pre_avg) * 100:.2f}%")

def create_simple_visualization(data_dict):
    """创建简单可视化"""
    print("\n正在创建可视化图表...")
    
    if len(data_dict) >= 2:
        fig, axes = plt.subplots(2, 1, figsize=(12, 8))
        
        # 第一个子图：商品价格
        ax1 = axes[0]
        for name, data in data_dict.items():
            if name not in ["日经225", "日元汇率"]:
                ax1.plot(data.index, data['Close'], label=name, linewidth=2)
        ax1.set_title('大宗商品价格走势 (1990-2024)')
        ax1.set_ylabel('价格')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 第二个子图：日本经济指标
        ax2 = axes[1]
        for name, data in data_dict.items():
            if name in ["日经225", "日元汇率"]:
                ax2.plot(data.index, data['Close'], label=name, linewidth=2)
        ax2.set_title('日本经济指标走势 (1990-2024)')
        ax2.set_ylabel('指数值')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('japan_analysis_test.png', dpi=300, bbox_inches='tight')
        plt.show()
        print("✓ 图表已保存为 japan_analysis_test.png")

def generate_summary_report(data_dict):
    """生成总结报告"""
    print("\n" + "="*60)
    print("日本大宗商品价格变动分析总结报告")
    print("="*60)
    
    print("\n1. 数据获取情况:")
    for name, data in data_dict.items():
        if not data.empty:
            start_price = data['Close'].iloc[0]
            end_price = data['Close'].iloc[-1]
            change_pct = ((end_price - start_price) / start_price) * 100
            print(f"  {name}: {change_pct:+.2f}%")
    
    print("\n2. 关键发现:")
    print("• 日本泡沫破裂后，不同大宗商品表现出不同走势")
    print("• 贵金属如黄金在避险需求推动下表现较好")
    print("• 工业金属受全球经济周期影响较大")
    print("• 日元汇率变动对大宗商品价格有重要影响")
    
    print("\n3. 投资建议:")
    print("• 关注地缘政治风险对能源价格的影响")
    print("• 贵金属可作为避险资产配置")
    print("• 工业金属投资需关注全球经济复苏情况")
    print("• 建议多元化投资组合以分散风险")

def main():
    """主函数"""
    print("日本大宗商品价格变动分析测试")
    print("="*50)
    
    # 获取数据
    data_dict = test_data_fetching()
    
    if data_dict:
        # 分析日本经济影响
        analyze_japan_impact(data_dict)
        
        # 创建可视化
        create_simple_visualization(data_dict)
        
        # 生成报告
        generate_summary_report(data_dict)
    else:
        print("无法获取数据，请检查网络连接")

if __name__ == "__main__":
    main() 