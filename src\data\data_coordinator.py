"""
数据获取协调器
统一管理所有数据源的获取和缓存
"""
import asyncio
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timezone, timedelta
import json

from src.data.blockchain_api import EtherscanAPI, MoralisAPI
from src.data.defi_data import <PERSON>fiLlamaAP<PERSON>
from src.data.market_data import CoinGeckoAPI
from src.database.database import cache_manager
from src.utils.logger import get_logger
from config.settings import CACHE_CONFIG

logger = get_logger(__name__)


class DataCoordinator:
    """数据获取协调器"""
    
    def __init__(self):
        self.etherscan = None
        self.moralis = None
        self.defillama = None
        self.coingecko = None
        self.cache = cache_manager
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.etherscan = await EtherscanAPI().__aenter__()
        self.moralis = await MoralisAPI().__aenter__()
        self.defillama = await DefiLlamaAPI().__aenter__()
        self.coingecko = await CoinGeckoAPI().__aenter__()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.etherscan:
            await self.etherscan.__aexit__(exc_type, exc_val, exc_tb)
        if self.moralis:
            await self.moralis.__aexit__(exc_type, exc_val, exc_tb)
        if self.defillama:
            await self.defillama.__aexit__(exc_type, exc_val, exc_tb)
        if self.coingecko:
            await self.coingecko.__aexit__(exc_type, exc_val, exc_tb)
    
    def _get_cache_key(self, prefix: str, *args, **kwargs) -> str:
        """生成缓存键"""
        key_parts = [prefix] + [str(arg) for arg in args]
        if kwargs:
            key_parts.append(json.dumps(kwargs, sort_keys=True))
        return ':'.join(key_parts)
    
    async def _get_cached_data(self, cache_key: str, timeout: int = None) -> Optional[Any]:
        """获取缓存数据"""
        if not self.cache:
            return None
        
        try:
            cached_data = self.cache.get(cache_key)
            if cached_data:
                data = json.loads(cached_data)
                # 检查数据是否过期
                if timeout:
                    cached_time = datetime.fromisoformat(data.get('cached_at', ''))
                    if datetime.now(timezone.utc) - cached_time > timedelta(seconds=timeout):
                        return None
                return data.get('data')
        except Exception as e:
            logger.error(f"Error getting cached data for {cache_key}: {e}")
        
        return None
    
    async def _set_cached_data(self, cache_key: str, data: Any, timeout: int = None) -> None:
        """设置缓存数据"""
        if not self.cache:
            return
        
        try:
            cache_data = {
                'data': data,
                'cached_at': datetime.now(timezone.utc).isoformat()
            }
            cache_timeout = timeout or CACHE_CONFIG['default_timeout']
            self.cache.set(cache_key, json.dumps(cache_data, default=str), cache_timeout)
        except Exception as e:
            logger.error(f"Error setting cached data for {cache_key}: {e}")
    
    async def get_address_balance(self, address: str, use_cache: bool = True) -> Dict[str, Any]:
        """获取地址余额（带缓存）"""
        cache_key = self._get_cache_key('balance', address)
        
        if use_cache:
            cached_data = await self._get_cached_data(cache_key, CACHE_CONFIG['blockchain_data_timeout'])
            if cached_data:
                logger.debug(f"Using cached balance data for {address}")
                return cached_data
        
        try:
            # 优先使用Etherscan
            balance_data = await self.etherscan.get_balance(address)
            
            if use_cache:
                await self._set_cached_data(cache_key, balance_data, CACHE_CONFIG['blockchain_data_timeout'])
            
            return balance_data
        except Exception as e:
            logger.error(f"Failed to get balance for {address}: {e}")
            # 尝试使用Moralis作为备选
            try:
                balance_data = await self.moralis.get_native_balance(address)
                if use_cache:
                    await self._set_cached_data(cache_key, balance_data, CACHE_CONFIG['blockchain_data_timeout'])
                return balance_data
            except Exception as e2:
                logger.error(f"Fallback also failed for {address}: {e2}")
                raise e
    
    async def get_address_transactions(self, address: str, limit: int = 100, 
                                     use_cache: bool = True) -> List[Dict[str, Any]]:
        """获取地址交易历史（带缓存）"""
        cache_key = self._get_cache_key('transactions', address, limit)
        
        if use_cache:
            cached_data = await self._get_cached_data(cache_key, CACHE_CONFIG['blockchain_data_timeout'])
            if cached_data:
                logger.debug(f"Using cached transaction data for {address}")
                return cached_data
        
        try:
            transactions = await self.etherscan.get_transactions(address, offset=limit)
            
            if use_cache:
                await self._set_cached_data(cache_key, transactions, CACHE_CONFIG['blockchain_data_timeout'])
            
            return transactions
        except Exception as e:
            logger.error(f"Failed to get transactions for {address}: {e}")
            raise
    
    async def get_token_transfers(self, address: str, contract_address: str = None,
                                limit: int = 100, use_cache: bool = True) -> List[Dict[str, Any]]:
        """获取代币转账记录（带缓存）"""
        cache_key = self._get_cache_key('token_transfers', address, contract_address, limit)
        
        if use_cache:
            cached_data = await self._get_cached_data(cache_key, CACHE_CONFIG['blockchain_data_timeout'])
            if cached_data:
                logger.debug(f"Using cached token transfer data for {address}")
                return cached_data
        
        try:
            transfers = await self.etherscan.get_token_transfers(
                address, contract_address, offset=limit
            )
            
            if use_cache:
                await self._set_cached_data(cache_key, transfers, CACHE_CONFIG['blockchain_data_timeout'])
            
            return transfers
        except Exception as e:
            logger.error(f"Failed to get token transfers for {address}: {e}")
            raise
    
    async def get_token_price(self, coin_ids: Union[str, List[str]], 
                            vs_currency: str = 'usd', use_cache: bool = True) -> Dict[str, Any]:
        """获取代币价格（带缓存）"""
        if isinstance(coin_ids, str):
            coin_ids = [coin_ids]
        
        cache_key = self._get_cache_key('price', ','.join(coin_ids), vs_currency)
        
        if use_cache:
            cached_data = await self._get_cached_data(cache_key, CACHE_CONFIG['market_data_timeout'])
            if cached_data:
                logger.debug(f"Using cached price data for {coin_ids}")
                return cached_data
        
        try:
            price_data = await self.coingecko.get_price(coin_ids, vs_currency)
            
            if use_cache:
                await self._set_cached_data(cache_key, price_data, CACHE_CONFIG['market_data_timeout'])
            
            return price_data
        except Exception as e:
            logger.error(f"Failed to get price for {coin_ids}: {e}")
            raise
    
    async def get_defi_protocols(self, use_cache: bool = True) -> List[Dict[str, Any]]:
        """获取DeFi协议列表（带缓存）"""
        cache_key = self._get_cache_key('defi_protocols')
        
        if use_cache:
            cached_data = await self._get_cached_data(cache_key, CACHE_CONFIG['analysis_timeout'])
            if cached_data:
                logger.debug("Using cached DeFi protocols data")
                return cached_data
        
        try:
            protocols = await self.defillama.get_protocols()
            
            if use_cache:
                await self._set_cached_data(cache_key, protocols, CACHE_CONFIG['analysis_timeout'])
            
            return protocols
        except Exception as e:
            logger.error(f"Failed to get DeFi protocols: {e}")
            raise
    
    async def get_protocol_tvl(self, protocol: str, use_cache: bool = True) -> Dict[str, Any]:
        """获取协议TVL数据（带缓存）"""
        cache_key = self._get_cache_key('protocol_tvl', protocol)
        
        if use_cache:
            cached_data = await self._get_cached_data(cache_key, CACHE_CONFIG['analysis_timeout'])
            if cached_data:
                logger.debug(f"Using cached TVL data for {protocol}")
                return cached_data
        
        try:
            tvl_data = await self.defillama.get_protocol_tvl(protocol)
            
            if use_cache:
                await self._set_cached_data(cache_key, tvl_data, CACHE_CONFIG['analysis_timeout'])
            
            return tvl_data
        except Exception as e:
            logger.error(f"Failed to get TVL for {protocol}: {e}")
            raise
    
    async def get_yield_opportunities(self, use_cache: bool = True) -> List[Dict[str, Any]]:
        """获取收益机会（带缓存）"""
        cache_key = self._get_cache_key('yield_opportunities')
        
        if use_cache:
            cached_data = await self._get_cached_data(cache_key, CACHE_CONFIG['analysis_timeout'])
            if cached_data:
                logger.debug("Using cached yield opportunities data")
                return cached_data
        
        try:
            yields = await self.defillama.get_yields()
            
            if use_cache:
                await self._set_cached_data(cache_key, yields, CACHE_CONFIG['analysis_timeout'])
            
            return yields
        except Exception as e:
            logger.error(f"Failed to get yield opportunities: {e}")
            raise
    
    async def get_market_overview(self, coin_ids: List[str], use_cache: bool = True) -> Dict[str, Any]:
        """获取市场概览（多个数据源组合）"""
        cache_key = self._get_cache_key('market_overview', ','.join(coin_ids))
        
        if use_cache:
            cached_data = await self._get_cached_data(cache_key, CACHE_CONFIG['market_data_timeout'])
            if cached_data:
                logger.debug("Using cached market overview data")
                return cached_data
        
        try:
            # 并行获取多个数据源
            tasks = [
                self.get_token_price(coin_ids, use_cache=False),
                self.coingecko.get_trending(),
                self.coingecko.get_fear_greed_index()
            ]
            
            price_data, trending_data, fear_greed_data = await asyncio.gather(*tasks)
            
            overview = {
                'prices': price_data,
                'trending': trending_data,
                'fear_greed_index': fear_greed_data,
                'timestamp': datetime.now(timezone.utc)
            }
            
            if use_cache:
                await self._set_cached_data(cache_key, overview, CACHE_CONFIG['market_data_timeout'])
            
            return overview
        except Exception as e:
            logger.error(f"Failed to get market overview: {e}")
            raise
