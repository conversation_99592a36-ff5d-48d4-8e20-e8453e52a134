"""
分析工具API路由
提供技术分析、链上分析和Smart Money分析功能
"""
from fastapi import APIRouter, HTTPException, Query
from typing import Dict, List, Optional, Any
from datetime import datetime, timezone, timedelta
import random

from src.analysis.smart_money_signals import SmartMoneySignalGenerator
from src.indicators.technical_indicators import TechnicalIndicatorCalculator
from src.indicators.onchain_indicators import OnChainIndicatorCalculator
from src.database.repositories import MarketDataRepository, TransactionRepository
from src.utils.logger import get_logger

logger = get_logger(__name__)

router = APIRouter()

# 初始化组件
smart_money_generator = SmartMoneySignalGenerator()
technical_calculator = TechnicalIndicatorCalculator()
onchain_calculator = OnChainIndicatorCalculator()
market_data_repo = MarketDataRepository()
transaction_repo = TransactionRepository()


@router.get("/smart-money/{symbol}")
async def get_smart_money_analysis(
    symbol: str,
    days: int = Query(default=7, ge=1, le=30)
):
    """获取Smart Money分析"""
    try:
        # 生成Smart Money信号
        signals = await smart_money_generator.generate_signals([symbol])
        
        # 模拟Smart Money数据
        smart_money_data = {
            'symbol': symbol,
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'signals': signals,
            'summary': {
                'overall_sentiment': random.choice(['bullish', 'bearish', 'neutral']),
                'confidence_score': random.uniform(0.6, 0.9),
                'activity_level': random.choice(['low', 'medium', 'high']),
                'net_flow': random.uniform(-10000000, 10000000)
            },
            'metrics': {
                'unique_addresses': random.randint(50, 200),
                'total_volume_usd': random.uniform(50000000, 500000000),
                'avg_transaction_size': random.uniform(100000, 1000000),
                'buy_sell_ratio': random.uniform(0.3, 1.7)
            },
            'top_transactions': [
                {
                    'tx_hash': f'0x{random.randint(10**15, 10**16-1):016x}',
                    'amount_usd': random.uniform(1000000, 10000000),
                    'type': random.choice(['buy', 'sell']),
                    'timestamp': (datetime.now(timezone.utc) - timedelta(hours=random.randint(1, 24))).isoformat()
                }
                for _ in range(5)
            ]
        }
        
        return smart_money_data
    
    except Exception as e:
        logger.error(f"Failed to get smart money analysis for {symbol}: {e}")
        raise HTTPException(status_code=500, detail="Failed to get smart money analysis")


@router.get("/technical/{symbol}")
async def get_technical_analysis(
    symbol: str,
    timeframe: str = Query(default='1d'),
    indicators: List[str] = Query(default=['sma', 'rsi', 'macd'])
):
    """获取技术分析"""
    try:
        # 获取价格数据
        price_history = market_data_repo.get_price_history(symbol, days=30)
        
        if not price_history:
            # 生成模拟数据
            base_price = {'BTC': 50000, 'ETH': 3000, 'ADA': 1.0, 'DOT': 10.0}.get(symbol, 100)
            prices = [base_price * (1 + random.uniform(-0.02, 0.02)) for _ in range(30)]
        else:
            prices = [p.price_usd for p in price_history]
        
        # 计算技术指标
        technical_data = {
            'symbol': symbol,
            'timeframe': timeframe,
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'indicators': {}
        }
        
        if 'sma' in indicators:
            technical_data['indicators']['sma'] = {
                'sma_20': sum(prices[-20:]) / 20 if len(prices) >= 20 else prices[-1],
                'sma_50': sum(prices[-50:]) / 50 if len(prices) >= 50 else prices[-1],
                'signal': random.choice(['bullish', 'bearish', 'neutral'])
            }
        
        if 'rsi' in indicators:
            technical_data['indicators']['rsi'] = {
                'value': random.uniform(20, 80),
                'signal': random.choice(['oversold', 'overbought', 'neutral'])
            }
        
        if 'macd' in indicators:
            technical_data['indicators']['macd'] = {
                'macd_line': random.uniform(-100, 100),
                'signal_line': random.uniform(-100, 100),
                'histogram': random.uniform(-50, 50),
                'signal': random.choice(['bullish', 'bearish', 'neutral'])
            }
        
        # 综合信号
        signals = [ind.get('signal', 'neutral') for ind in technical_data['indicators'].values()]
        bullish_count = signals.count('bullish')
        bearish_count = signals.count('bearish')
        
        if bullish_count > bearish_count:
            overall_signal = 'bullish'
        elif bearish_count > bullish_count:
            overall_signal = 'bearish'
        else:
            overall_signal = 'neutral'
        
        technical_data['summary'] = {
            'overall_signal': overall_signal,
            'strength': random.uniform(0.5, 1.0),
            'confidence': random.uniform(0.6, 0.9)
        }
        
        return technical_data
    
    except Exception as e:
        logger.error(f"Failed to get technical analysis for {symbol}: {e}")
        raise HTTPException(status_code=500, detail="Failed to get technical analysis")


@router.get("/onchain/{symbol}")
async def get_onchain_analysis(
    symbol: str,
    metrics: List[str] = Query(default=['active_addresses', 'transaction_count', 'network_value'])
):
    """获取链上分析"""
    try:
        onchain_data = {
            'symbol': symbol,
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'metrics': {}
        }
        
        if 'active_addresses' in metrics:
            onchain_data['metrics']['active_addresses'] = {
                'current': random.randint(500000, 1500000),
                'change_24h': random.uniform(-0.05, 0.05),
                'trend': random.choice(['increasing', 'decreasing', 'stable'])
            }
        
        if 'transaction_count' in metrics:
            onchain_data['metrics']['transaction_count'] = {
                'current': random.randint(200000, 400000),
                'change_24h': random.uniform(-0.1, 0.1),
                'trend': random.choice(['increasing', 'decreasing', 'stable'])
            }
        
        if 'network_value' in metrics:
            onchain_data['metrics']['network_value'] = {
                'nvt_ratio': random.uniform(50, 150),
                'mvrv_ratio': random.uniform(1, 3),
                'realized_cap': random.uniform(300000000000, 600000000000)
            }
        
        # 健康度评分
        onchain_data['health_score'] = {
            'overall': random.uniform(0.6, 0.9),
            'network_growth': random.uniform(0.5, 1.0),
            'network_value': random.uniform(0.5, 1.0),
            'market_sentiment': random.uniform(0.4, 0.8)
        }
        
        return onchain_data
    
    except Exception as e:
        logger.error(f"Failed to get onchain analysis for {symbol}: {e}")
        raise HTTPException(status_code=500, detail="Failed to get onchain analysis")


@router.get("/correlation-matrix")
async def get_correlation_matrix(
    symbols: List[str] = Query(default=['BTC', 'ETH', 'ADA', 'DOT']),
    days: int = Query(default=30, ge=7, le=365)
):
    """获取相关性矩阵"""
    try:
        # 模拟相关性数据
        correlation_matrix = {}
        
        for symbol1 in symbols:
            correlation_matrix[symbol1] = {}
            for symbol2 in symbols:
                if symbol1 == symbol2:
                    correlation_matrix[symbol1][symbol2] = 1.0
                else:
                    # 生成合理的相关性值
                    correlation_matrix[symbol1][symbol2] = random.uniform(0.3, 0.8)
        
        return {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'period_days': days,
            'symbols': symbols,
            'correlation_matrix': correlation_matrix,
            'summary': {
                'avg_correlation': sum(
                    correlation_matrix[s1][s2] 
                    for s1 in symbols for s2 in symbols if s1 != s2
                ) / (len(symbols) * (len(symbols) - 1)),
                'highest_correlation': max(
                    correlation_matrix[s1][s2] 
                    for s1 in symbols for s2 in symbols if s1 != s2
                ),
                'lowest_correlation': min(
                    correlation_matrix[s1][s2] 
                    for s1 in symbols for s2 in symbols if s1 != s2
                )
            }
        }
    
    except Exception as e:
        logger.error(f"Failed to get correlation matrix: {e}")
        raise HTTPException(status_code=500, detail="Failed to get correlation matrix")


@router.get("/market-sentiment")
async def get_market_sentiment():
    """获取市场情绪分析"""
    try:
        sentiment_data = {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'overall_sentiment': {
                'score': random.uniform(-1, 1),
                'label': random.choice(['极度恐惧', '恐惧', '中性', '贪婪', '极度贪婪']),
                'confidence': random.uniform(0.7, 0.95)
            },
            'fear_greed_index': {
                'value': random.randint(0, 100),
                'classification': random.choice(['极度恐惧', '恐惧', '中性', '贪婪', '极度贪婪']),
                'change_24h': random.randint(-10, 10)
            },
            'social_sentiment': {
                'twitter_sentiment': random.uniform(-0.5, 0.5),
                'reddit_sentiment': random.uniform(-0.5, 0.5),
                'news_sentiment': random.uniform(-0.5, 0.5),
                'mentions_24h': random.randint(10000, 100000)
            },
            'market_indicators': {
                'volatility_index': random.uniform(0.2, 0.8),
                'momentum_score': random.uniform(-1, 1),
                'trend_strength': random.uniform(0, 1)
            }
        }
        
        return sentiment_data
    
    except Exception as e:
        logger.error(f"Failed to get market sentiment: {e}")
        raise HTTPException(status_code=500, detail="Failed to get market sentiment")


@router.post("/custom-analysis")
async def run_custom_analysis(analysis_config: dict):
    """运行自定义分析"""
    try:
        # 解析分析配置
        symbols = analysis_config.get('symbols', ['BTC'])
        analysis_types = analysis_config.get('analysis_types', ['technical'])
        timeframe = analysis_config.get('timeframe', '1d')
        
        results = {}
        
        for symbol in symbols:
            results[symbol] = {
                'symbol': symbol,
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'analyses': {}
            }
            
            for analysis_type in analysis_types:
                if analysis_type == 'technical':
                    results[symbol]['analyses']['technical'] = {
                        'signal': random.choice(['bullish', 'bearish', 'neutral']),
                        'strength': random.uniform(0.5, 1.0),
                        'key_levels': {
                            'support': random.uniform(40000, 45000) if symbol == 'BTC' else random.uniform(2500, 2800),
                            'resistance': random.uniform(52000, 55000) if symbol == 'BTC' else random.uniform(3200, 3500)
                        }
                    }
                
                elif analysis_type == 'onchain':
                    results[symbol]['analyses']['onchain'] = {
                        'health_score': random.uniform(0.6, 0.9),
                        'network_activity': random.choice(['low', 'medium', 'high']),
                        'holder_behavior': random.choice(['accumulating', 'distributing', 'holding'])
                    }
                
                elif analysis_type == 'smart_money':
                    results[symbol]['analyses']['smart_money'] = {
                        'sentiment': random.choice(['bullish', 'bearish', 'neutral']),
                        'activity_level': random.choice(['low', 'medium', 'high']),
                        'net_flow_usd': random.uniform(-10000000, 10000000)
                    }
        
        return {
            'analysis_id': f"custom_{int(datetime.now().timestamp())}",
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'config': analysis_config,
            'results': results
        }
    
    except Exception as e:
        logger.error(f"Failed to run custom analysis: {e}")
        raise HTTPException(status_code=500, detail="Failed to run custom analysis")
