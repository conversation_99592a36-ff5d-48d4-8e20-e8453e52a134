#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日本大宗商品价格变动分析 (1990-2024)
分析日本从90年代泡沫破裂后这三十年来大宗商品价格的变动情况
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import yfinance as yf
import requests
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class JapanCommodityAnalyzer:
    """日本大宗商品价格分析器"""
    
    def __init__(self):
        self.start_date = "1990-01-01"
        self.end_date = "2024-12-31"
        
        # 定义主要大宗商品类别
        self.commodity_categories = {
            "能源": {
                "原油": ["CL=F", "BZ=F"],  # WTI原油, 布伦特原油
                "天然气": ["NG=F"],
                "煤炭": ["^DJCI"]
            },
            "金属": {
                "黄金": ["GC=F"],
                "白银": ["SI=F"],
                "铜": ["HG=F"],
                "铝": ["ALI=F"],
                "铁矿石": ["^DJCI"]
            },
            "农产品": {
                "大豆": ["ZS=F"],
                "玉米": ["ZC=F"],
                "小麦": ["ZW=F"],
                "棉花": ["CT=F"],
                "糖": ["SB=F"]
            },
            "软商品": {
                "咖啡": ["KC=F"],
                "可可": ["CC=F"],
                "橙汁": ["OJ=F"]
            }
        }
        
        # 日本经济指标
        self.japan_indicators = {
            "日经225": ["^N225"],
            "日元汇率": ["JPY=X"],
            "日本国债": ["^TNX"],
            "日本CPI": None  # 需要从其他数据源获取
        }
        
        self.data = {}
        
    def fetch_commodity_data(self, symbol, start_date=None, end_date=None):
        """获取大宗商品数据"""
        try:
            if start_date is None:
                start_date = self.start_date
            if end_date is None:
                end_date = self.end_date
                
            ticker = yf.Ticker(symbol)
            data = ticker.history(start=start_date, end=end_date)
            
            if not data.empty:
                return data
            else:
                print(f"警告: 无法获取 {symbol} 的数据")
                return None
        except Exception as e:
            print(f"获取 {symbol} 数据时出错: {e}")
            return None
    
    def fetch_all_commodity_data(self):
        """获取所有大宗商品数据"""
        print("正在获取大宗商品数据...")
        
        for category, commodities in self.commodity_categories.items():
            self.data[category] = {}
            for commodity_name, symbols in commodities.items():
                self.data[category][commodity_name] = {}
                for symbol in symbols:
                    data = self.fetch_commodity_data(symbol)
                    if data is not None:
                        self.data[category][commodity_name][symbol] = data
                        print(f"✓ 已获取 {commodity_name} ({symbol}) 数据")
        
        # 获取日本经济指标数据
        print("\n正在获取日本经济指标数据...")
        self.data["日本经济指标"] = {}
        for indicator_name, symbols in self.japan_indicators.items():
            if symbols:
                for symbol in symbols:
                    data = self.fetch_commodity_data(symbol)
                    if data is not None:
                        self.data["日本经济指标"][indicator_name] = data
                        print(f"✓ 已获取 {indicator_name} ({symbol}) 数据")
    
    def calculate_price_changes(self, data, periods=[1, 5, 10, 20, 30]):
        """计算不同时期的价格变化"""
        if data is None or data.empty:
            return None
            
        changes = {}
        for period in periods:
            if len(data) > period * 252:  # 假设一年252个交易日
                start_price = data['Close'].iloc[-period*252]
                end_price = data['Close'].iloc[-1]
                change_pct = ((end_price - start_price) / start_price) * 100
                changes[f"{period}年变化(%)"] = change_pct
            else:
                changes[f"{period}年变化(%)"] = None
        return changes
    
    def analyze_commodity_performance(self):
        """分析大宗商品表现"""
        print("\n=== 日本大宗商品价格变动分析 (1990-2024) ===")
        
        analysis_results = {}
        
        for category, commodities in self.data.items():
            if category == "日本经济指标":
                continue
                
            print(f"\n--- {category} ---")
            analysis_results[category] = {}
            
            for commodity_name, symbol_data in commodities.items():
                for symbol, data in symbol_data.items():
                    if data is not None and not data.empty:
                        # 计算关键指标
                        current_price = data['Close'].iloc[-1]
                        peak_price = data['High'].max()
                        trough_price = data['Low'].min()
                        avg_price = data['Close'].mean()
                        
                        # 计算泡沫破裂后的表现 (1990年后)
                        post_bubble_data = data[data.index >= '1990-01-01']
                        if not post_bubble_data.empty:
                            post_bubble_start = post_bubble_data['Close'].iloc[0]
                            post_bubble_current = post_bubble_data['Close'].iloc[-1]
                            post_bubble_change = ((post_bubble_current - post_bubble_start) / post_bubble_start) * 100
                            
                            # 计算不同时期变化
                            changes = self.calculate_price_changes(post_bubble_data)
                            
                            result = {
                                "当前价格": current_price,
                                "历史最高": peak_price,
                                "历史最低": trough_price,
                                "平均价格": avg_price,
                                "泡沫破裂后变化(%)": post_bubble_change,
                                "价格变化详情": changes
                            }
                            
                            analysis_results[category][commodity_name] = result
                            
                            print(f"{commodity_name}:")
                            print(f"  当前价格: {current_price:.2f}")
                            print(f"  泡沫破裂后变化: {post_bubble_change:.2f}%")
                            if changes:
                                for period, change in changes.items():
                                    if change is not None:
                                        print(f"  {period}: {change:.2f}%")
        
        return analysis_results
    
    def analyze_japan_economy_impact(self):
        """分析日本经济对大宗商品的影响"""
        print("\n=== 日本经济指标分析 ===")
        
        economy_results = {}
        
        if "日本经济指标" in self.data:
            for indicator_name, data in self.data["日本经济指标"].items():
                if data is not None and not data.empty:
                    # 计算关键指标
                    current_value = data['Close'].iloc[-1]
                    post_bubble_data = data[data.index >= '1990-01-01']
                    
                    if not post_bubble_data.empty:
                        post_bubble_start = post_bubble_data['Close'].iloc[0]
                        post_bubble_current = post_bubble_data['Close'].iloc[-1]
                        post_bubble_change = ((post_bubble_current - post_bubble_start) / post_bubble_start) * 100
                        
                        changes = self.calculate_price_changes(post_bubble_data)
                        
                        result = {
                            "当前值": current_value,
                            "泡沫破裂后变化(%)": post_bubble_change,
                            "变化详情": changes
                        }
                        
                        economy_results[indicator_name] = result
                        
                        print(f"{indicator_name}:")
                        print(f"  当前值: {current_value:.2f}")
                        print(f"  泡沫破裂后变化: {post_bubble_change:.2f}%")
        
        return economy_results
    
    def create_visualizations(self):
        """创建可视化图表"""
        print("\n正在生成可视化图表...")
        
        # 设置图表样式
        plt.style.use('seaborn-v0_8')
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('日本大宗商品价格变动分析 (1990-2024)', fontsize=16, fontweight='bold')
        
        # 1. 能源类商品价格走势
        ax1 = axes[0, 0]
        if "能源" in self.data:
            for commodity_name, symbol_data in self.data["能源"].items():
                for symbol, data in symbol_data.items():
                    if data is not None and not data.empty:
                        post_bubble_data = data[data.index >= '1990-01-01']
                        if not post_bubble_data.empty:
                            ax1.plot(post_bubble_data.index, post_bubble_data['Close'], 
                                    label=f"{commodity_name}", linewidth=2)
        ax1.set_title('能源类商品价格走势')
        ax1.set_ylabel('价格')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 2. 金属类商品价格走势
        ax2 = axes[0, 1]
        if "金属" in self.data:
            for commodity_name, symbol_data in self.data["金属"].items():
                for symbol, data in symbol_data.items():
                    if data is not None and not data.empty:
                        post_bubble_data = data[data.index >= '1990-01-01']
                        if not post_bubble_data.empty:
                            ax2.plot(post_bubble_data.index, post_bubble_data['Close'], 
                                    label=f"{commodity_name}", linewidth=2)
        ax2.set_title('金属类商品价格走势')
        ax2.set_ylabel('价格')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 3. 农产品价格走势
        ax3 = axes[1, 0]
        if "农产品" in self.data:
            for commodity_name, symbol_data in self.data["农产品"].items():
                for symbol, data in symbol_data.items():
                    if data is not None and not data.empty:
                        post_bubble_data = data[data.index >= '1990-01-01']
                        if not post_bubble_data.empty:
                            ax3.plot(post_bubble_data.index, post_bubble_data['Close'], 
                                    label=f"{commodity_name}", linewidth=2)
        ax3.set_title('农产品价格走势')
        ax3.set_ylabel('价格')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # 4. 日本经济指标
        ax4 = axes[1, 1]
        if "日本经济指标" in self.data:
            for indicator_name, data in self.data["日本经济指标"].items():
                if data is not None and not data.empty:
                    post_bubble_data = data[data.index >= '1990-01-01']
                    if not post_bubble_data.empty:
                        ax4.plot(post_bubble_data.index, post_bubble_data['Close'], 
                                label=f"{indicator_name}", linewidth=2)
        ax4.set_title('日本经济指标走势')
        ax4.set_ylabel('指数值')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('japan_commodity_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        # 创建价格变化对比图
        self.create_performance_comparison()
    
    def create_performance_comparison(self):
        """创建价格表现对比图"""
        # 收集所有商品的价格变化数据
        performance_data = []
        
        for category, commodities in self.data.items():
            if category == "日本经济指标":
                continue
            for commodity_name, symbol_data in commodities.items():
                for symbol, data in symbol_data.items():
                    if data is not None and not data.empty:
                        post_bubble_data = data[data.index >= '1990-01-01']
                        if not post_bubble_data.empty:
                            start_price = post_bubble_data['Close'].iloc[0]
                            end_price = post_bubble_data['Close'].iloc[-1]
                            change_pct = ((end_price - start_price) / start_price) * 100
                            
                            performance_data.append({
                                '商品': commodity_name,
                                '类别': category,
                                '变化率(%)': change_pct
                            })
        
        if performance_data:
            df_performance = pd.DataFrame(performance_data)
            
            plt.figure(figsize=(14, 8))
            
            # 按类别分组显示
            categories = df_performance['类别'].unique()
            colors = plt.cm.Set3(np.linspace(0, 1, len(categories)))
            
            for i, category in enumerate(categories):
                cat_data = df_performance[df_performance['类别'] == category]
                plt.barh(cat_data['商品'], cat_data['变化率(%)'], 
                        label=category, color=colors[i], alpha=0.7)
            
            plt.axvline(x=0, color='black', linestyle='--', alpha=0.5)
            plt.xlabel('价格变化率 (%)')
            plt.title('日本泡沫破裂后大宗商品价格变化对比 (1990-2024)')
            plt.legend()
            plt.grid(True, alpha=0.3)
            plt.tight_layout()
            plt.savefig('japan_commodity_performance.png', dpi=300, bbox_inches='tight')
            plt.show()
    
    def generate_report(self):
        """生成分析报告"""
        print("\n" + "="*60)
        print("日本大宗商品价格变动分析报告")
        print("="*60)
        
        # 获取数据
        self.fetch_all_commodity_data()
        
        # 分析商品表现
        commodity_results = self.analyze_commodity_performance()
        
        # 分析经济指标
        economy_results = self.analyze_japan_economy_impact()
        
        # 创建可视化
        self.create_visualizations()
        
        # 生成总结报告
        self.generate_summary_report(commodity_results, economy_results)
    
    def generate_summary_report(self, commodity_results, economy_results):
        """生成总结报告"""
        print("\n" + "="*60)
        print("分析总结")
        print("="*60)
        
        # 找出表现最好和最差的商品
        all_performances = []
        for category, commodities in commodity_results.items():
            for commodity_name, result in commodities.items():
                all_performances.append({
                    '商品': commodity_name,
                    '类别': category,
                    '变化率': result['泡沫破裂后变化(%)']
                })
        
        if all_performances:
            df_perf = pd.DataFrame(all_performances)
            df_perf = df_perf.sort_values('变化率', ascending=False)
            
            print("\n表现最好的大宗商品 (1990-2024):")
            print(df_perf.head().to_string(index=False))
            
            print("\n表现最差的大宗商品 (1990-2024):")
            print(df_perf.tail().to_string(index=False))
            
            print(f"\n平均变化率: {df_perf['变化率'].mean():.2f}%")
            print(f"中位数变化率: {df_perf['变化率'].median():.2f}%")
        
        # 日本经济影响分析
        if economy_results:
            print("\n日本经济指标影响:")
            for indicator, result in economy_results.items():
                print(f"{indicator}: {result['泡沫破裂后变化(%)']:.2f}%")

def main():
    """主函数"""
    analyzer = JapanCommodityAnalyzer()
    analyzer.generate_report()

if __name__ == "__main__":
    main() 