"""
交易管理API路由
提供交易策略、回测和风险管理功能
"""
from fastapi import APIRouter, HTTPException, Query
from typing import Dict, List, Optional, Any
from datetime import datetime, timezone, timedelta
import random

from src.backtesting.backtest_engine import BacktestEngine, BacktestConfig
from src.backtesting.strategies import StrategyFactory
from src.risk_management.risk_engine import RiskEngine
from src.risk_management.position_manager import PositionManager, PositionSizeConfig, PositionSizeMethod
from src.utils.logger import get_logger

logger = get_logger(__name__)

router = APIRouter()

# 初始化组件
risk_engine = RiskEngine()


@router.get("/strategies")
async def get_available_strategies():
    """获取可用策略列表"""
    try:
        strategies = StrategyFactory.get_available_strategies()
        
        strategy_info = {
            'ma_crossover': {
                'name': '移动平均线交叉',
                'description': '基于快慢移动平均线交叉的趋势跟踪策略',
                'parameters': ['fast_period', 'slow_period'],
                'risk_level': 'medium'
            },
            'rsi_mean_reversion': {
                'name': 'RSI均值回归',
                'description': '基于RSI超买超卖的均值回归策略',
                'parameters': ['rsi_period', 'oversold_threshold', 'overbought_threshold'],
                'risk_level': 'medium'
            },
            'bollinger_bands': {
                'name': '布林带策略',
                'description': '基于布林带的突破和回归策略',
                'parameters': ['period', 'std_dev'],
                'risk_level': 'low'
            },
            'smart_money_follow': {
                'name': 'Smart Money跟随',
                'description': '跟随聪明资金的投资策略',
                'parameters': ['min_confidence', 'min_impact_score'],
                'risk_level': 'high'
            },
            'buy_and_hold': {
                'name': '买入持有',
                'description': '长期持有策略',
                'parameters': [],
                'risk_level': 'low'
            }
        }
        
        return {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'strategies': [
                {
                    'id': strategy_id,
                    'name': info['name'],
                    'description': info['description'],
                    'parameters': info['parameters'],
                    'risk_level': info['risk_level']
                }
                for strategy_id, info in strategy_info.items()
                if strategy_id in strategies
            ]
        }
    
    except Exception as e:
        logger.error(f"Failed to get available strategies: {e}")
        raise HTTPException(status_code=500, detail="Failed to get available strategies")


@router.post("/backtest")
async def run_backtest(backtest_request: dict):
    """运行回测"""
    try:
        # 解析回测请求
        strategy_config = backtest_request.get('strategy', {})
        symbols = backtest_request.get('symbols', ['BTC'])
        start_date = datetime.fromisoformat(backtest_request.get('start_date', '2023-01-01T00:00:00Z'))
        end_date = datetime.fromisoformat(backtest_request.get('end_date', '2023-12-31T23:59:59Z'))
        initial_capital = backtest_request.get('initial_capital', 100000)
        
        # 创建回测配置
        config = BacktestConfig(
            start_date=start_date,
            end_date=end_date,
            initial_capital=initial_capital,
            commission_rate=backtest_request.get('commission_rate', 0.001),
            slippage_rate=backtest_request.get('slippage_rate', 0.0005)
        )
        
        # 由于需要真实数据，这里返回模拟回测结果
        backtest_results = {
            'backtest_id': f"bt_{int(datetime.now().timestamp())}",
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'config': {
                'strategy': strategy_config,
                'symbols': symbols,
                'start_date': start_date.isoformat(),
                'end_date': end_date.isoformat(),
                'initial_capital': initial_capital
            },
            'results': {
                'total_return': random.uniform(-0.2, 0.5),
                'annualized_return': random.uniform(-0.1, 0.3),
                'volatility': random.uniform(0.15, 0.4),
                'sharpe_ratio': random.uniform(0.5, 2.0),
                'max_drawdown': random.uniform(-0.3, -0.05),
                'total_trades': random.randint(50, 200),
                'winning_trades': random.randint(25, 120),
                'win_rate': random.uniform(0.4, 0.7),
                'profit_factor': random.uniform(1.1, 2.5),
                'final_portfolio_value': initial_capital * (1 + random.uniform(-0.2, 0.5))
            },
            'equity_curve': [
                {
                    'date': (start_date + timedelta(days=i)).isoformat(),
                    'value': initial_capital * (1 + random.uniform(-0.1, 0.1) * i / 365)
                }
                for i in range(0, (end_date - start_date).days, 7)
            ],
            'trades': [
                {
                    'symbol': random.choice(symbols),
                    'side': random.choice(['buy', 'sell']),
                    'quantity': random.uniform(0.1, 2.0),
                    'price': random.uniform(30000, 60000) if 'BTC' in symbols else random.uniform(2000, 4000),
                    'timestamp': (start_date + timedelta(days=random.randint(0, (end_date - start_date).days))).isoformat(),
                    'pnl': random.uniform(-1000, 2000)
                }
                for _ in range(random.randint(20, 50))
            ]
        }
        
        return backtest_results
    
    except Exception as e:
        logger.error(f"Failed to run backtest: {e}")
        raise HTTPException(status_code=500, detail="Failed to run backtest")


@router.get("/risk-assessment/{symbol}")
async def get_risk_assessment(symbol: str):
    """获取风险评估"""
    try:
        risk_metrics = await risk_engine.assess_asset_risk(symbol)
        
        return {
            'symbol': symbol,
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'risk_metrics': {
                'overall_risk_score': risk_metrics.overall_risk_score,
                'risk_level': risk_metrics.risk_level.value,
                'volatility': risk_metrics.volatility,
                'var_95': risk_metrics.var_95,
                'cvar_95': risk_metrics.cvar_95,
                'liquidity_score': risk_metrics.liquidity_score,
                'credit_score': risk_metrics.credit_score,
                'counterparty_risk': risk_metrics.counterparty_risk,
                'smart_contract_risk': risk_metrics.smart_contract_risk
            },
            'recommendations': [
                '根据风险等级调整仓位大小',
                '设置适当的止损止盈',
                '定期监控风险指标变化',
                '考虑投资组合分散化'
            ]
        }
    
    except Exception as e:
        logger.error(f"Failed to get risk assessment for {symbol}: {e}")
        raise HTTPException(status_code=500, detail="Failed to get risk assessment")


@router.post("/portfolio-optimization")
async def optimize_portfolio(optimization_request: dict):
    """投资组合优化"""
    try:
        symbols = optimization_request.get('symbols', ['BTC', 'ETH'])
        total_capital = optimization_request.get('total_capital', 100000)
        risk_tolerance = optimization_request.get('risk_tolerance', 'medium')
        
        # 模拟优化结果
        num_assets = len(symbols)
        weights = [random.uniform(0.1, 0.5) for _ in range(num_assets)]
        total_weight = sum(weights)
        weights = [w / total_weight for w in weights]  # 标准化权重
        
        optimization_results = {
            'optimization_id': f"opt_{int(datetime.now().timestamp())}",
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'input': {
                'symbols': symbols,
                'total_capital': total_capital,
                'risk_tolerance': risk_tolerance
            },
            'optimal_weights': {
                symbol: weight for symbol, weight in zip(symbols, weights)
            },
            'allocation': {
                symbol: {
                    'weight': weight,
                    'amount': total_capital * weight,
                    'expected_return': random.uniform(0.05, 0.25),
                    'risk': random.uniform(0.15, 0.4)
                }
                for symbol, weight in zip(symbols, weights)
            },
            'portfolio_metrics': {
                'expected_return': sum(random.uniform(0.05, 0.25) * w for w in weights),
                'expected_volatility': random.uniform(0.2, 0.35),
                'sharpe_ratio': random.uniform(0.8, 1.5),
                'max_drawdown_estimate': random.uniform(-0.25, -0.1)
            },
            'rebalancing_suggestions': [
                f"增加 {symbols[0]} 配置至 {weights[0]:.1%}",
                f"减少 {symbols[-1]} 配置至 {weights[-1]:.1%}",
                "建议每月进行一次再平衡"
            ]
        }
        
        return optimization_results
    
    except Exception as e:
        logger.error(f"Failed to optimize portfolio: {e}")
        raise HTTPException(status_code=500, detail="Failed to optimize portfolio")


@router.get("/position-sizing")
async def get_position_sizing_recommendation(
    symbol: str,
    signal_strength: float = Query(default=1.0, ge=0.1, le=1.0),
    total_capital: float = Query(default=100000, gt=0),
    risk_per_trade: float = Query(default=0.02, gt=0, le=0.1)
):
    """获取仓位大小建议"""
    try:
        # 创建仓位管理配置
        config = PositionSizeConfig(
            method=PositionSizeMethod.VOLATILITY_ADJUSTED,
            base_size=0.1,
            max_position_size=0.2,
            max_total_exposure=0.8,
            risk_per_trade=risk_per_trade
        )
        
        position_manager = PositionManager(config)
        position_manager.set_capital(total_capital)
        
        # 计算建议仓位大小
        recommended_size = await position_manager.calculate_position_size(symbol, signal_strength)
        
        return {
            'symbol': symbol,
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'input': {
                'signal_strength': signal_strength,
                'total_capital': total_capital,
                'risk_per_trade': risk_per_trade
            },
            'recommendation': {
                'position_size_usd': recommended_size,
                'position_size_pct': recommended_size / total_capital,
                'max_loss_usd': recommended_size * risk_per_trade,
                'suggested_stop_loss_pct': risk_per_trade,
                'suggested_take_profit_pct': risk_per_trade * 2  # 1:2 风险收益比
            },
            'risk_analysis': {
                'position_risk': 'low' if recommended_size / total_capital < 0.1 else 'medium',
                'diversification_impact': 'positive' if recommended_size / total_capital < 0.15 else 'neutral',
                'liquidity_impact': 'minimal'
            }
        }
    
    except Exception as e:
        logger.error(f"Failed to get position sizing recommendation: {e}")
        raise HTTPException(status_code=500, detail="Failed to get position sizing recommendation")


@router.get("/trading-signals/{symbol}")
async def get_trading_signals(
    symbol: str,
    timeframe: str = Query(default='1d'),
    signal_types: List[str] = Query(default=['technical', 'smart_money'])
):
    """获取交易信号"""
    try:
        signals = {
            'symbol': symbol,
            'timeframe': timeframe,
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'signals': {}
        }
        
        if 'technical' in signal_types:
            signals['signals']['technical'] = {
                'signal': random.choice(['buy', 'sell', 'hold']),
                'strength': random.uniform(0.5, 1.0),
                'confidence': random.uniform(0.6, 0.9),
                'indicators': {
                    'rsi': random.uniform(20, 80),
                    'macd': random.choice(['bullish', 'bearish']),
                    'moving_averages': random.choice(['golden_cross', 'death_cross', 'neutral'])
                }
            }
        
        if 'smart_money' in signal_types:
            signals['signals']['smart_money'] = {
                'signal': random.choice(['buy', 'sell', 'hold']),
                'strength': random.uniform(0.4, 0.9),
                'confidence': random.uniform(0.5, 0.8),
                'activity_level': random.choice(['low', 'medium', 'high']),
                'net_flow_usd': random.uniform(-5000000, 5000000)
            }
        
        if 'sentiment' in signal_types:
            signals['signals']['sentiment'] = {
                'signal': random.choice(['buy', 'sell', 'hold']),
                'strength': random.uniform(0.3, 0.8),
                'fear_greed_index': random.randint(0, 100),
                'social_sentiment': random.uniform(-1, 1)
            }
        
        # 综合信号
        all_signals = [sig.get('signal', 'hold') for sig in signals['signals'].values()]
        buy_count = all_signals.count('buy')
        sell_count = all_signals.count('sell')
        
        if buy_count > sell_count:
            composite_signal = 'buy'
        elif sell_count > buy_count:
            composite_signal = 'sell'
        else:
            composite_signal = 'hold'
        
        signals['composite'] = {
            'signal': composite_signal,
            'strength': random.uniform(0.5, 0.9),
            'consensus': max(buy_count, sell_count) / len(all_signals)
        }
        
        return signals
    
    except Exception as e:
        logger.error(f"Failed to get trading signals for {symbol}: {e}")
        raise HTTPException(status_code=500, detail="Failed to get trading signals")
