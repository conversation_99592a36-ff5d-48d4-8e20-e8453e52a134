"""
仓位管理模块
实现动态仓位调整、资金分配和风险控制
"""
import asyncio
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timezone, timedelta
from dataclasses import dataclass
from enum import Enum

from src.risk_management.risk_engine import RiskEngine, RiskLevel
from src.utils.logger import get_logger

logger = get_logger(__name__)


class PositionSizeMethod(Enum):
    """仓位大小计算方法"""
    FIXED_AMOUNT = "fixed_amount"
    FIXED_PERCENTAGE = "fixed_percentage"
    VOLATILITY_ADJUSTED = "volatility_adjusted"
    RISK_PARITY = "risk_parity"
    KELLY_CRITERION = "kelly_criterion"
    MAX_DRAWDOWN_BASED = "max_drawdown_based"


@dataclass
class PositionSizeConfig:
    """仓位大小配置"""
    method: PositionSizeMethod
    base_size: float  # 基础仓位大小
    max_position_size: float  # 最大单仓位大小
    max_total_exposure: float  # 最大总敞口
    risk_per_trade: float  # 单笔交易风险
    volatility_lookback: int = 20  # 波动率回看期
    rebalance_threshold: float = 0.05  # 再平衡阈值


@dataclass
class PositionRecommendation:
    """仓位建议"""
    symbol: str
    action: str  # buy, sell, hold, reduce, increase
    current_size: float
    recommended_size: float
    size_change: float
    reason: str
    confidence: float
    risk_level: RiskLevel


class PositionManager:
    """仓位管理器"""
    
    def __init__(self, config: PositionSizeConfig):
        self.config = config
        self.risk_engine = RiskEngine()
        
        # 当前持仓
        self.positions: Dict[str, Dict[str, float]] = {}
        self.total_capital = 0
        self.available_capital = 0
        
        # 历史数据
        self.price_history: Dict[str, pd.DataFrame] = {}
        self.volatility_cache: Dict[str, float] = {}
    
    def set_capital(self, total_capital: float, available_capital: float = None) -> None:
        """设置资金"""
        self.total_capital = total_capital
        self.available_capital = available_capital or total_capital
        logger.info(f"Capital set: total=${total_capital:,.2f}, available=${self.available_capital:,.2f}")
    
    def update_positions(self, positions: Dict[str, Dict[str, float]]) -> None:
        """更新当前持仓"""
        self.positions = positions
        
        # 计算已用资金
        used_capital = sum(pos.get('value', 0) for pos in positions.values())
        self.available_capital = self.total_capital - used_capital
        
        logger.info(f"Positions updated: {len(positions)} positions, "
                   f"used capital=${used_capital:,.2f}")
    
    async def calculate_position_size(self, symbol: str, signal_strength: float = 1.0,
                                    current_price: float = None) -> float:
        """计算建议仓位大小"""
        try:
            if self.config.method == PositionSizeMethod.FIXED_AMOUNT:
                return self._calculate_fixed_amount_size()
            
            elif self.config.method == PositionSizeMethod.FIXED_PERCENTAGE:
                return self._calculate_fixed_percentage_size()
            
            elif self.config.method == PositionSizeMethod.VOLATILITY_ADJUSTED:
                return await self._calculate_volatility_adjusted_size(symbol, signal_strength)
            
            elif self.config.method == PositionSizeMethod.RISK_PARITY:
                return await self._calculate_risk_parity_size(symbol)
            
            elif self.config.method == PositionSizeMethod.KELLY_CRITERION:
                return await self._calculate_kelly_size(symbol, signal_strength)
            
            elif self.config.method == PositionSizeMethod.MAX_DRAWDOWN_BASED:
                return await self._calculate_max_drawdown_size(symbol)
            
            else:
                return self._calculate_fixed_percentage_size()
        
        except Exception as e:
            logger.error(f"Failed to calculate position size for {symbol}: {e}")
            return self._calculate_fixed_percentage_size()
    
    def _calculate_fixed_amount_size(self) -> float:
        """固定金额仓位"""
        return min(self.config.base_size, self.available_capital)
    
    def _calculate_fixed_percentage_size(self) -> float:
        """固定百分比仓位"""
        size = self.total_capital * self.config.base_size
        return min(size, self.available_capital, 
                  self.total_capital * self.config.max_position_size)
    
    async def _calculate_volatility_adjusted_size(self, symbol: str, 
                                                signal_strength: float) -> float:
        """波动率调整仓位"""
        try:
            # 获取波动率
            volatility = await self._get_volatility(symbol)
            
            # 目标波动率
            target_volatility = 0.15  # 15%
            
            # 调整因子
            volatility_adjustment = target_volatility / volatility if volatility > 0 else 1.0
            volatility_adjustment = min(2.0, max(0.5, volatility_adjustment))  # 限制在0.5-2.0之间
            
            # 基础仓位
            base_size = self.total_capital * self.config.base_size
            
            # 调整后仓位
            adjusted_size = base_size * volatility_adjustment * signal_strength
            
            # 应用限制
            max_size = self.total_capital * self.config.max_position_size
            return min(adjusted_size, self.available_capital, max_size)
        
        except Exception as e:
            logger.error(f"Failed to calculate volatility adjusted size: {e}")
            return self._calculate_fixed_percentage_size()
    
    async def _calculate_risk_parity_size(self, symbol: str) -> float:
        """风险平价仓位"""
        try:
            # 获取当前所有持仓的风险贡献
            total_risk_budget = self.total_capital * self.config.risk_per_trade
            
            # 获取资产风险
            risk_metrics = await self.risk_engine.assess_asset_risk(symbol)
            asset_volatility = risk_metrics.volatility
            
            # 计算风险预算分配
            if asset_volatility > 0:
                risk_adjusted_size = total_risk_budget / asset_volatility
            else:
                risk_adjusted_size = self._calculate_fixed_percentage_size()
            
            # 应用限制
            max_size = self.total_capital * self.config.max_position_size
            return min(risk_adjusted_size, self.available_capital, max_size)
        
        except Exception as e:
            logger.error(f"Failed to calculate risk parity size: {e}")
            return self._calculate_fixed_percentage_size()
    
    async def _calculate_kelly_size(self, symbol: str, signal_strength: float) -> float:
        """凯利公式仓位"""
        try:
            # 简化的凯利公式实现
            # 需要胜率和平均盈亏比数据
            
            # 假设参数（实际应该从历史数据计算）
            win_rate = 0.55  # 55%胜率
            avg_win = 0.08   # 8%平均盈利
            avg_loss = 0.05  # 5%平均亏损
            
            # 凯利比例 = (胜率 * 平均盈利 - 败率 * 平均亏损) / 平均盈利
            kelly_fraction = (win_rate * avg_win - (1 - win_rate) * avg_loss) / avg_win
            
            # 应用信号强度调整
            kelly_fraction *= signal_strength
            
            # 保守调整（通常使用1/4或1/2凯利）
            conservative_kelly = kelly_fraction * 0.25
            
            # 计算仓位大小
            kelly_size = self.total_capital * max(0, min(conservative_kelly, self.config.max_position_size))
            
            return min(kelly_size, self.available_capital)
        
        except Exception as e:
            logger.error(f"Failed to calculate Kelly size: {e}")
            return self._calculate_fixed_percentage_size()
    
    async def _calculate_max_drawdown_size(self, symbol: str) -> float:
        """基于最大回撤的仓位"""
        try:
            # 获取历史最大回撤
            risk_metrics = await self.risk_engine.assess_asset_risk(symbol)
            
            # 假设最大回撤数据（实际应该从历史数据计算）
            historical_max_drawdown = 0.3  # 30%
            
            # 目标最大回撤
            target_max_drawdown = 0.1  # 10%
            
            # 计算仓位调整因子
            drawdown_adjustment = target_max_drawdown / historical_max_drawdown
            
            # 基础仓位
            base_size = self.total_capital * self.config.base_size
            
            # 调整后仓位
            adjusted_size = base_size * drawdown_adjustment
            
            # 应用限制
            max_size = self.total_capital * self.config.max_position_size
            return min(adjusted_size, self.available_capital, max_size)
        
        except Exception as e:
            logger.error(f"Failed to calculate max drawdown size: {e}")
            return self._calculate_fixed_percentage_size()
    
    async def _get_volatility(self, symbol: str) -> float:
        """获取资产波动率"""
        try:
            # 检查缓存
            if symbol in self.volatility_cache:
                return self.volatility_cache[symbol]
            
            # 计算波动率
            risk_metrics = await self.risk_engine.assess_asset_risk(symbol)
            volatility = risk_metrics.volatility
            
            # 缓存结果
            self.volatility_cache[symbol] = volatility
            
            return volatility
        
        except Exception as e:
            logger.error(f"Failed to get volatility for {symbol}: {e}")
            return 0.3  # 默认30%波动率
    
    async def generate_position_recommendations(self, 
                                              signals: List[Dict[str, Any]]) -> List[PositionRecommendation]:
        """生成仓位调整建议"""
        try:
            recommendations = []
            
            for signal in signals:
                symbol = signal['symbol']
                action = signal['action']
                signal_strength = signal.get('confidence', 1.0)
                
                # 获取当前持仓
                current_position = self.positions.get(symbol, {})
                current_size = current_position.get('value', 0)
                
                # 计算建议仓位大小
                if action == 'buy':
                    recommended_size = await self.calculate_position_size(
                        symbol, signal_strength
                    )
                    action_type = 'buy' if current_size == 0 else 'increase'
                
                elif action == 'sell':
                    recommended_size = 0
                    action_type = 'sell' if current_size > 0 else 'hold'
                
                else:  # hold
                    recommended_size = current_size
                    action_type = 'hold'
                
                # 检查是否需要调整
                size_change = recommended_size - current_size
                
                if abs(size_change) > self.total_capital * self.config.rebalance_threshold:
                    # 获取风险评估
                    risk_metrics = await self.risk_engine.assess_asset_risk(symbol)
                    
                    recommendation = PositionRecommendation(
                        symbol=symbol,
                        action=action_type,
                        current_size=current_size,
                        recommended_size=recommended_size,
                        size_change=size_change,
                        reason=self._generate_position_reason(signal, risk_metrics),
                        confidence=signal_strength,
                        risk_level=risk_metrics.risk_level
                    )
                    
                    recommendations.append(recommendation)
            
            # 检查总敞口限制
            recommendations = self._apply_exposure_limits(recommendations)
            
            return recommendations
        
        except Exception as e:
            logger.error(f"Failed to generate position recommendations: {e}")
            return []
    
    def _generate_position_reason(self, signal: Dict[str, Any], risk_metrics) -> str:
        """生成仓位调整原因"""
        reasons = []
        
        # 信号原因
        if 'reason' in signal:
            reasons.append(f"信号: {signal['reason']}")
        
        # 风险考虑
        if risk_metrics.risk_level == RiskLevel.HIGH:
            reasons.append("高风险资产，降低仓位")
        elif risk_metrics.risk_level == RiskLevel.LOW:
            reasons.append("低风险资产，可适当增加仓位")
        
        # 波动率考虑
        if risk_metrics.volatility > 0.4:
            reasons.append("高波动率，调整仓位大小")
        
        return "; ".join(reasons) if reasons else "基于策略信号"
    
    def _apply_exposure_limits(self, recommendations: List[PositionRecommendation]) -> List[PositionRecommendation]:
        """应用敞口限制"""
        try:
            # 计算总建议敞口
            total_recommended_exposure = sum(
                max(0, rec.recommended_size) for rec in recommendations
            )
            
            # 加上现有其他持仓
            other_positions_value = sum(
                pos.get('value', 0) for symbol, pos in self.positions.items()
                if not any(rec.symbol == symbol for rec in recommendations)
            )
            
            total_exposure = total_recommended_exposure + other_positions_value
            max_exposure = self.total_capital * self.config.max_total_exposure
            
            # 如果超过限制，按比例缩减
            if total_exposure > max_exposure:
                scale_factor = max_exposure / total_exposure
                
                for rec in recommendations:
                    if rec.recommended_size > 0:
                        rec.recommended_size *= scale_factor
                        rec.size_change = rec.recommended_size - rec.current_size
                        rec.reason += f"; 按比例缩减({scale_factor:.2f})以控制总敞口"
            
            return recommendations
        
        except Exception as e:
            logger.error(f"Failed to apply exposure limits: {e}")
            return recommendations
    
    async def rebalance_portfolio(self) -> List[PositionRecommendation]:
        """投资组合再平衡"""
        try:
            logger.info("Starting portfolio rebalancing")
            
            if not self.positions:
                return []
            
            recommendations = []
            
            # 评估当前投资组合风险
            portfolio_risk = await self.risk_engine.assess_portfolio_risk(self.positions)
            
            # 基于风险评估生成调整建议
            for symbol, position in self.positions.items():
                current_size = position.get('value', 0)
                
                # 获取资产风险
                asset_risk = await self.risk_engine.assess_asset_risk(symbol)
                
                # 基于风险调整仓位
                if asset_risk.risk_level == RiskLevel.VERY_HIGH:
                    # 高风险资产减仓
                    recommended_size = current_size * 0.5
                    action = 'reduce'
                    reason = "高风险资产，建议减仓"
                
                elif asset_risk.risk_level == RiskLevel.HIGH:
                    recommended_size = current_size * 0.7
                    action = 'reduce'
                    reason = "较高风险，适度减仓"
                
                elif portfolio_risk.concentration_risk > 0.6 and current_size > self.total_capital * 0.3:
                    # 集中度过高，减少大仓位
                    recommended_size = self.total_capital * 0.2
                    action = 'reduce'
                    reason = "降低集中度风险"
                
                else:
                    recommended_size = current_size
                    action = 'hold'
                    reason = "维持当前仓位"
                
                size_change = recommended_size - current_size
                
                if abs(size_change) > self.total_capital * self.config.rebalance_threshold:
                    recommendation = PositionRecommendation(
                        symbol=symbol,
                        action=action,
                        current_size=current_size,
                        recommended_size=recommended_size,
                        size_change=size_change,
                        reason=reason,
                        confidence=0.8,
                        risk_level=asset_risk.risk_level
                    )
                    
                    recommendations.append(recommendation)
            
            logger.info(f"Generated {len(recommendations)} rebalancing recommendations")
            return recommendations
        
        except Exception as e:
            logger.error(f"Failed to rebalance portfolio: {e}")
            return []
    
    def get_position_summary(self) -> Dict[str, Any]:
        """获取仓位摘要"""
        try:
            total_position_value = sum(pos.get('value', 0) for pos in self.positions.values())
            
            return {
                'total_capital': self.total_capital,
                'available_capital': self.available_capital,
                'used_capital': total_position_value,
                'capital_utilization': total_position_value / self.total_capital if self.total_capital > 0 else 0,
                'number_of_positions': len(self.positions),
                'largest_position': max((pos.get('value', 0) for pos in self.positions.values()), default=0),
                'largest_position_pct': max((pos.get('value', 0) for pos in self.positions.values()), default=0) / self.total_capital if self.total_capital > 0 else 0,
                'position_config': {
                    'method': self.config.method.value,
                    'base_size': self.config.base_size,
                    'max_position_size': self.config.max_position_size,
                    'max_total_exposure': self.config.max_total_exposure,
                    'risk_per_trade': self.config.risk_per_trade
                }
            }
        
        except Exception as e:
            logger.error(f"Failed to get position summary: {e}")
            return {}
