"""
回测框架测试
"""
import pytest
import asyncio
import pandas as pd
import numpy as np
from datetime import datetime, timezone, timedelta
from unittest.mock import Mock, patch, MagicMock

from src.backtesting.backtest_engine import (
    BacktestEngine, BacktestConfig, Order, Trade, Position, Portfolio,
    OrderType, OrderSide, OrderStatus
)
from src.backtesting.strategies import (
    MovingAverageCrossoverStrategy, RSIMeanReversionStrategy, 
    BollingerBandsStrategy, StrategyFactory
)
from src.backtesting.performance_analyzer import PerformanceAnalyzer


class TestBacktestEngine:
    """回测引擎测试"""
    
    def test_backtest_config(self):
        """测试回测配置"""
        config = BacktestConfig(
            start_date=datetime(2023, 1, 1, tzinfo=timezone.utc),
            end_date=datetime(2023, 12, 31, tzinfo=timezone.utc),
            initial_capital=100000.0,
            commission_rate=0.001
        )
        
        assert config.initial_capital == 100000.0
        assert config.commission_rate == 0.001
        assert config.start_date.year == 2023
        assert config.end_date.year == 2023
    
    def test_order_creation(self):
        """测试订单创建"""
        order = Order(
            id="test_order_1",
            symbol="BTC",
            side=OrderSide.BUY,
            order_type=OrderType.MARKET,
            quantity=1.0
        )
        
        assert order.id == "test_order_1"
        assert order.symbol == "BTC"
        assert order.side == OrderSide.BUY
        assert order.order_type == OrderType.MARKET
        assert order.quantity == 1.0
        assert order.status == OrderStatus.PENDING
    
    def test_trade_creation(self):
        """测试交易记录创建"""
        trade = Trade(
            id="test_trade_1",
            order_id="test_order_1",
            symbol="BTC",
            side=OrderSide.BUY,
            quantity=1.0,
            price=50000.0,
            timestamp=datetime.now(timezone.utc),
            commission=50.0
        )
        
        assert trade.id == "test_trade_1"
        assert trade.symbol == "BTC"
        assert trade.quantity == 1.0
        assert trade.price == 50000.0
        assert trade.commission == 50.0
    
    def test_position_update(self):
        """测试持仓更新"""
        position = Position(symbol="BTC")
        
        # 买入交易
        buy_trade = Trade(
            id="trade_1",
            order_id="order_1",
            symbol="BTC",
            side=OrderSide.BUY,
            quantity=1.0,
            price=50000.0,
            timestamp=datetime.now(timezone.utc),
            commission=50.0
        )
        
        position.update_position(buy_trade)
        
        assert position.quantity == 1.0
        assert position.avg_price == 50000.0
        
        # 再次买入
        buy_trade_2 = Trade(
            id="trade_2",
            order_id="order_2",
            symbol="BTC",
            side=OrderSide.BUY,
            quantity=1.0,
            price=52000.0,
            timestamp=datetime.now(timezone.utc),
            commission=52.0
        )
        
        position.update_position(buy_trade_2)
        
        assert position.quantity == 2.0
        assert position.avg_price == 51000.0  # (50000 + 52000) / 2
        
        # 卖出交易
        sell_trade = Trade(
            id="trade_3",
            order_id="order_3",
            symbol="BTC",
            side=OrderSide.SELL,
            quantity=1.0,
            price=53000.0,
            timestamp=datetime.now(timezone.utc),
            commission=53.0
        )
        
        position.update_position(sell_trade)
        
        assert position.quantity == 1.0
        assert position.realized_pnl == 2000.0  # (53000 - 51000) * 1
    
    def test_portfolio_management(self):
        """测试投资组合管理"""
        portfolio = Portfolio(
            initial_capital=100000.0,
            cash=100000.0
        )
        
        # 买入交易
        buy_trade = Trade(
            id="trade_1",
            order_id="order_1",
            symbol="BTC",
            side=OrderSide.BUY,
            quantity=1.0,
            price=50000.0,
            timestamp=datetime.now(timezone.utc),
            commission=50.0
        )
        
        current_prices = {"BTC": 50000.0}
        portfolio.update_portfolio(buy_trade, current_prices)
        
        assert portfolio.cash == 49950.0  # 100000 - 50000 - 50
        assert len(portfolio.positions) == 1
        assert portfolio.positions["BTC"].quantity == 1.0
        
        # 计算总价值
        total_value = portfolio.calculate_total_value(current_prices)
        assert total_value == 99950.0  # 49950 + 50000
    
    def test_backtest_engine_initialization(self):
        """测试回测引擎初始化"""
        config = BacktestConfig(
            start_date=datetime(2023, 1, 1, tzinfo=timezone.utc),
            end_date=datetime(2023, 12, 31, tzinfo=timezone.utc),
            initial_capital=100000.0
        )
        
        engine = BacktestEngine(config)
        
        assert engine.config == config
        assert engine.portfolio.initial_capital == 100000.0
        assert engine.portfolio.cash == 100000.0
        assert len(engine.orders) == 0
    
    def test_order_filling_logic(self):
        """测试订单成交逻辑"""
        config = BacktestConfig(
            start_date=datetime(2023, 1, 1, tzinfo=timezone.utc),
            end_date=datetime(2023, 12, 31, tzinfo=timezone.utc),
            initial_capital=100000.0
        )
        
        engine = BacktestEngine(config)
        engine.current_prices = {"BTC": 50000.0}
        
        # 市价单应该立即成交
        market_order = Order(
            id="market_order",
            symbol="BTC",
            side=OrderSide.BUY,
            order_type=OrderType.MARKET,
            quantity=1.0
        )
        
        assert engine._should_fill_order(market_order) == True
        
        # 限价买单，当前价格高于限价，不成交
        limit_order = Order(
            id="limit_order",
            symbol="BTC",
            side=OrderSide.BUY,
            order_type=OrderType.LIMIT,
            quantity=1.0,
            price=49000.0
        )
        
        assert engine._should_fill_order(limit_order) == False
        
        # 限价买单，当前价格低于限价，成交
        limit_order.price = 51000.0
        assert engine._should_fill_order(limit_order) == True


class TestStrategies:
    """策略测试"""
    
    @pytest.mark.asyncio
    async def test_moving_average_strategy(self):
        """测试移动平均线策略"""
        strategy = MovingAverageCrossoverStrategy(fast_period=5, slow_period=10)
        
        # 创建测试数据
        dates = pd.date_range('2023-01-01', periods=20, freq='D')
        prices = [100 + i for i in range(20)]  # 上涨趋势
        
        df = pd.DataFrame({
            'timestamp': dates,
            'close': prices,
            'open': prices,
            'high': [p * 1.02 for p in prices],
            'low': [p * 0.98 for p in prices],
            'volume': [1000000] * 20
        })
        
        data = {
            'current_prices': {'BTC': prices[-1]},
            'price_data': {'BTC': df}
        }
        
        signals = await strategy.generate_signals(data)
        
        # 在上涨趋势中应该有买入信号
        assert isinstance(signals, list)
        if signals:
            assert signals[0]['action'] in ['buy', 'sell']
    
    @pytest.mark.asyncio
    async def test_rsi_strategy(self):
        """测试RSI策略"""
        strategy = RSIMeanReversionStrategy(rsi_period=14)
        
        # 创建测试数据 - 先上涨后下跌
        dates = pd.date_range('2023-01-01', periods=30, freq='D')
        prices = [100] * 15 + [100 - i for i in range(15)]  # 前15天平稳，后15天下跌
        
        df = pd.DataFrame({
            'timestamp': dates,
            'close': prices,
            'open': prices,
            'high': [p * 1.02 for p in prices],
            'low': [p * 0.98 for p in prices],
            'volume': [1000000] * 30
        })
        
        data = {
            'current_prices': {'BTC': prices[-1]},
            'price_data': {'BTC': df}
        }
        
        signals = await strategy.generate_signals(data)
        
        assert isinstance(signals, list)
    
    @pytest.mark.asyncio
    async def test_bollinger_bands_strategy(self):
        """测试布林带策略"""
        strategy = BollingerBandsStrategy(period=20, std_dev=2.0)
        
        # 创建测试数据
        dates = pd.date_range('2023-01-01', periods=25, freq='D')
        # 创建波动的价格数据
        np.random.seed(42)
        prices = 100 + np.cumsum(np.random.randn(25) * 2)
        
        df = pd.DataFrame({
            'timestamp': dates,
            'close': prices,
            'open': prices,
            'high': prices * 1.02,
            'low': prices * 0.98,
            'volume': [1000000] * 25
        })
        
        data = {
            'current_prices': {'BTC': prices[-1]},
            'price_data': {'BTC': df}
        }
        
        signals = await strategy.generate_signals(data)
        
        assert isinstance(signals, list)
    
    def test_strategy_factory(self):
        """测试策略工厂"""
        # 测试创建移动平均策略
        ma_strategy = StrategyFactory.create_strategy(
            'ma_crossover', 
            fast_period=10, 
            slow_period=20
        )
        
        assert isinstance(ma_strategy, MovingAverageCrossoverStrategy)
        assert ma_strategy.fast_period == 10
        assert ma_strategy.slow_period == 20
        
        # 测试创建RSI策略
        rsi_strategy = StrategyFactory.create_strategy(
            'rsi_mean_reversion',
            rsi_period=14,
            oversold_threshold=30
        )
        
        assert isinstance(rsi_strategy, RSIMeanReversionStrategy)
        assert rsi_strategy.rsi_period == 14
        
        # 测试获取可用策略
        available_strategies = StrategyFactory.get_available_strategies()
        assert 'ma_crossover' in available_strategies
        assert 'rsi_mean_reversion' in available_strategies
        
        # 测试未知策略
        with pytest.raises(ValueError):
            StrategyFactory.create_strategy('unknown_strategy')


class TestPerformanceAnalyzer:
    """性能分析器测试"""
    
    def test_analyzer_initialization(self):
        """测试分析器初始化"""
        analyzer = PerformanceAnalyzer()
        
        assert analyzer.results == {}
        assert analyzer.benchmark_results == {}
    
    def test_basic_metrics_calculation(self):
        """测试基本指标计算"""
        analyzer = PerformanceAnalyzer()
        
        # 创建模拟回测结果
        dates = pd.date_range('2023-01-01', periods=100, freq='D')
        values = [100000 * (1 + 0.001) ** i for i in range(100)]  # 0.1%日收益
        
        equity_curve = list(zip(dates, values))
        
        results = {
            'equity_curve': equity_curve,
            'total_return': 0.1,
            'trades': []
        }
        
        analyzer.results = results
        basic_metrics = analyzer._calculate_basic_metrics()
        
        assert 'total_return' in basic_metrics
        assert 'annualized_return' in basic_metrics
        assert 'volatility' in basic_metrics
        assert 'sharpe_ratio' in basic_metrics
        assert basic_metrics['initial_value'] == values[0]
        assert basic_metrics['final_value'] == values[-1]
    
    def test_trade_analysis(self):
        """测试交易分析"""
        analyzer = PerformanceAnalyzer()
        
        # 创建模拟交易数据
        trades = [
            {'symbol': 'BTC', 'side': 'buy', 'pnl': 1000, 'quantity': 1, 'price': 50000},
            {'symbol': 'BTC', 'side': 'sell', 'pnl': -500, 'quantity': 1, 'price': 49500},
            {'symbol': 'ETH', 'side': 'buy', 'pnl': 2000, 'quantity': 10, 'price': 3000},
            {'symbol': 'ETH', 'side': 'sell', 'pnl': 1500, 'quantity': 10, 'price': 3150}
        ]
        
        analyzer.results = {'trades': trades}
        trade_analysis = analyzer._trade_analysis()
        
        assert trade_analysis['total_trades'] == 4
        assert trade_analysis['winning_trades'] == 3
        assert trade_analysis['losing_trades'] == 1
        assert trade_analysis['win_rate'] == 0.75
        assert 'asset_performance' in trade_analysis
    
    def test_drawdown_analysis(self):
        """测试回撤分析"""
        analyzer = PerformanceAnalyzer()
        
        # 创建有回撤的权益曲线
        dates = pd.date_range('2023-01-01', periods=50, freq='D')
        values = [100000] * 10 + [100000 - i * 1000 for i in range(10)] + [90000] * 30
        
        equity_curve = list(zip(dates, values))
        analyzer.results = {'equity_curve': equity_curve}
        
        drawdown_analysis = analyzer._analyze_drawdowns()
        
        assert 'max_drawdown' in drawdown_analysis
        assert 'max_drawdown_date' in drawdown_analysis
        assert drawdown_analysis['max_drawdown'] < 0  # 回撤应该是负数
    
    def test_report_generation(self):
        """测试报告生成"""
        analyzer = PerformanceAnalyzer()
        
        # 创建完整的分析结果
        analysis = {
            'basic_metrics': {
                'total_return': 0.15,
                'annualized_return': 0.12,
                'volatility': 0.20,
                'sharpe_ratio': 0.6,
                'max_drawdown': -0.05
            },
            'trade_analysis': {
                'total_trades': 100,
                'win_rate': 0.65,
                'profit_factor': 1.8,
                'avg_win': 500,
                'avg_loss': -300
            },
            'risk_metrics': {
                'var_95': -0.02,
                'max_consecutive_losses': 5,
                'skewness': 0.1,
                'kurtosis': 2.5
            }
        }
        
        report = analyzer.generate_report(analysis)
        
        assert isinstance(report, str)
        assert '回测性能分析报告' in report
        assert '15.00%' in report  # 总收益率
        assert '65.00%' in report  # 胜率


class TestIntegration:
    """集成测试"""
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_full_backtest_workflow(self):
        """测试完整回测工作流程"""
        # 这个测试需要真实的数据库连接，通常在开发环境中跳过
        pytest.skip("Integration test requires real database connection")
        
        # 1. 创建回测配置
        config = BacktestConfig(
            start_date=datetime(2023, 1, 1, tzinfo=timezone.utc),
            end_date=datetime(2023, 3, 31, tzinfo=timezone.utc),
            initial_capital=100000.0
        )
        
        # 2. 创建回测引擎
        engine = BacktestEngine(config)
        
        # 3. 创建策略
        strategy = MovingAverageCrossoverStrategy(fast_period=10, slow_period=20)
        
        # 4. 设置策略
        async def strategy_wrapper(data):
            return await strategy.generate_signals(data)
        
        engine.set_strategy(strategy_wrapper)
        
        # 5. 运行回测
        results = await engine.run_backtest(['BTC', 'ETH'])
        
        # 6. 分析结果
        analyzer = PerformanceAnalyzer()
        analysis = analyzer.analyze_backtest_results(results)
        
        # 7. 生成报告
        report = analyzer.generate_report(analysis)
        
        assert isinstance(results, dict)
        assert isinstance(analysis, dict)
        assert isinstance(report, str)


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v", "-m", "not integration"])
