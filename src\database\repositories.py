"""
数据仓库模块
提供各种数据模型的专门化仓库类
"""
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timezone, timedelta
from sqlalchemy import and_, or_, desc, asc, func
from sqlalchemy.orm import Session

from .database import BaseRepository, db_manager
from .models import (
    Address, Transaction, Token, SmartMoneyActivity, WhaleAlert,
    MarketData, OnChainMetrics, TradingSignal, BacktestResult
)
from src.utils.logger import get_logger

logger = get_logger(__name__)


class AddressRepository(BaseRepository):
    """地址仓库"""
    
    def __init__(self):
        super().__init__(Address)
    
    def get_by_address(self, address: str) -> Optional[Address]:
        """根据地址获取记录"""
        try:
            with self.db_manager.get_session() as session:
                return session.query(Address).filter(
                    Address.address == address.lower()
                ).first()
        except Exception as e:
            logger.error(f"Failed to get address {address}: {e}")
            raise
    
    def get_smart_money_addresses(self, limit: int = 100) -> List[Address]:
        """获取Smart Money地址列表"""
        try:
            with self.db_manager.get_session() as session:
                return session.query(Address).filter(
                    Address.is_smart_money == True
                ).order_by(desc(Address.roi_percentage)).limit(limit).all()
        except Exception as e:
            logger.error(f"Failed to get smart money addresses: {e}")
            raise
    
    def get_whale_addresses(self, limit: int = 100) -> List[Address]:
        """获取巨鲸地址列表"""
        try:
            with self.db_manager.get_session() as session:
                return session.query(Address).filter(
                    Address.is_whale == True
                ).order_by(desc(Address.total_volume_usd)).limit(limit).all()
        except Exception as e:
            logger.error(f"Failed to get whale addresses: {e}")
            raise
    
    def update_address_stats(self, address: str, stats: Dict[str, Any]) -> Optional[Address]:
        """更新地址统计信息"""
        try:
            with self.db_manager.get_session() as session:
                addr_record = session.query(Address).filter(
                    Address.address == address.lower()
                ).first()
                
                if not addr_record:
                    # 创建新记录
                    addr_record = Address(address=address.lower(), **stats)
                    session.add(addr_record)
                else:
                    # 更新现有记录
                    for key, value in stats.items():
                        if hasattr(addr_record, key):
                            setattr(addr_record, key, value)
                
                session.flush()
                session.refresh(addr_record)
                return addr_record
        except Exception as e:
            logger.error(f"Failed to update address stats for {address}: {e}")
            raise
    
    def search_addresses(self, query: str, address_type: str = None, 
                        limit: int = 50) -> List[Address]:
        """搜索地址"""
        try:
            with self.db_manager.get_session() as session:
                q = session.query(Address)
                
                # 地址或标签搜索
                q = q.filter(or_(
                    Address.address.ilike(f'%{query}%'),
                    Address.label.ilike(f'%{query}%')
                ))
                
                # 类型过滤
                if address_type:
                    q = q.filter(Address.address_type == address_type)
                
                return q.limit(limit).all()
        except Exception as e:
            logger.error(f"Failed to search addresses with query {query}: {e}")
            raise


class TransactionRepository(BaseRepository):
    """交易仓库"""
    
    def __init__(self):
        super().__init__(Transaction)
    
    def get_by_hash(self, tx_hash: str) -> Optional[Transaction]:
        """根据交易哈希获取记录"""
        try:
            with self.db_manager.get_session() as session:
                return session.query(Transaction).filter(
                    Transaction.tx_hash == tx_hash
                ).first()
        except Exception as e:
            logger.error(f"Failed to get transaction {tx_hash}: {e}")
            raise
    
    def get_address_transactions(self, address: str, limit: int = 100, 
                               offset: int = 0) -> List[Transaction]:
        """获取地址的交易记录"""
        try:
            with self.db_manager.get_session() as session:
                return session.query(Transaction).filter(or_(
                    Transaction.from_address == address.lower(),
                    Transaction.to_address == address.lower()
                )).order_by(desc(Transaction.block_timestamp)).offset(offset).limit(limit).all()
        except Exception as e:
            logger.error(f"Failed to get transactions for address {address}: {e}")
            raise
    
    def get_large_transactions(self, min_value_usd: float = 100000, 
                             hours: int = 24, limit: int = 100) -> List[Transaction]:
        """获取大额交易"""
        try:
            with self.db_manager.get_session() as session:
                since = datetime.now(timezone.utc) - timedelta(hours=hours)
                return session.query(Transaction).filter(
                    and_(
                        Transaction.value_usd >= min_value_usd,
                        Transaction.block_timestamp >= since
                    )
                ).order_by(desc(Transaction.value_usd)).limit(limit).all()
        except Exception as e:
            logger.error(f"Failed to get large transactions: {e}")
            raise
    
    def get_whale_transactions(self, hours: int = 24, limit: int = 100) -> List[Transaction]:
        """获取巨鲸交易"""
        try:
            with self.db_manager.get_session() as session:
                since = datetime.now(timezone.utc) - timedelta(hours=hours)
                return session.query(Transaction).filter(
                    and_(
                        Transaction.is_whale_tx == True,
                        Transaction.block_timestamp >= since
                    )
                ).order_by(desc(Transaction.block_timestamp)).limit(limit).all()
        except Exception as e:
            logger.error(f"Failed to get whale transactions: {e}")
            raise
    
    def get_smart_money_transactions(self, hours: int = 24, limit: int = 100) -> List[Transaction]:
        """获取Smart Money交易"""
        try:
            with self.db_manager.get_session() as session:
                since = datetime.now(timezone.utc) - timedelta(hours=hours)
                return session.query(Transaction).filter(
                    and_(
                        Transaction.is_smart_money_tx == True,
                        Transaction.block_timestamp >= since
                    )
                ).order_by(desc(Transaction.block_timestamp)).limit(limit).all()
        except Exception as e:
            logger.error(f"Failed to get smart money transactions: {e}")
            raise
    
    def get_transaction_stats(self, hours: int = 24) -> Dict[str, Any]:
        """获取交易统计信息"""
        try:
            with self.db_manager.get_session() as session:
                since = datetime.now(timezone.utc) - timedelta(hours=hours)
                
                # 基本统计
                total_count = session.query(func.count(Transaction.id)).filter(
                    Transaction.block_timestamp >= since
                ).scalar()
                
                total_volume = session.query(func.sum(Transaction.value_usd)).filter(
                    Transaction.block_timestamp >= since
                ).scalar() or 0
                
                avg_value = session.query(func.avg(Transaction.value_usd)).filter(
                    Transaction.block_timestamp >= since
                ).scalar() or 0
                
                # 巨鲸交易统计
                whale_count = session.query(func.count(Transaction.id)).filter(
                    and_(
                        Transaction.is_whale_tx == True,
                        Transaction.block_timestamp >= since
                    )
                ).scalar()
                
                # Smart Money交易统计
                smart_money_count = session.query(func.count(Transaction.id)).filter(
                    and_(
                        Transaction.is_smart_money_tx == True,
                        Transaction.block_timestamp >= since
                    )
                ).scalar()
                
                return {
                    'total_transactions': total_count,
                    'total_volume_usd': total_volume,
                    'average_value_usd': avg_value,
                    'whale_transactions': whale_count,
                    'smart_money_transactions': smart_money_count,
                    'period_hours': hours,
                    'timestamp': datetime.now(timezone.utc)
                }
        except Exception as e:
            logger.error(f"Failed to get transaction stats: {e}")
            raise


class TokenRepository(BaseRepository):
    """代币仓库"""
    
    def __init__(self):
        super().__init__(Token)
    
    def get_by_contract_address(self, contract_address: str) -> Optional[Token]:
        """根据合约地址获取代币"""
        try:
            with self.db_manager.get_session() as session:
                return session.query(Token).filter(
                    Token.contract_address == contract_address.lower()
                ).first()
        except Exception as e:
            logger.error(f"Failed to get token {contract_address}: {e}")
            raise
    
    def get_by_symbol(self, symbol: str) -> List[Token]:
        """根据符号获取代币"""
        try:
            with self.db_manager.get_session() as session:
                return session.query(Token).filter(
                    Token.symbol.ilike(f'%{symbol}%')
                ).all()
        except Exception as e:
            logger.error(f"Failed to get tokens by symbol {symbol}: {e}")
            raise
    
    def get_top_tokens_by_market_cap(self, limit: int = 100) -> List[Token]:
        """按市值获取顶级代币"""
        try:
            with self.db_manager.get_session() as session:
                return session.query(Token).filter(
                    Token.market_cap_usd.isnot(None)
                ).order_by(desc(Token.market_cap_usd)).limit(limit).all()
        except Exception as e:
            logger.error(f"Failed to get top tokens by market cap: {e}")
            raise
    
    def update_token_price(self, contract_address: str, price_data: Dict[str, Any]) -> Optional[Token]:
        """更新代币价格信息"""
        try:
            with self.db_manager.get_session() as session:
                token = session.query(Token).filter(
                    Token.contract_address == contract_address.lower()
                ).first()
                
                if token:
                    for key, value in price_data.items():
                        if hasattr(token, key):
                            setattr(token, key, value)
                    
                    session.flush()
                    session.refresh(token)
                    return token
                return None
        except Exception as e:
            logger.error(f"Failed to update token price for {contract_address}: {e}")
            raise


class MarketDataRepository(BaseRepository):
    """市场数据仓库"""
    
    def __init__(self):
        super().__init__(MarketData)
    
    def get_latest_price(self, symbol: str) -> Optional[MarketData]:
        """获取最新价格"""
        try:
            with self.db_manager.get_session() as session:
                return session.query(MarketData).filter(
                    MarketData.symbol == symbol.upper()
                ).order_by(desc(MarketData.timestamp)).first()
        except Exception as e:
            logger.error(f"Failed to get latest price for {symbol}: {e}")
            raise
    
    def get_price_history(self, symbol: str, days: int = 30) -> List[MarketData]:
        """获取价格历史"""
        try:
            with self.db_manager.get_session() as session:
                since = datetime.now(timezone.utc) - timedelta(days=days)
                return session.query(MarketData).filter(
                    and_(
                        MarketData.symbol == symbol.upper(),
                        MarketData.timestamp >= since
                    )
                ).order_by(asc(MarketData.timestamp)).all()
        except Exception as e:
            logger.error(f"Failed to get price history for {symbol}: {e}")
            raise
    
    def bulk_insert_market_data(self, market_data_list: List[Dict[str, Any]]) -> int:
        """批量插入市场数据"""
        try:
            with self.db_manager.get_session() as session:
                records = []
                for data in market_data_list:
                    # 检查是否已存在
                    existing = session.query(MarketData).filter(
                        and_(
                            MarketData.symbol == data['symbol'],
                            MarketData.timestamp == data['timestamp']
                        )
                    ).first()
                    
                    if not existing:
                        records.append(MarketData(**data))
                
                if records:
                    session.add_all(records)
                    session.flush()
                
                return len(records)
        except Exception as e:
            logger.error(f"Failed to bulk insert market data: {e}")
            raise


class SmartMoneyActivityRepository(BaseRepository):
    """Smart Money活动仓库"""

    def __init__(self):
        super().__init__(SmartMoneyActivity)

    def get_recent_activities(self, hours: int = 24, limit: int = 100) -> List[SmartMoneyActivity]:
        """获取最近的Smart Money活动"""
        try:
            with self.db_manager.get_session() as session:
                since = datetime.now(timezone.utc) - timedelta(hours=hours)
                return session.query(SmartMoneyActivity).filter(
                    SmartMoneyActivity.created_at >= since
                ).order_by(desc(SmartMoneyActivity.created_at)).limit(limit).all()
        except Exception as e:
            logger.error(f"Failed to get recent smart money activities: {e}")
            raise

    def get_address_activities(self, address: str, limit: int = 100) -> List[SmartMoneyActivity]:
        """获取特定地址的Smart Money活动"""
        try:
            with self.db_manager.get_session() as session:
                return session.query(SmartMoneyActivity).filter(
                    SmartMoneyActivity.address == address.lower()
                ).order_by(desc(SmartMoneyActivity.created_at)).limit(limit).all()
        except Exception as e:
            logger.error(f"Failed to get activities for address {address}: {e}")
            raise

    def get_token_activities(self, token_address: str, action_type: str = None,
                           limit: int = 100) -> List[SmartMoneyActivity]:
        """获取特定代币的Smart Money活动"""
        try:
            with self.db_manager.get_session() as session:
                q = session.query(SmartMoneyActivity).filter(
                    SmartMoneyActivity.token_address == token_address.lower()
                )

                if action_type:
                    q = q.filter(SmartMoneyActivity.action_type == action_type)

                return q.order_by(desc(SmartMoneyActivity.created_at)).limit(limit).all()
        except Exception as e:
            logger.error(f"Failed to get token activities for {token_address}: {e}")
            raise

    def get_profitable_activities(self, min_pnl_percentage: float = 10.0,
                                limit: int = 100) -> List[SmartMoneyActivity]:
        """获取盈利的Smart Money活动"""
        try:
            with self.db_manager.get_session() as session:
                return session.query(SmartMoneyActivity).filter(
                    SmartMoneyActivity.pnl_percentage >= min_pnl_percentage
                ).order_by(desc(SmartMoneyActivity.pnl_percentage)).limit(limit).all()
        except Exception as e:
            logger.error(f"Failed to get profitable activities: {e}")
            raise


class WhaleAlertRepository(BaseRepository):
    """巨鲸预警仓库"""

    def __init__(self):
        super().__init__(WhaleAlert)

    def get_recent_alerts(self, hours: int = 24, alert_level: str = None,
                         limit: int = 100) -> List[WhaleAlert]:
        """获取最近的巨鲸预警"""
        try:
            with self.db_manager.get_session() as session:
                since = datetime.now(timezone.utc) - timedelta(hours=hours)
                q = session.query(WhaleAlert).filter(
                    WhaleAlert.created_at >= since
                )

                if alert_level:
                    q = q.filter(WhaleAlert.alert_level == alert_level)

                return q.order_by(desc(WhaleAlert.created_at)).limit(limit).all()
        except Exception as e:
            logger.error(f"Failed to get recent whale alerts: {e}")
            raise

    def get_unprocessed_alerts(self, limit: int = 100) -> List[WhaleAlert]:
        """获取未处理的预警"""
        try:
            with self.db_manager.get_session() as session:
                return session.query(WhaleAlert).filter(
                    WhaleAlert.is_processed == False
                ).order_by(desc(WhaleAlert.created_at)).limit(limit).all()
        except Exception as e:
            logger.error(f"Failed to get unprocessed alerts: {e}")
            raise

    def mark_alert_processed(self, alert_id: int, notification_sent: bool = False) -> Optional[WhaleAlert]:
        """标记预警为已处理"""
        try:
            with self.db_manager.get_session() as session:
                alert = session.query(WhaleAlert).filter(
                    WhaleAlert.id == alert_id
                ).first()

                if alert:
                    alert.is_processed = True
                    alert.notification_sent = notification_sent
                    session.flush()
                    session.refresh(alert)
                    return alert
                return None
        except Exception as e:
            logger.error(f"Failed to mark alert {alert_id} as processed: {e}")
            raise
