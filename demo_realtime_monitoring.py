"""
实时监控和预警系统演示脚本
演示实时监控、异常检测和通知功能
"""
import asyncio
import sys
from pathlib import Path
from datetime import datetime, timezone

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.monitoring.realtime_monitor import RealtimeMonitor, MonitoringEvent
from src.monitoring.anomaly_detector import AnomalyDetector
from src.monitoring.notification_system import NotificationSystem, NotificationRequest
from src.utils.logger import get_logger

logger = get_logger(__name__)


async def demo_realtime_monitoring():
    """演示实时监控功能"""
    print("\n=== 实时监控演示 ===")
    
    try:
        # 注意：由于依赖外部服务，这里使用模拟数据
        print("--- 实时监控系统 ---")
        
        # 创建监控器（使用模拟模式）
        monitor = RealtimeMonitor()
        
        print(f"监控的代币: {', '.join(monitor.monitored_symbols)}")
        print(f"监控配置: {monitor.config}")
        
        # 模拟一些监控事件
        print(f"\n--- 模拟监控事件 ---")
        
        # 价格变化事件
        price_event = MonitoringEvent(
            event_type='price_change',
            symbol='BTC',
            timestamp=datetime.now(timezone.utc),
            data={
                'previous_price': 50000,
                'current_price': 52500,
                'change_percentage': 5.0,
                'change_direction': 'up'
            },
            severity='medium',
            source='price_monitor'
        )
        
        await monitor._emit_event(price_event)
        print(f"✅ 价格变化事件: BTC 上涨 5.0%")
        
        # 巨鲸交易事件
        whale_event = MonitoringEvent(
            event_type='whale_transaction',
            symbol='ETH',
            timestamp=datetime.now(timezone.utc),
            data={
                'tx_hash': '0xabc123...',
                'from_address': '0x123...',
                'to_address': '0x456...',
                'value_usd': 5000000,
                'transaction_type': 'exchange_withdrawal',
                'alert_level': 'high',
                'impact_score': 0.8
            },
            severity='high',
            source='whale_monitor'
        )
        
        await monitor._emit_event(whale_event)
        print(f"✅ 巨鲸交易事件: ETH 500万美元交易所提币")
        
        # Smart Money活动事件
        smart_money_event = MonitoringEvent(
            event_type='smart_money_activity',
            symbol='ADA',
            timestamp=datetime.now(timezone.utc),
            data={
                'direction': 'strong_buy',
                'impact_score': 0.75,
                'total_volume_usd': 2000000,
                'unique_addresses': 8,
                'buy_ratio': 0.85
            },
            severity='medium',
            source='smart_money_monitor'
        )
        
        await monitor._emit_event(smart_money_event)
        print(f"✅ Smart Money事件: ADA 强烈买入信号")
        
        # 技术信号事件
        technical_event = MonitoringEvent(
            event_type='technical_signal',
            symbol='DOT',
            timestamp=datetime.now(timezone.utc),
            data={
                'composite_signal': 'bullish',
                'composite_strength': 0.82,
                'confidence': 0.88,
                'recommendation': 'buy',
                'technical_signal': 'bullish',
                'onchain_signal': 'bullish'
            },
            severity='medium',
            source='technical_monitor'
        )
        
        await monitor._emit_event(technical_event)
        print(f"✅ 技术信号事件: DOT 看涨信号")
        
        # 显示事件队列状态
        print(f"\n--- 事件队列状态 ---")
        print(f"队列中事件数: {len(monitor.event_queue)}")
        
        for i, event in enumerate(monitor.event_queue, 1):
            alert_message = monitor._create_alert_message(event)
            print(f"{i}. {alert_message}")
        
        # 获取监控状态
        status = monitor.get_monitoring_status()
        print(f"\n--- 监控状态 ---")
        print(f"运行状态: {'运行中' if status['is_running'] else '已停止'}")
        print(f"监控代币数: {len(status['monitored_symbols'])}")
        print(f"事件队列大小: {status['event_queue_size']}")
        print(f"WebSocket连接数: {status['websocket_connections']}")
    
    except Exception as e:
        print(f"实时监控演示失败: {e}")


async def demo_anomaly_detection():
    """演示异常检测功能"""
    print("\n=== 异常检测演示 ===")
    
    try:
        detector = AnomalyDetector()
        
        print("--- 异常检测器配置 ---")
        print(f"检测阈值:")
        for key, value in detector.thresholds.items():
            print(f"  {key}: {value}")
        
        print(f"\n异常模式数量: {len(detector.anomaly_patterns)}")
        
        # 显示异常模式
        print(f"\n--- 异常模式库 ---")
        for i, pattern in enumerate(detector.anomaly_patterns, 1):
            print(f"{i}. {pattern.pattern_id}")
            print(f"   类型: {pattern.pattern_type}")
            print(f"   描述: {pattern.description}")
            print(f"   历史发生次数: {pattern.historical_occurrences}")
            print(f"   平均影响: {pattern.avg_impact:.2f}")
        
        # 模拟异常检测
        print(f"\n--- 模拟异常检测 ---")
        
        symbols = ['BTC', 'ETH']
        
        for symbol in symbols:
            try:
                print(f"\n{symbol} 异常检测:")
                
                # 由于需要真实数据，这里模拟检测结果
                anomalies = await detector.detect_anomalies(symbol, hours=24)
                
                if anomalies:
                    print(f"  检测到 {len(anomalies)} 个异常:")
                    
                    for j, anomaly in enumerate(anomalies[:3], 1):  # 显示前3个
                        print(f"    {j}. {anomaly.anomaly_type}")
                        print(f"       严重程度: {anomaly.severity}")
                        print(f"       置信度: {anomaly.confidence:.3f}")
                        print(f"       描述: {anomaly.description}")
                        print(f"       建议行动: {', '.join(anomaly.suggested_actions[:2])}")
                else:
                    print(f"  未检测到异常")
                
                # 分析异常趋势
                trends = await detector.analyze_anomaly_trends(symbol, days=7)
                if not trends.get('no_data'):
                    print(f"  异常趋势分析:")
                    print(f"    总异常数: {trends.get('total_anomalies', 0)}")
                    print(f"    趋势: {trends.get('trend', 'stable')}")
                    print(f"    最近7天: {trends.get('recent_count', 0)} 个")
                    print(f"    平均置信度: {trends.get('avg_confidence', 0):.3f}")
                    
                    most_common = trends.get('most_common_type')
                    if most_common:
                        print(f"    最常见类型: {most_common}")
            
            except Exception as e:
                print(f"  {symbol} 异常检测失败: {e}")
        
        # 演示特定异常检测方法
        print(f"\n--- 特定异常检测方法演示 ---")
        
        # 模拟数据异常检测
        test_data = {
            'symbol': 'BTC',
            'price': 50000,
            'volume': 1000000,
            'timestamp': datetime.now(timezone.utc)
        }
        
        is_anomalous = detector._is_data_anomalous if hasattr(detector, '_is_data_anomalous') else lambda s, d: False
        print(f"正常数据检测: {'异常' if is_anomalous('BTC', test_data) else '正常'}")
        
        # 异常数据
        abnormal_data = {
            'symbol': 'BTC',
            'price': -100,  # 异常价格
            'volume': 1000000,
            'timestamp': datetime.now(timezone.utc)
        }
        
        print(f"异常数据检测: {'异常' if is_anomalous('BTC', abnormal_data) else '正常'}")
    
    except Exception as e:
        print(f"异常检测演示失败: {e}")


async def demo_notification_system():
    """演示通知系统功能"""
    print("\n=== 通知系统演示 ===")
    
    try:
        notification_system = NotificationSystem()
        
        print("--- 通知系统配置 ---")
        print(f"可用通知模板: {len(notification_system.templates)}")
        
        for template_id, template in notification_system.templates.items():
            print(f"  {template_id}: {template.name}")
            print(f"    渠道: {', '.join(template.channels)}")
            print(f"    优先级: {template.priority}")
        
        print(f"\n可用通知渠道: {', '.join(notification_system.channel_handlers.keys())}")
        
        # 演示模板渲染
        print(f"\n--- 模板渲染演示 ---")
        
        template = "🚨 {symbol} 价格{direction} {change_percentage:.2f}%\n当前价格: ${current_price:,.2f}"
        data = {
            'symbol': 'BTC',
            'direction': '上涨',
            'change_percentage': 5.25,
            'current_price': 52625.50
        }
        
        rendered = notification_system._render_template(template, data)
        print(f"渲染结果:")
        print(f"  {rendered}")
        
        # 演示列表数据渲染
        list_template = "建议行动:\n{actions}"
        list_data = {'actions': ['检查新闻', '监控走势', '调整仓位']}
        
        rendered_list = notification_system._render_template(list_template, list_data)
        print(f"\n列表渲染结果:")
        print(f"  {rendered_list}")
        
        # 演示发送通知（模拟模式）
        print(f"\n--- 通知发送演示 ---")
        
        # 价格预警
        print("1. 价格预警通知:")
        price_results = await notification_system.send_price_alert(
            'BTC', 52000, 4.0, '<EMAIL>'
        )
        
        for result in price_results:
            status = '✅ 成功' if result.success else '❌ 失败'
            print(f"   {result.channel}: {status}")
            if result.error:
                print(f"     错误: {result.error}")
        
        # 巨鲸预警
        print("\n2. 巨鲸预警通知:")
        whale_data = {
            'transaction_type': 'exchange_withdrawal',
            'value_usd': 5000000,
            'from_address': '0x123456789abcdef',
            'to_address': '0xfedcba987654321',
            'alert_level': 'high',
            'tx_hash': '0xabcdef123456789'
        }
        
        whale_results = await notification_system.send_whale_alert(
            'ETH', whale_data, '<EMAIL>'
        )
        
        for result in whale_results:
            status = '✅ 成功' if result.success else '❌ 失败'
            print(f"   {result.channel}: {status}")
        
        # 系统预警
        print("\n3. 系统预警通知:")
        system_results = await notification_system.send_system_alert(
            'Database', 'Connection Error', 'Failed to connect to database', '<EMAIL>'
        )
        
        for result in system_results:
            status = '✅ 成功' if result.success else '❌ 失败'
            print(f"   {result.channel}: {status}")
        
        # 自定义通知
        print("\n4. 自定义通知:")
        custom_request = NotificationRequest(
            template_id='technical_signal',
            recipient='<EMAIL>',
            data={
                'symbol': 'ADA',
                'composite_signal': 'bullish',
                'composite_strength': 0.85,
                'confidence': 0.92,
                'recommendation': 'strong_buy',
                'technical_signal': 'bullish',
                'onchain_signal': 'bullish'
            },
            channels=['telegram'],
            priority='medium'
        )
        
        custom_results = await notification_system.send_notification(custom_request)
        
        for result in custom_results:
            status = '✅ 成功' if result.success else '❌ 失败'
            print(f"   {result.channel}: {status}")
        
        # 通知统计
        print(f"\n--- 通知统计 ---")
        stats = notification_system.get_notification_stats(hours=24)
        
        if not stats.get('no_data'):
            print(f"总通知数: {stats.get('total_notifications', 0)}")
            print(f"成功发送: {stats.get('successful_notifications', 0)}")
            print(f"发送失败: {stats.get('failed_notifications', 0)}")
            print(f"成功率: {stats.get('success_rate', 0):.2%}")
            
            channel_stats = stats.get('channel_stats', {})
            if channel_stats:
                print(f"渠道统计:")
                for channel, channel_stat in channel_stats.items():
                    print(f"  {channel}: {channel_stat['success']}/{channel_stat['total']}")
    
    except Exception as e:
        print(f"通知系统演示失败: {e}")


async def demo_integrated_monitoring():
    """演示集成监控场景"""
    print("\n=== 集成监控场景演示 ===")
    
    try:
        print("--- 完整监控流程 ---")
        
        # 1. 实时监控检测到异常
        print("1. 实时监控检测异常...")
        monitor = RealtimeMonitor()
        
        # 模拟检测到价格异常
        price_anomaly_event = MonitoringEvent(
            event_type='price_change',
            symbol='BTC',
            timestamp=datetime.now(timezone.utc),
            data={
                'previous_price': 50000,
                'current_price': 45000,
                'change_percentage': -10.0,
                'change_direction': 'down'
            },
            severity='high',
            source='price_monitor'
        )
        
        await monitor._emit_event(price_anomaly_event)
        print("   ✅ 检测到BTC价格下跌10%")
        
        # 2. 异常检测器分析
        print("\n2. 异常检测器深度分析...")
        detector = AnomalyDetector()
        
        # 模拟异常分析结果
        print("   ✅ 确认为闪崩异常，置信度: 0.85")
        print("   ✅ 建议行动: 检查新闻、监控支撑位、评估止损")
        
        # 3. 发送多渠道预警
        print("\n3. 发送多渠道预警...")
        notification_system = NotificationSystem()
        
        # 发送价格预警
        results = await notification_system.send_price_alert(
            'BTC', 45000, -10.0, '<EMAIL>'
        )
        
        print("   预警发送结果:")
        for result in results:
            status = '✅ 成功' if result.success else '❌ 失败'
            print(f"     {result.channel}: {status}")
        
        # 4. 监控系统健康检查
        print("\n4. 系统健康检查...")
        
        # 模拟健康检查
        health_status = {
            'overall_healthy': True,
            'components': {
                'database': {'status': 'ok'},
                'api': {'status': 'ok'},
                'memory': {'usage_percent': 65, 'status': 'ok'}
            }
        }
        
        if health_status['overall_healthy']:
            print("   ✅ 系统健康状态良好")
        else:
            print("   ❌ 系统健康状态异常")
        
        for component, status in health_status['components'].items():
            print(f"     {component}: {status['status']}")
        
        # 5. 生成监控报告
        print("\n5. 生成监控报告...")
        
        report = {
            'monitoring_period': '过去1小时',
            'total_events': len(monitor.event_queue),
            'high_severity_events': len([e for e in monitor.event_queue if e.severity == 'high']),
            'notifications_sent': len(results),
            'system_health': 'healthy' if health_status['overall_healthy'] else 'unhealthy'
        }
        
        print(f"   监控周期: {report['monitoring_period']}")
        print(f"   总事件数: {report['total_events']}")
        print(f"   高严重性事件: {report['high_severity_events']}")
        print(f"   发送通知数: {report['notifications_sent']}")
        print(f"   系统健康: {report['system_health']}")
        
        print("\n✅ 集成监控流程演示完成")
    
    except Exception as e:
        print(f"集成监控演示失败: {e}")


async def main():
    """主演示函数"""
    print("🔔 实时监控和预警系统演示")
    print("=" * 50)
    
    # 检查配置
    try:
        from config.settings import MONITORING_CONFIG, NOTIFICATION_CONFIG
        print("✅ 配置文件加载成功")
        
        # 检查监控配置
        print(f"监控配置:")
        print(f"  巨鲸阈值: ${MONITORING_CONFIG.get('whale_threshold', {}).get('usdt', 'N/A'):,}")
        print(f"  WebSocket: {'启用' if MONITORING_CONFIG.get('websocket_enabled') else '禁用'}")
        
        # 检查通知配置
        print(f"通知配置:")
        for channel in ['email', 'telegram', 'discord']:
            enabled = NOTIFICATION_CONFIG.get(channel, {}).get('enabled', False)
            print(f"  {channel}: {'启用' if enabled else '禁用'}")
    
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        print("某些功能可能无法正常工作")
    
    # 运行演示
    demos = [
        ("实时监控", demo_realtime_monitoring),
        ("异常检测", demo_anomaly_detection),
        ("通知系统", demo_notification_system),
        ("集成监控场景", demo_integrated_monitoring),
    ]
    
    for demo_name, demo_func in demos:
        try:
            print(f"\n🔄 开始 {demo_name} 演示...")
            await demo_func()
            print(f"✅ {demo_name} 演示完成")
        except Exception as e:
            print(f"❌ {demo_name} 演示失败: {e}")
            logger.error(f"{demo_name} demo failed", exc_info=True)
        
        # 在演示之间添加延迟
        await asyncio.sleep(1)
    
    print("\n🎉 所有演示完成!")
    print("=" * 50)
    print("\n💡 提示:")
    print("- 实时监控需要稳定的网络连接和API访问")
    print("- 异常检测的准确性依赖于历史数据的质量")
    print("- 通知系统需要正确配置各种通知渠道")
    print("- 建议根据实际需求调整监控阈值和预警规则")
    print("- 定期检查系统健康状态以确保监控的可靠性")


if __name__ == "__main__":
    # 运行演示
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 演示被用户中断")
    except Exception as e:
        print(f"\n💥 演示过程中发生错误: {e}")
        logger.error("Demo failed", exc_info=True)
