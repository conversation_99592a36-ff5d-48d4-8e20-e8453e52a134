"""
通知系统
支持多种通知渠道的统一通知管理
"""
import asyncio
import json
import smtplib
import aiohttp
from typing import Dict, List, Optional, Any
from datetime import datetime, timezone
from dataclasses import dataclass
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

from src.utils.logger import get_logger
from config.settings import NOTIFICATION_CONFIG

logger = get_logger(__name__)


@dataclass
class NotificationTemplate:
    """通知模板"""
    template_id: str
    name: str
    subject_template: str
    content_template: str
    channels: List[str]
    priority: str


@dataclass
class NotificationRequest:
    """通知请求"""
    template_id: str
    recipient: str
    data: Dict[str, Any]
    channels: List[str]
    priority: str = 'medium'
    scheduled_time: Optional[datetime] = None


@dataclass
class NotificationResult:
    """通知结果"""
    request_id: str
    channel: str
    success: bool
    message: str
    sent_at: datetime
    error: Optional[str] = None


class NotificationSystem:
    """通知系统"""
    
    def __init__(self):
        self.config = NOTIFICATION_CONFIG
        self.templates = self._load_templates()
        self.notification_history = []
        
        # 通知渠道处理器
        self.channel_handlers = {
            'email': self._send_email,
            'telegram': self._send_telegram,
            'discord': self._send_discord,
            'webhook': self._send_webhook,
            'sms': self._send_sms
        }
    
    def _load_templates(self) -> Dict[str, NotificationTemplate]:
        """加载通知模板"""
        templates = {
            'price_alert': NotificationTemplate(
                template_id='price_alert',
                name='价格预警',
                subject_template='🚨 {symbol} 价格预警',
                content_template='''
{symbol} 价格{direction} {change_percentage:.2f}%

当前价格: ${current_price:,.2f}
{time_info}

{additional_info}
                '''.strip(),
                channels=['telegram', 'email'],
                priority='high'
            ),
            
            'whale_alert': NotificationTemplate(
                template_id='whale_alert',
                name='巨鲸预警',
                subject_template='🐋 {symbol} 巨鲸活动',
                content_template='''
检测到 {symbol} 巨鲸活动

交易类型: {transaction_type}
金额: ${amount:,.0f}
从: {from_address}
到: {to_address}
预警级别: {alert_level}

交易哈希: {tx_hash}
                '''.strip(),
                channels=['telegram', 'discord'],
                priority='high'
            ),
            
            'smart_money_alert': NotificationTemplate(
                template_id='smart_money_alert',
                name='Smart Money预警',
                subject_template='🧠 {symbol} Smart Money活动',
                content_template='''
{symbol} Smart Money {direction}

影响分数: {impact_score:.2f}
交易量: ${total_volume:,.0f}
参与地址: {unique_addresses}个
买入比例: {buy_ratio:.1%}

建议关注后续走势
                '''.strip(),
                channels=['telegram'],
                priority='medium'
            ),
            
            'technical_signal': NotificationTemplate(
                template_id='technical_signal',
                name='技术信号',
                subject_template='📊 {symbol} 技术信号',
                content_template='''
{symbol} 技术分析信号

综合信号: {composite_signal}
信号强度: {composite_strength:.2f}
置信度: {confidence:.2f}
建议: {recommendation}

技术信号: {technical_signal}
链上信号: {onchain_signal}
                '''.strip(),
                channels=['telegram'],
                priority='medium'
            ),
            
            'system_alert': NotificationTemplate(
                template_id='system_alert',
                name='系统预警',
                subject_template='⚠️ 系统预警',
                content_template='''
系统状态异常

组件: {component}
状态: {status}
错误信息: {error_message}

请及时检查系统状态
                '''.strip(),
                channels=['email', 'telegram'],
                priority='critical'
            ),
            
            'anomaly_alert': NotificationTemplate(
                template_id='anomaly_alert',
                name='异常检测预警',
                subject_template='🔍 {symbol} 异常检测',
                content_template='''
检测到 {symbol} 异常活动

异常类型: {anomaly_type}
严重程度: {severity}
置信度: {confidence:.2f}
描述: {description}

建议行动:
{suggested_actions}
                '''.strip(),
                channels=['telegram', 'email'],
                priority='high'
            )
        }
        
        return templates
    
    async def send_notification(self, request: NotificationRequest) -> List[NotificationResult]:
        """发送通知"""
        try:
            logger.info(f"Sending notification: {request.template_id} to {request.recipient}")
            
            # 获取模板
            template = self.templates.get(request.template_id)
            if not template:
                raise ValueError(f"Template not found: {request.template_id}")
            
            # 渲染内容
            subject = self._render_template(template.subject_template, request.data)
            content = self._render_template(template.content_template, request.data)
            
            # 确定发送渠道
            channels = request.channels or template.channels
            
            # 并行发送到各个渠道
            tasks = []
            for channel in channels:
                if channel in self.channel_handlers and self._is_channel_enabled(channel):
                    task = self._send_to_channel(
                        channel, request.recipient, subject, content, request
                    )
                    tasks.append(task)
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理结果
            notification_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    notification_results.append(NotificationResult(
                        request_id=f"{request.template_id}_{int(datetime.now().timestamp())}",
                        channel=channels[i],
                        success=False,
                        message="Failed to send",
                        sent_at=datetime.now(timezone.utc),
                        error=str(result)
                    ))
                else:
                    notification_results.append(result)
            
            # 记录通知历史
            self.notification_history.extend(notification_results)
            
            return notification_results
        
        except Exception as e:
            logger.error(f"Failed to send notification: {e}")
            return [NotificationResult(
                request_id=f"error_{int(datetime.now().timestamp())}",
                channel='system',
                success=False,
                message="System error",
                sent_at=datetime.now(timezone.utc),
                error=str(e)
            )]
    
    def _render_template(self, template: str, data: Dict[str, Any]) -> str:
        """渲染模板"""
        try:
            # 处理列表类型的数据
            processed_data = {}
            for key, value in data.items():
                if isinstance(value, list):
                    processed_data[key] = '\n'.join(f"• {item}" for item in value)
                else:
                    processed_data[key] = value
            
            return template.format(**processed_data)
        
        except Exception as e:
            logger.error(f"Failed to render template: {e}")
            return template
    
    def _is_channel_enabled(self, channel: str) -> bool:
        """检查渠道是否启用"""
        return self.config.get(channel, {}).get('enabled', False)
    
    async def _send_to_channel(self, channel: str, recipient: str, subject: str, 
                             content: str, request: NotificationRequest) -> NotificationResult:
        """发送到指定渠道"""
        try:
            handler = self.channel_handlers[channel]
            success = await handler(recipient, subject, content, request.data)
            
            return NotificationResult(
                request_id=f"{request.template_id}_{int(datetime.now().timestamp())}",
                channel=channel,
                success=success,
                message="Sent successfully" if success else "Failed to send",
                sent_at=datetime.now(timezone.utc)
            )
        
        except Exception as e:
            logger.error(f"Failed to send to {channel}: {e}")
            return NotificationResult(
                request_id=f"{request.template_id}_{int(datetime.now().timestamp())}",
                channel=channel,
                success=False,
                message="Channel error",
                sent_at=datetime.now(timezone.utc),
                error=str(e)
            )
    
    async def _send_email(self, recipient: str, subject: str, content: str, data: Dict[str, Any]) -> bool:
        """发送邮件"""
        try:
            email_config = self.config.get('email', {})
            
            if not email_config.get('enabled', False):
                return False
            
            # 创建邮件
            msg = MIMEMultipart()
            msg['From'] = email_config['smtp_user']
            msg['To'] = recipient
            msg['Subject'] = subject
            
            # 添加内容
            msg.attach(MIMEText(content, 'plain', 'utf-8'))
            
            # 发送邮件
            with smtplib.SMTP(email_config['smtp_server'], email_config['smtp_port']) as server:
                server.starttls()
                server.login(email_config['smtp_user'], email_config['smtp_password'])
                server.send_message(msg)
            
            logger.info(f"Email sent to {recipient}")
            return True
        
        except Exception as e:
            logger.error(f"Failed to send email: {e}")
            return False
    
    async def _send_telegram(self, recipient: str, subject: str, content: str, data: Dict[str, Any]) -> bool:
        """发送Telegram消息"""
        try:
            telegram_config = self.config.get('telegram', {})
            
            if not telegram_config.get('enabled', False):
                return False
            
            bot_token = telegram_config['bot_token']
            chat_id = recipient  # recipient应该是chat_id
            
            # 组合消息
            message = f"*{subject}*\n\n{content}"
            
            # 发送消息
            url = f"https://api.telegram.org/bot{bot_token}/sendMessage"
            payload = {
                'chat_id': chat_id,
                'text': message,
                'parse_mode': 'Markdown'
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=payload) as response:
                    if response.status == 200:
                        logger.info(f"Telegram message sent to {chat_id}")
                        return True
                    else:
                        logger.error(f"Telegram API error: {response.status}")
                        return False
        
        except Exception as e:
            logger.error(f"Failed to send Telegram message: {e}")
            return False
    
    async def _send_discord(self, recipient: str, subject: str, content: str, data: Dict[str, Any]) -> bool:
        """发送Discord消息"""
        try:
            discord_config = self.config.get('discord', {})
            
            if not discord_config.get('enabled', False):
                return False
            
            webhook_url = recipient  # recipient应该是webhook URL
            
            # 创建Discord嵌入消息
            embed = {
                'title': subject,
                'description': content,
                'color': 0xff6b6b,  # 红色
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
            
            payload = {
                'embeds': [embed]
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(webhook_url, json=payload) as response:
                    if response.status == 204:
                        logger.info("Discord message sent")
                        return True
                    else:
                        logger.error(f"Discord webhook error: {response.status}")
                        return False
        
        except Exception as e:
            logger.error(f"Failed to send Discord message: {e}")
            return False
    
    async def _send_webhook(self, recipient: str, subject: str, content: str, data: Dict[str, Any]) -> bool:
        """发送Webhook"""
        try:
            webhook_config = self.config.get('webhook', {})
            
            if not webhook_config.get('enabled', False):
                return False
            
            webhook_url = recipient  # recipient应该是webhook URL
            
            # 创建payload
            payload = {
                'subject': subject,
                'content': content,
                'data': data,
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(webhook_url, json=payload) as response:
                    if 200 <= response.status < 300:
                        logger.info("Webhook sent successfully")
                        return True
                    else:
                        logger.error(f"Webhook error: {response.status}")
                        return False
        
        except Exception as e:
            logger.error(f"Failed to send webhook: {e}")
            return False
    
    async def _send_sms(self, recipient: str, subject: str, content: str, data: Dict[str, Any]) -> bool:
        """发送短信"""
        try:
            sms_config = self.config.get('sms', {})
            
            if not sms_config.get('enabled', False):
                return False
            
            # 简化短信内容
            sms_content = f"{subject}: {content[:100]}..."
            
            # 这里应该调用实际的短信API
            # 现在只是记录日志
            logger.info(f"SMS would be sent to {recipient}: {sms_content}")
            return True
        
        except Exception as e:
            logger.error(f"Failed to send SMS: {e}")
            return False
    
    async def send_price_alert(self, symbol: str, current_price: float, 
                             change_percentage: float, recipient: str) -> List[NotificationResult]:
        """发送价格预警"""
        direction = '上涨' if change_percentage > 0 else '下跌'
        
        request = NotificationRequest(
            template_id='price_alert',
            recipient=recipient,
            data={
                'symbol': symbol,
                'current_price': current_price,
                'change_percentage': abs(change_percentage),
                'direction': direction,
                'time_info': f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                'additional_info': '请关注市场动态'
            },
            channels=['telegram', 'email'],
            priority='high'
        )
        
        return await self.send_notification(request)
    
    async def send_whale_alert(self, symbol: str, tx_data: Dict[str, Any], recipient: str) -> List[NotificationResult]:
        """发送巨鲸预警"""
        request = NotificationRequest(
            template_id='whale_alert',
            recipient=recipient,
            data={
                'symbol': symbol,
                'transaction_type': tx_data.get('transaction_type', 'Unknown'),
                'amount': tx_data.get('value_usd', 0),
                'from_address': tx_data.get('from_address', '')[:10] + '...',
                'to_address': tx_data.get('to_address', '')[:10] + '...',
                'alert_level': tx_data.get('alert_level', 'medium'),
                'tx_hash': tx_data.get('tx_hash', '')[:10] + '...'
            },
            channels=['telegram', 'discord'],
            priority='high'
        )
        
        return await self.send_notification(request)
    
    async def send_system_alert(self, component: str, status: str, error_message: str, recipient: str) -> List[NotificationResult]:
        """发送系统预警"""
        request = NotificationRequest(
            template_id='system_alert',
            recipient=recipient,
            data={
                'component': component,
                'status': status,
                'error_message': error_message
            },
            channels=['email', 'telegram'],
            priority='critical'
        )
        
        return await self.send_notification(request)
    
    def get_notification_history(self, hours: int = 24) -> List[NotificationResult]:
        """获取通知历史"""
        try:
            cutoff_time = datetime.now(timezone.utc) - timedelta(hours=hours)
            
            return [
                result for result in self.notification_history
                if result.sent_at >= cutoff_time
            ]
        
        except Exception as e:
            logger.error(f"Failed to get notification history: {e}")
            return []
    
    def get_notification_stats(self, hours: int = 24) -> Dict[str, Any]:
        """获取通知统计"""
        try:
            history = self.get_notification_history(hours)
            
            if not history:
                return {'no_data': True}
            
            # 统计分析
            total_notifications = len(history)
            successful_notifications = len([r for r in history if r.success])
            failed_notifications = total_notifications - successful_notifications
            
            # 按渠道统计
            channel_stats = {}
            for result in history:
                channel = result.channel
                if channel not in channel_stats:
                    channel_stats[channel] = {'total': 0, 'success': 0, 'failed': 0}
                
                channel_stats[channel]['total'] += 1
                if result.success:
                    channel_stats[channel]['success'] += 1
                else:
                    channel_stats[channel]['failed'] += 1
            
            # 成功率
            success_rate = successful_notifications / total_notifications if total_notifications > 0 else 0
            
            return {
                'total_notifications': total_notifications,
                'successful_notifications': successful_notifications,
                'failed_notifications': failed_notifications,
                'success_rate': success_rate,
                'channel_stats': channel_stats,
                'time_range_hours': hours
            }
        
        except Exception as e:
            logger.error(f"Failed to get notification stats: {e}")
            return {'error': str(e)}
