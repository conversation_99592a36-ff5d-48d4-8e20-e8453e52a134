"""
实时监控和预警系统测试
"""
import pytest
import asyncio
from datetime import datetime, timezone, timedelta
from unittest.mock import Mock, patch, MagicMock, AsyncMock

from src.monitoring.realtime_monitor import RealtimeMonitor, MonitoringEvent, MonitoringMetrics
from src.monitoring.anomaly_detector import AnomalyDetector, Anomaly, AnomalyPattern
from src.monitoring.notification_system import NotificationSystem, NotificationRequest, NotificationResult


class TestRealtimeMonitor:
    """实时监控器测试"""
    
    @pytest.mark.asyncio
    async def test_monitor_initialization(self):
        """测试监控器初始化"""
        with patch('src.monitoring.realtime_monitor.DataCoordinator'):
            with patch('src.monitoring.realtime_monitor.WhaleMonitor'):
                with patch('src.monitoring.realtime_monitor.IndicatorEngine'):
                    async with RealtimeMonitor() as monitor:
                        assert monitor.data_coordinator is not None
                        assert monitor.whale_monitor is not None
                        assert monitor.alert_system is not None
                        assert monitor.indicator_engine is not None
                        assert not monitor.is_running
    
    @pytest.mark.asyncio
    async def test_emit_event(self):
        """测试事件发出"""
        monitor = RealtimeMonitor()
        
        event = MonitoringEvent(
            event_type='test_event',
            symbol='BTC',
            timestamp=datetime.now(timezone.utc),
            data={'test': 'data'},
            severity='medium',
            source='test'
        )
        
        await monitor._emit_event(event)
        
        assert len(monitor.event_queue) == 1
        assert monitor.event_queue[0].event_type == 'test_event'
    
    @pytest.mark.asyncio
    async def test_get_current_price(self):
        """测试获取当前价格"""
        monitor = RealtimeMonitor()
        
        price = await monitor._get_current_price('BTC')
        
        assert price is not None
        assert isinstance(price, float)
        assert price > 0
    
    def test_is_data_anomalous(self):
        """测试数据异常检测"""
        monitor = RealtimeMonitor()
        
        # 正常数据
        normal_data = {
            'symbol': 'BTC',
            'price': 50000,
            'volume': 1000000,
            'timestamp': datetime.now(timezone.utc)
        }
        assert not monitor._is_data_anomalous('BTC', normal_data)
        
        # 异常价格
        abnormal_price_data = {
            'symbol': 'BTC',
            'price': -100,  # 负价格
            'volume': 1000000,
            'timestamp': datetime.now(timezone.utc)
        }
        assert monitor._is_data_anomalous('BTC', abnormal_price_data)
        
        # 异常成交量
        abnormal_volume_data = {
            'symbol': 'BTC',
            'price': 50000,
            'volume': -1000,  # 负成交量
            'timestamp': datetime.now(timezone.utc)
        }
        assert monitor._is_data_anomalous('BTC', abnormal_volume_data)
    
    def test_create_alert_message(self):
        """测试预警消息创建"""
        monitor = RealtimeMonitor()
        
        # 价格变化事件
        price_event = MonitoringEvent(
            event_type='price_change',
            symbol='BTC',
            timestamp=datetime.now(timezone.utc),
            data={
                'current_price': 52000,
                'change_percentage': 5.0,
                'change_direction': 'up'
            },
            severity='medium',
            source='price_monitor'
        )
        
        message = monitor._create_alert_message(price_event)
        assert 'BTC' in message
        assert '5.00%' in message
        assert 'up' in message
        
        # 巨鲸交易事件
        whale_event = MonitoringEvent(
            event_type='whale_transaction',
            symbol='ETH',
            timestamp=datetime.now(timezone.utc),
            data={
                'value_usd': 5000000,
                'transaction_type': 'large_transfer'
            },
            severity='high',
            source='whale_monitor'
        )
        
        message = monitor._create_alert_message(whale_event)
        assert 'ETH' in message
        assert '5,000,000' in message
        assert 'large_transfer' in message
    
    def test_get_monitoring_status(self):
        """测试获取监控状态"""
        monitor = RealtimeMonitor()
        
        status = monitor.get_monitoring_status()
        
        assert 'is_running' in status
        assert 'monitored_symbols' in status
        assert 'active_monitors' in status
        assert 'event_queue_size' in status
        assert status['is_running'] == False
        assert isinstance(status['monitored_symbols'], list)


class TestAnomalyDetector:
    """异常检测器测试"""
    
    def test_detector_initialization(self):
        """测试检测器初始化"""
        detector = AnomalyDetector()
        
        assert detector.transaction_repo is not None
        assert detector.market_data_repo is not None
        assert len(detector.anomaly_patterns) > 0
        assert 'price_volatility' in detector.thresholds
    
    @pytest.mark.asyncio
    async def test_detect_price_anomalies(self):
        """测试价格异常检测"""
        detector = AnomalyDetector()
        
        # 模拟价格历史数据
        mock_price_history = [
            Mock(timestamp=datetime.now(timezone.utc) - timedelta(hours=i),
                 price_usd=50000 * (1 + 0.01 * i),  # 逐渐上涨
                 volume_24h=1000000)
            for i in range(10)
        ]
        
        # 添加一个闪崩点
        mock_price_history.append(
            Mock(timestamp=datetime.now(timezone.utc),
                 price_usd=40000,  # 20%下跌
                 volume_24h=5000000)
        )
        
        detector.price_history['BTC'] = mock_price_history
        
        anomalies = await detector._detect_price_anomalies('BTC')
        
        # 应该检测到闪崩异常
        flash_crashes = [a for a in anomalies if a.anomaly_type == 'flash_crash']
        assert len(flash_crashes) > 0
        assert flash_crashes[0].severity == 'high'
    
    @pytest.mark.asyncio
    async def test_detect_volume_anomalies(self):
        """测试成交量异常检测"""
        detector = AnomalyDetector()
        
        # 模拟正常成交量历史
        normal_volumes = [1000000] * 10
        
        # 模拟成交量激增
        spike_volume = Mock(
            timestamp=datetime.now(timezone.utc),
            price_usd=50000,
            volume_24h=5000000  # 5倍激增
        )
        
        mock_price_history = [
            Mock(timestamp=datetime.now(timezone.utc) - timedelta(hours=i),
                 price_usd=50000,
                 volume_24h=normal_volumes[i])
            for i in range(10)
        ]
        mock_price_history.append(spike_volume)
        
        detector.price_history['BTC'] = mock_price_history
        
        anomalies = await detector._detect_volume_anomalies('BTC')
        
        # 应该检测到成交量异常
        volume_spikes = [a for a in anomalies if a.anomaly_type == 'volume_spike']
        assert len(volume_spikes) > 0
        assert volume_spikes[0].data['volume_ratio'] > 3.0
    
    def test_get_severity_score(self):
        """测试严重程度分数"""
        detector = AnomalyDetector()
        
        assert detector._get_severity_score('critical') == 4
        assert detector._get_severity_score('high') == 3
        assert detector._get_severity_score('medium') == 2
        assert detector._get_severity_score('low') == 1
        assert detector._get_severity_score('unknown') == 0
    
    @pytest.mark.asyncio
    async def test_match_pattern(self):
        """测试模式匹配"""
        detector = AnomalyDetector()
        
        # 创建闪崩模式
        flash_crash_pattern = AnomalyPattern(
            pattern_id='flash_crash',
            pattern_type='price_anomaly',
            description='闪崩模式',
            detection_rules={'price_drop_threshold': 0.15},
            historical_occurrences=5,
            avg_impact=0.8
        )
        
        # 模拟价格下跌数据
        detector.price_history['BTC'] = [
            Mock(price_usd=50000),
            Mock(price_usd=40000)  # 20%下跌
        ]
        
        match_result = await detector._match_pattern('BTC', flash_crash_pattern)
        assert match_result == True


class TestNotificationSystem:
    """通知系统测试"""
    
    def test_system_initialization(self):
        """测试系统初始化"""
        notification_system = NotificationSystem()
        
        assert len(notification_system.templates) > 0
        assert 'price_alert' in notification_system.templates
        assert 'whale_alert' in notification_system.templates
        assert len(notification_system.channel_handlers) > 0
    
    def test_render_template(self):
        """测试模板渲染"""
        notification_system = NotificationSystem()
        
        template = "Price of {symbol} is ${price:,.2f}"
        data = {'symbol': 'BTC', 'price': 50000.123}
        
        rendered = notification_system._render_template(template, data)
        
        assert rendered == "Price of BTC is $50,000.12"
    
    def test_render_template_with_list(self):
        """测试包含列表的模板渲染"""
        notification_system = NotificationSystem()
        
        template = "Actions:\n{actions}"
        data = {'actions': ['Buy', 'Hold', 'Sell']}
        
        rendered = notification_system._render_template(template, data)
        
        assert '• Buy' in rendered
        assert '• Hold' in rendered
        assert '• Sell' in rendered
    
    @pytest.mark.asyncio
    async def test_send_email(self):
        """测试邮件发送"""
        notification_system = NotificationSystem()
        
        # 模拟邮件配置
        notification_system.config['email'] = {
            'enabled': False  # 禁用实际发送
        }
        
        result = await notification_system._send_email(
            '<EMAIL>',
            'Test Subject',
            'Test Content',
            {}
        )
        
        # 由于禁用了邮件，应该返回False
        assert result == False
    
    @pytest.mark.asyncio
    async def test_send_telegram(self):
        """测试Telegram发送"""
        notification_system = NotificationSystem()
        
        # 模拟Telegram配置
        notification_system.config['telegram'] = {
            'enabled': False  # 禁用实际发送
        }
        
        result = await notification_system._send_telegram(
            '123456789',
            'Test Subject',
            'Test Content',
            {}
        )
        
        # 由于禁用了Telegram，应该返回False
        assert result == False
    
    @pytest.mark.asyncio
    async def test_send_price_alert(self):
        """测试价格预警发送"""
        notification_system = NotificationSystem()
        
        # 禁用所有渠道
        notification_system.config = {
            'email': {'enabled': False},
            'telegram': {'enabled': False}
        }
        
        results = await notification_system.send_price_alert(
            'BTC', 50000, 5.0, '<EMAIL>'
        )
        
        assert isinstance(results, list)
        # 由于所有渠道都禁用，结果应该为空或包含失败结果
        if results:
            assert all(not result.success for result in results)
    
    def test_get_notification_history(self):
        """测试获取通知历史"""
        notification_system = NotificationSystem()
        
        # 添加一些历史记录
        notification_system.notification_history = [
            NotificationResult(
                request_id='test1',
                channel='email',
                success=True,
                message='Sent',
                sent_at=datetime.now(timezone.utc)
            ),
            NotificationResult(
                request_id='test2',
                channel='telegram',
                success=False,
                message='Failed',
                sent_at=datetime.now(timezone.utc) - timedelta(hours=25)  # 超过24小时
            )
        ]
        
        history = notification_system.get_notification_history(hours=24)
        
        # 应该只返回24小时内的记录
        assert len(history) == 1
        assert history[0].request_id == 'test1'
    
    def test_get_notification_stats(self):
        """测试获取通知统计"""
        notification_system = NotificationSystem()
        
        # 添加一些历史记录
        notification_system.notification_history = [
            NotificationResult(
                request_id='test1',
                channel='email',
                success=True,
                message='Sent',
                sent_at=datetime.now(timezone.utc)
            ),
            NotificationResult(
                request_id='test2',
                channel='email',
                success=False,
                message='Failed',
                sent_at=datetime.now(timezone.utc)
            ),
            NotificationResult(
                request_id='test3',
                channel='telegram',
                success=True,
                message='Sent',
                sent_at=datetime.now(timezone.utc)
            )
        ]
        
        stats = notification_system.get_notification_stats(hours=24)
        
        assert stats['total_notifications'] == 3
        assert stats['successful_notifications'] == 2
        assert stats['failed_notifications'] == 1
        assert stats['success_rate'] == 2/3
        assert 'channel_stats' in stats
        assert stats['channel_stats']['email']['total'] == 2
        assert stats['channel_stats']['telegram']['total'] == 1


class TestIntegration:
    """集成测试"""
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_full_monitoring_workflow(self):
        """测试完整监控工作流程"""
        # 这个测试需要真实的数据库连接和API，通常在开发环境中跳过
        pytest.skip("Integration test requires real database and API connections")
        
        # 1. 启动实时监控
        async with RealtimeMonitor() as monitor:
            # 模拟运行一小段时间
            await asyncio.sleep(1)
            
            # 检查监控状态
            status = monitor.get_monitoring_status()
            assert 'is_running' in status
        
        # 2. 异常检测
        detector = AnomalyDetector()
        anomalies = await detector.detect_anomalies('BTC', hours=1)
        assert isinstance(anomalies, list)
        
        # 3. 发送通知
        notification_system = NotificationSystem()
        if anomalies:
            # 发送异常预警
            request = NotificationRequest(
                template_id='anomaly_alert',
                recipient='<EMAIL>',
                data={
                    'symbol': 'BTC',
                    'anomaly_type': anomalies[0].anomaly_type,
                    'severity': anomalies[0].severity,
                    'confidence': anomalies[0].confidence,
                    'description': anomalies[0].description,
                    'suggested_actions': anomalies[0].suggested_actions
                },
                channels=['email']
            )
            
            results = await notification_system.send_notification(request)
            assert isinstance(results, list)


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v", "-m", "not integration"])
