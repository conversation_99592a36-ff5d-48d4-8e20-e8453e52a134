"""
仪表板API路由
提供市场概览、关键指标和实时数据
"""
import asyncio
from fastapi import APIRouter, HTTPException, Query
from typing import Dict, List, Optional, Any
from datetime import datetime, timezone, timedelta
import random

from src.database.repositories import MarketDataRepository, TransactionRepository
from src.analysis.smart_money_signals import SmartMoneySignalGenerator
from src.monitoring.whale_monitor import WhaleMonitor
from src.indicators.technical_indicators import TechnicalIndicatorCalculator
from src.utils.logger import get_logger

logger = get_logger(__name__)

router = APIRouter()

# 初始化组件
market_data_repo = MarketDataRepository()
transaction_repo = TransactionRepository()
smart_money_generator = SmartMoneySignalGenerator()
whale_monitor = WhaleMonitor()
technical_calculator = TechnicalIndicatorCalculator()


@router.get("/overview")
async def get_dashboard_overview():
    """获取仪表板概览数据"""
    try:
        # 获取主要加密货币价格
        symbols = ['BTC', 'ETH', 'ADA', 'DOT']
        prices = {}
        price_changes = {}
        
        for symbol in symbols:
            try:
                # 获取最新价格数据
                price_history = market_data_repo.get_price_history(symbol, days=2)
                
                if price_history and len(price_history) >= 2:
                    current_price = price_history[-1].price_usd
                    previous_price = price_history[-2].price_usd
                    
                    prices[symbol] = current_price
                    price_changes[symbol] = (current_price - previous_price) / previous_price
                else:
                    # 模拟数据
                    base_prices = {'BTC': 50000, 'ETH': 3000, 'ADA': 1.0, 'DOT': 10.0}
                    prices[symbol] = base_prices[symbol] + random.uniform(-base_prices[symbol]*0.05, base_prices[symbol]*0.05)
                    price_changes[symbol] = random.uniform(-0.05, 0.05)
            
            except Exception as e:
                logger.error(f"Failed to get price for {symbol}: {e}")
                # 使用默认值
                base_prices = {'BTC': 50000, 'ETH': 3000, 'ADA': 1.0, 'DOT': 10.0}
                prices[symbol] = base_prices[symbol]
                price_changes[symbol] = 0
        
        # 获取巨鲸活动统计
        try:
            whale_activity = await whale_monitor.get_recent_activity_count(hours=24)
        except Exception as e:
            logger.error(f"Failed to get whale activity: {e}")
            whale_activity = random.randint(10, 30)
        
        # 获取Smart Money信号统计
        try:
            smart_money_signals = len(await smart_money_generator.generate_signals(['BTC', 'ETH']))
        except Exception as e:
            logger.error(f"Failed to get smart money signals: {e}")
            smart_money_signals = random.randint(5, 15)
        
        # 计算市场总市值
        total_market_cap = sum(
            prices[symbol] * {'BTC': 19000000, 'ETH': 120000000, 'ADA': 35000000000, 'DOT': 1200000000}[symbol]
            for symbol in symbols
        )
        
        return {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'prices': prices,
            'price_changes': price_changes,
            'market_stats': {
                'total_market_cap': total_market_cap,
                'btc_dominance': (prices['BTC'] * 19000000) / total_market_cap,
                'fear_greed_index': random.randint(20, 80),
                'active_addresses': random.randint(800000, 1200000)
            },
            'activity_stats': {
                'whale_activity': whale_activity,
                'smart_money_signals': smart_money_signals,
                'technical_signals': random.randint(3, 12),
                'risk_alerts': random.randint(0, 5)
            }
        }
    
    except Exception as e:
        logger.error(f"Failed to get dashboard overview: {e}")
        raise HTTPException(status_code=500, detail="Failed to get dashboard overview")


@router.get("/market-metrics")
async def get_market_metrics(
    symbols: List[str] = Query(default=['BTC', 'ETH']),
    timeframe: str = Query(default='24h')
):
    """获取市场指标"""
    try:
        metrics = {}
        
        for symbol in symbols:
            try:
                # 获取价格历史
                days = {'1h': 1, '24h': 1, '7d': 7, '30d': 30}.get(timeframe, 1)
                price_history = market_data_repo.get_price_history(symbol, days=days)
                
                if price_history:
                    prices = [p.price_usd for p in price_history]
                    volumes = [p.volume_24h or 0 for p in price_history]
                    
                    # 计算基本指标
                    current_price = prices[-1]
                    price_change = (prices[-1] - prices[0]) / prices[0] if len(prices) > 1 else 0
                    avg_volume = sum(volumes) / len(volumes) if volumes else 0
                    
                    # 计算波动率
                    if len(prices) > 1:
                        returns = [(prices[i] - prices[i-1]) / prices[i-1] for i in range(1, len(prices))]
                        volatility = (sum(r**2 for r in returns) / len(returns)) ** 0.5
                    else:
                        volatility = 0
                    
                    metrics[symbol] = {
                        'current_price': current_price,
                        'price_change': price_change,
                        'price_change_pct': price_change * 100,
                        'volume_24h': volumes[-1] if volumes else 0,
                        'avg_volume': avg_volume,
                        'volatility': volatility,
                        'high_24h': max(prices) if prices else current_price,
                        'low_24h': min(prices) if prices else current_price,
                        'market_cap': current_price * {'BTC': 19000000, 'ETH': 120000000, 'ADA': 35000000000, 'DOT': 1200000000}.get(symbol, 1000000)
                    }
                else:
                    # 模拟数据
                    base_prices = {'BTC': 50000, 'ETH': 3000, 'ADA': 1.0, 'DOT': 10.0}
                    current_price = base_prices.get(symbol, 100)
                    
                    metrics[symbol] = {
                        'current_price': current_price,
                        'price_change': random.uniform(-0.05, 0.05),
                        'price_change_pct': random.uniform(-5, 5),
                        'volume_24h': random.uniform(1000000, 10000000),
                        'avg_volume': random.uniform(1000000, 10000000),
                        'volatility': random.uniform(0.02, 0.08),
                        'high_24h': current_price * 1.05,
                        'low_24h': current_price * 0.95,
                        'market_cap': current_price * 1000000
                    }
            
            except Exception as e:
                logger.error(f"Failed to get metrics for {symbol}: {e}")
                continue
        
        return {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'timeframe': timeframe,
            'metrics': metrics
        }
    
    except Exception as e:
        logger.error(f"Failed to get market metrics: {e}")
        raise HTTPException(status_code=500, detail="Failed to get market metrics")


@router.get("/price-history/{symbol}")
async def get_price_history(
    symbol: str,
    days: int = Query(default=7, ge=1, le=365),
    interval: str = Query(default='1h')
):
    """获取价格历史数据"""
    try:
        # 获取价格历史
        price_history = market_data_repo.get_price_history(symbol, days=days)
        
        if not price_history:
            # 生成模拟数据
            base_price = {'BTC': 50000, 'ETH': 3000, 'ADA': 1.0, 'DOT': 10.0}.get(symbol, 100)
            
            price_data = []
            current_price = base_price
            
            for i in range(days * 24):  # 每小时一个数据点
                timestamp = datetime.now(timezone.utc) - timedelta(hours=days*24-i)
                
                # 模拟价格变化
                change = random.uniform(-0.02, 0.02)
                current_price *= (1 + change)
                
                price_data.append({
                    'timestamp': timestamp.isoformat(),
                    'price': current_price,
                    'volume': random.uniform(1000000, 5000000)
                })
            
            return {
                'symbol': symbol,
                'interval': interval,
                'data': price_data
            }
        
        # 转换真实数据
        price_data = [
            {
                'timestamp': p.timestamp.isoformat(),
                'price': p.price_usd,
                'volume': p.volume_24h or 0
            }
            for p in price_history
        ]
        
        return {
            'symbol': symbol,
            'interval': interval,
            'data': price_data
        }
    
    except Exception as e:
        logger.error(f"Failed to get price history for {symbol}: {e}")
        raise HTTPException(status_code=500, detail="Failed to get price history")


@router.get("/top-movers")
async def get_top_movers(limit: int = Query(default=10, ge=1, le=50)):
    """获取涨跌幅排行"""
    try:
        symbols = ['BTC', 'ETH', 'ADA', 'DOT', 'LINK', 'UNI', 'AAVE', 'COMP', 'MKR', 'SNX']
        movers = []
        
        for symbol in symbols[:limit]:
            try:
                # 获取价格数据
                price_history = market_data_repo.get_price_history(symbol, days=1)
                
                if price_history and len(price_history) >= 2:
                    current_price = price_history[-1].price_usd
                    previous_price = price_history[0].price_usd
                    change_pct = (current_price - previous_price) / previous_price * 100
                    volume = price_history[-1].volume_24h or 0
                else:
                    # 模拟数据
                    base_prices = {'BTC': 50000, 'ETH': 3000, 'ADA': 1.0, 'DOT': 10.0, 'LINK': 15, 'UNI': 8, 'AAVE': 100, 'COMP': 60, 'MKR': 1500, 'SNX': 3}
                    current_price = base_prices.get(symbol, 100)
                    change_pct = random.uniform(-10, 10)
                    volume = random.uniform(1000000, 10000000)
                
                movers.append({
                    'symbol': symbol,
                    'price': current_price,
                    'change_pct': change_pct,
                    'volume_24h': volume
                })
            
            except Exception as e:
                logger.error(f"Failed to get data for {symbol}: {e}")
                continue
        
        # 按涨跌幅排序
        movers.sort(key=lambda x: x['change_pct'], reverse=True)
        
        return {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'gainers': movers[:limit//2],
            'losers': movers[-limit//2:]
        }
    
    except Exception as e:
        logger.error(f"Failed to get top movers: {e}")
        raise HTTPException(status_code=500, detail="Failed to get top movers")


@router.get("/activity-feed")
async def get_activity_feed(limit: int = Query(default=20, ge=1, le=100)):
    """获取活动动态"""
    try:
        activities = []
        
        # 模拟各种活动
        activity_types = [
            'whale_transaction',
            'smart_money_signal',
            'technical_signal',
            'price_alert',
            'volume_spike'
        ]
        
        symbols = ['BTC', 'ETH', 'ADA', 'DOT']
        
        for i in range(limit):
            activity_type = random.choice(activity_types)
            symbol = random.choice(symbols)
            timestamp = datetime.now(timezone.utc) - timedelta(minutes=random.randint(1, 1440))
            
            if activity_type == 'whale_transaction':
                activity = {
                    'id': f"whale_{i}",
                    'type': 'whale_transaction',
                    'symbol': symbol,
                    'timestamp': timestamp.isoformat(),
                    'title': f'{symbol} 巨鲸交易',
                    'description': f'检测到 {symbol} 大额转账 ${random.randint(1000000, 50000000):,}',
                    'severity': random.choice(['medium', 'high']),
                    'data': {
                        'amount_usd': random.randint(1000000, 50000000),
                        'transaction_type': random.choice(['exchange_deposit', 'exchange_withdrawal', 'wallet_transfer'])
                    }
                }
            
            elif activity_type == 'smart_money_signal':
                activity = {
                    'id': f"smart_{i}",
                    'type': 'smart_money_signal',
                    'symbol': symbol,
                    'timestamp': timestamp.isoformat(),
                    'title': f'{symbol} Smart Money信号',
                    'description': f'Smart Money {random.choice(["买入", "卖出"])} {symbol}',
                    'severity': 'medium',
                    'data': {
                        'direction': random.choice(['buy', 'sell']),
                        'confidence': random.uniform(0.6, 0.9),
                        'impact_score': random.uniform(0.5, 0.8)
                    }
                }
            
            elif activity_type == 'technical_signal':
                activity = {
                    'id': f"tech_{i}",
                    'type': 'technical_signal',
                    'symbol': symbol,
                    'timestamp': timestamp.isoformat(),
                    'title': f'{symbol} 技术信号',
                    'description': f'{symbol} {random.choice(["突破阻力位", "跌破支撑位", "金叉", "死叉"])}',
                    'severity': 'low',
                    'data': {
                        'signal_type': random.choice(['breakout', 'breakdown', 'golden_cross', 'death_cross']),
                        'strength': random.uniform(0.5, 1.0)
                    }
                }
            
            elif activity_type == 'price_alert':
                activity = {
                    'id': f"price_{i}",
                    'type': 'price_alert',
                    'symbol': symbol,
                    'timestamp': timestamp.isoformat(),
                    'title': f'{symbol} 价格预警',
                    'description': f'{symbol} 价格{random.choice(["上涨", "下跌"])} {random.uniform(3, 8):.1f}%',
                    'severity': random.choice(['low', 'medium']),
                    'data': {
                        'price_change_pct': random.uniform(-8, 8),
                        'trigger_type': 'percentage_change'
                    }
                }
            
            else:  # volume_spike
                activity = {
                    'id': f"volume_{i}",
                    'type': 'volume_spike',
                    'symbol': symbol,
                    'timestamp': timestamp.isoformat(),
                    'title': f'{symbol} 成交量激增',
                    'description': f'{symbol} 成交量激增 {random.uniform(2, 5):.1f}倍',
                    'severity': 'medium',
                    'data': {
                        'volume_multiplier': random.uniform(2, 5),
                        'current_volume': random.uniform(1000000, 10000000)
                    }
                }
            
            activities.append(activity)
        
        # 按时间排序
        activities.sort(key=lambda x: x['timestamp'], reverse=True)
        
        return {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'activities': activities
        }
    
    except Exception as e:
        logger.error(f"Failed to get activity feed: {e}")
        raise HTTPException(status_code=500, detail="Failed to get activity feed")


@router.get("/portfolio-summary")
async def get_portfolio_summary():
    """获取投资组合摘要"""
    try:
        # 模拟投资组合数据
        portfolio = {
            'total_value': 125000,
            'total_pnl': 25000,
            'total_pnl_pct': 25.0,
            'positions': [
                {
                    'symbol': 'BTC',
                    'quantity': 2.5,
                    'avg_price': 45000,
                    'current_price': 50000,
                    'value': 125000,
                    'pnl': 12500,
                    'pnl_pct': 11.11,
                    'weight': 0.6
                },
                {
                    'symbol': 'ETH',
                    'quantity': 20,
                    'avg_price': 2800,
                    'current_price': 3000,
                    'value': 60000,
                    'pnl': 4000,
                    'pnl_pct': 7.14,
                    'weight': 0.3
                },
                {
                    'symbol': 'ADA',
                    'quantity': 20000,
                    'avg_price': 0.9,
                    'current_price': 1.0,
                    'value': 20000,
                    'pnl': 2000,
                    'pnl_pct': 11.11,
                    'weight': 0.1
                }
            ],
            'performance': {
                'daily_pnl': 1500,
                'daily_pnl_pct': 1.2,
                'weekly_pnl': 8000,
                'weekly_pnl_pct': 6.8,
                'monthly_pnl': 15000,
                'monthly_pnl_pct': 13.6
            }
        }
        
        return {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'portfolio': portfolio
        }
    
    except Exception as e:
        logger.error(f"Failed to get portfolio summary: {e}")
        raise HTTPException(status_code=500, detail="Failed to get portfolio summary")
