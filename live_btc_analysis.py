#!/usr/bin/env python3
"""
实时BTC分析 - 2025年7月26日
获取真实的链上数据和价格进行分析
"""

import urllib.request
import json
import time
from datetime import datetime
import sys

def fetch_url(url, timeout=10):
    """安全的URL获取函数"""
    try:
        with urllib.request.urlopen(url, timeout=timeout) as response:
            return json.loads(response.read().decode())
    except Exception as e:
        print(f"❌ 获取数据失败 {url}: {e}")
        return None

def get_coingecko_data():
    """获取CoinGecko实时数据"""
    print("🌐 正在获取CoinGecko数据...")
    
    url = "https://api.coingecko.com/api/v3/simple/price?ids=bitcoin&vs_currencies=usd&include_24hr_change=true&include_24hr_vol=true&include_market_cap=true&include_last_updated_at=true"
    
    data = fetch_url(url)
    if data and 'bitcoin' in data:
        btc = data['bitcoin']
        print(f"✅ CoinGecko: ${btc['usd']:,.2f}")
        return btc
    return None

def get_binance_data():
    """获取Binance实时数据"""
    print("🟡 正在获取Binance数据...")
    
    url = "https://api.binance.com/api/v3/ticker/24hr?symbol=BTCUSDT"
    
    data = fetch_url(url)
    if data:
        print(f"✅ Binance: ${float(data['lastPrice']):,.2f}")
        return {
            'price': float(data['lastPrice']),
            'change_24h': float(data['priceChangePercent']),
            'volume_24h': float(data['volume']),
            'high_24h': float(data['highPrice']),
            'low_24h': float(data['lowPrice'])
        }
    return None

def get_blockchain_info():
    """获取区块链基础信息"""
    print("⛓️ 正在获取区块链数据...")
    
    url = "https://blockchain.info/stats?format=json"
    
    data = fetch_url(url, timeout=15)
    if data:
        print(f"✅ Blockchain.info: 哈希率 {data['hash_rate']:.2e}")
        return {
            'total_bitcoins': data['totalbc'] / 1e8,
            'market_price_usd': data['market_price_usd'],
            'hash_rate': data['hash_rate'],
            'difficulty': data['difficulty'],
            'minutes_between_blocks': data['minutes_between_blocks'],
            'n_tx': data['n_tx'],
            'total_fees_btc': data['total_fees_btc'] / 1e8
        }
    return None

def get_mempool_data():
    """获取内存池数据"""
    print("📊 正在获取内存池数据...")
    
    url = "https://mempool.space/api/mempool"
    
    data = fetch_url(url)
    if data:
        print(f"✅ Mempool: {data['count']} 笔交易")
        return {
            'count': data['count'],
            'vsize': data['vsize'],
            'total_fee': data['total_fee'],
            'fee_histogram': data.get('fee_histogram', [])
        }
    return None

def analyze_real_btc_data():
    """分析真实BTC数据"""
    print("🚀 OnChain Analytics Platform - 实时BTC分析")
    print("=" * 70)
    print(f"📅 分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🎯 数据源: 真实API数据")
    
    # 收集所有数据
    print(f"\n📡 正在收集实时数据...")
    
    coingecko_data = get_coingecko_data()
    binance_data = get_binance_data()
    blockchain_data = get_blockchain_info()
    mempool_data = get_mempool_data()
    
    # 检查数据获取情况
    successful_sources = 0
    if coingecko_data: successful_sources += 1
    if binance_data: successful_sources += 1
    if blockchain_data: successful_sources += 1
    if mempool_data: successful_sources += 1
    
    print(f"\n📊 数据收集结果: {successful_sources}/4 个数据源成功")
    
    if successful_sources == 0:
        print("❌ 无法获取任何实时数据，请检查网络连接")
        return None
    
    # 价格分析
    print(f"\n💰 实时价格分析")
    print("-" * 40)
    
    prices = []
    if coingecko_data:
        prices.append(('CoinGecko', coingecko_data['usd']))
        print(f"CoinGecko价格: ${coingecko_data['usd']:,.2f}")
        print(f"24h变化: {coingecko_data['usd_24h_change']:+.2f}%")
        print(f"24h成交量: ${coingecko_data['usd_24h_vol']:,.0f}")
        print(f"市值: ${coingecko_data['usd_market_cap']:,.0f}")
    
    if binance_data:
        prices.append(('Binance', binance_data['price']))
        print(f"Binance价格: ${binance_data['price']:,.2f}")
        print(f"24h变化: {binance_data['change_24h']:+.2f}%")
        print(f"24h最高: ${binance_data['high_24h']:,.2f}")
        print(f"24h最低: ${binance_data['low_24h']:,.2f}")
    
    # 价格一致性检查
    if len(prices) >= 2:
        price_values = [p[1] for p in prices]
        avg_price = sum(price_values) / len(price_values)
        max_diff = max(abs(p - avg_price) / avg_price for p in price_values) * 100
        
        print(f"\n🔍 价格一致性分析:")
        for source, price in prices:
            diff = (price - avg_price) / avg_price * 100
            print(f"  {source}: ${price:,.2f} ({diff:+.3f}%)")
        
        print(f"  平均价格: ${avg_price:,.2f}")
        print(f"  最大差异: {max_diff:.3f}%")
        
        if max_diff < 0.5:
            print("✅ 价格数据一致性优秀")
        elif max_diff < 1.0:
            print("🟡 价格数据一致性良好")
        else:
            print("⚠️ 价格数据存在较大差异")
    
    # 区块链网络分析
    if blockchain_data:
        print(f"\n⛓️ 区块链网络分析")
        print("-" * 40)
        print(f"BTC总供应量: {blockchain_data['total_bitcoins']:,.2f}")
        print(f"网络哈希率: {blockchain_data['hash_rate']:.2e} H/s")
        print(f"挖矿难度: {blockchain_data['difficulty']:,.0f}")
        print(f"平均出块时间: {blockchain_data['minutes_between_blocks']:.1f} 分钟")
        print(f"24h交易数: {blockchain_data['n_tx']:,}")
        print(f"24h总手续费: {blockchain_data['total_fees_btc']:.2f} BTC")
        
        # 网络健康度评估
        if blockchain_data['hash_rate'] > 500e18:
            security_level = "🛡️ 极高安全性"
        elif blockchain_data['hash_rate'] > 300e18:
            security_level = "🔒 高安全性"
        else:
            security_level = "⚠️ 中等安全性"
        
        print(f"网络安全等级: {security_level}")
        
        if blockchain_data['minutes_between_blocks'] < 12:
            block_time_status = "🟢 出块正常"
        elif blockchain_data['minutes_between_blocks'] < 15:
            block_time_status = "🟡 出块略慢"
        else:
            block_time_status = "🔴 出块较慢"
        
        print(f"出块时间状态: {block_time_status}")
    
    # 内存池分析
    if mempool_data:
        print(f"\n📊 内存池分析")
        print("-" * 40)
        print(f"待确认交易: {mempool_data['count']:,}")
        print(f"内存池大小: {mempool_data['vsize'] / 1e6:.1f} MB")
        print(f"总手续费: {mempool_data['total_fee'] / 1e8:.4f} BTC")
        
        if mempool_data['count'] < 50000:
            congestion = "🟢 网络畅通"
        elif mempool_data['count'] < 100000:
            congestion = "🟡 轻度拥堵"
        else:
            congestion = "🔴 网络拥堵"
        
        print(f"网络拥堵状态: {congestion}")
    
    # 综合分析
    print(f"\n💡 综合分析结论")
    print("-" * 40)
    
    # 价格趋势
    if coingecko_data:
        change_24h = coingecko_data['usd_24h_change']
        if change_24h > 5:
            price_trend = "🚀 强势上涨"
        elif change_24h > 2:
            price_trend = "📈 温和上涨"
        elif change_24h > -2:
            price_trend = "➡️ 横盘整理"
        elif change_24h > -5:
            price_trend = "📉 温和下跌"
        else:
            price_trend = "🔻 大幅下跌"
        
        print(f"价格趋势: {price_trend}")
    
    # 网络状态
    if blockchain_data and mempool_data:
        if (blockchain_data['hash_rate'] > 400e18 and 
            mempool_data['count'] < 80000 and 
            blockchain_data['minutes_between_blocks'] < 12):
            network_health = "🟢 网络健康"
        else:
            network_health = "🟡 网络正常"
        
        print(f"网络健康度: {network_health}")
    
    # 数据质量评估
    data_quality_score = successful_sources / 4 * 100
    print(f"数据完整性: {data_quality_score:.0f}% ({successful_sources}/4 数据源)")
    
    # 生成投资建议
    print(f"\n🎯 基于实时数据的市场观察")
    print("-" * 40)
    
    if coingecko_data:
        current_price = coingecko_data['usd']
        print(f"当前BTC价格: ${current_price:,.2f}")
        
        if successful_sources >= 3:
            confidence = "高"
        elif successful_sources >= 2:
            confidence = "中"
        else:
            confidence = "低"
        
        print(f"数据可信度: {confidence}")
        print(f"分析时间: {datetime.now().strftime('%H:%M:%S')}")
    
    print(f"\n⚠️ 重要提醒:")
    print(f"• 本分析基于实时公开数据")
    print(f"• 加密货币投资存在高风险")
    print(f"• 请结合多方信息谨慎决策")
    print(f"• 数据仅供参考，不构成投资建议")
    
    return {
        'coingecko': coingecko_data,
        'binance': binance_data,
        'blockchain': blockchain_data,
        'mempool': mempool_data,
        'analysis_time': datetime.now().isoformat(),
        'data_sources': successful_sources
    }

def main():
    """主函数"""
    try:
        print("🔥 启动实时BTC数据分析...")
        result = analyze_real_btc_data()
        
        if result:
            print(f"\n✅ 实时分析完成!")
            print(f"📊 成功获取 {result['data_sources']} 个数据源")
            print(f"🕐 分析耗时: 约 {time.time():.0f} 秒")
        else:
            print(f"\n❌ 分析失败，请检查网络连接")
            
    except KeyboardInterrupt:
        print(f"\n👋 用户中断分析")
    except Exception as e:
        print(f"\n💥 分析过程中发生错误: {e}")

if __name__ == "__main__":
    main()
