"""
简单数据测试 - 验证基础API连接
"""
import requests
import json
from datetime import datetime

def test_coingecko():
    """测试CoinGecko API"""
    print("🌐 测试CoinGecko API...")
    
    try:
        url = "https://api.coingecko.com/api/v3/simple/price"
        params = {
            'ids': 'bitcoin',
            'vs_currencies': 'usd',
            'include_24hr_change': 'true',
            'include_24hr_vol': 'true'
        }
        
        response = requests.get(url, params=params, timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            btc_data = data['bitcoin']
            
            print("✅ CoinGecko连接成功!")
            print(f"💰 BTC价格: ${btc_data['usd']:,.2f}")
            print(f"📈 24h变化: {btc_data['usd_24h_change']:+.2f}%")
            print(f"📊 24h成交量: ${btc_data['usd_24h_vol']:,.0f}")
            
            return btc_data
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"响应内容: {response.text[:200]}")
            return None
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
        return None
    except requests.exceptions.ConnectionError:
        print("❌ 网络连接错误")
        return None
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return None

def test_binance():
    """测试Binance API"""
    print("\n🟡 测试Binance API...")
    
    try:
        url = "https://api.binance.com/api/v3/ticker/24hr"
        params = {'symbol': 'BTCUSDT'}
        
        response = requests.get(url, params=params, timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            print("✅ Binance连接成功!")
            print(f"💰 BTC价格: ${float(data['lastPrice']):,.2f}")
            print(f"📈 24h变化: {float(data['priceChangePercent']):+.2f}%")
            print(f"📊 24h成交量: {float(data['volume']):,.2f} BTC")
            
            return data
        else:
            print(f"❌ 请求失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 错误: {e}")
        return None

def test_blockchain_info():
    """测试Blockchain.info API"""
    print("\n⛓️ 测试Blockchain.info API...")
    
    try:
        url = "https://blockchain.info/stats?format=json"
        
        response = requests.get(url, timeout=15)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            print("✅ Blockchain.info连接成功!")
            print(f"🪙 BTC总供应量: {data['totalbc'] / 1e8:,.2f}")
            print(f"💰 市场价格: ${data['market_price_usd']:,.2f}")
            print(f"⚡ 网络哈希率: {data['hash_rate']:.2e} H/s")
            print(f"🎯 挖矿难度: {data['difficulty']:,.0f}")
            
            return data
        else:
            print(f"❌ 请求失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 错误: {e}")
        return None

def main():
    """主函数"""
    print("🚀 OnChain Analytics Platform - 数据源连接测试")
    print("=" * 60)
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试各个API
    results = {}
    
    # CoinGecko
    cg_data = test_coingecko()
    if cg_data:
        results['coingecko'] = cg_data['usd']
    
    # Binance
    binance_data = test_binance()
    if binance_data:
        results['binance'] = float(binance_data['lastPrice'])
    
    # Blockchain.info
    blockchain_data = test_blockchain_info()
    if blockchain_data:
        results['blockchain'] = blockchain_data['market_price_usd']
    
    # 总结
    print(f"\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    
    if results:
        print(f"✅ 成功连接 {len(results)} 个数据源")
        
        prices = list(results.values())
        avg_price = sum(prices) / len(prices)
        
        print(f"\n💰 BTC价格对比:")
        for source, price in results.items():
            diff = (price - avg_price) / avg_price * 100
            print(f"  {source.capitalize()}: ${price:,.2f} ({diff:+.2f}%)")
        
        print(f"\n📈 平均价格: ${avg_price:,.2f}")
        
        # 检查价格一致性
        max_diff = max(abs(p - avg_price) / avg_price for p in prices) * 100
        if max_diff < 1:
            print("✅ 价格数据一致性良好")
        else:
            print(f"⚠️ 价格差异: {max_diff:.2f}%")
        
        print(f"\n🎉 真实数据获取成功!")
        print(f"📡 数据源状态: 正常")
        print(f"🔗 网络连接: 正常")
        
        return True
    else:
        print("❌ 所有数据源连接失败")
        print("🔧 请检查网络连接")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print(f"\n✅ 数据获取能力验证完成 - 系统可以获取真实数据!")
    else:
        print(f"\n❌ 数据获取失败 - 需要检查网络环境")
