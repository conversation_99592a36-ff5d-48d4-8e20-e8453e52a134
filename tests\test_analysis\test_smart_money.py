"""
Smart Money分析模块测试
"""
import pytest
import asyncio
from datetime import datetime, timezone, timedelta
from unittest.mock import Mock, patch, MagicMock

from src.analysis.smart_money import SmartMoneyAnalyzer, SmartMoneyMetrics, TokenActivity
from src.analysis.smart_money_signals import SmartMoneySignalGenerator, SmartMoneySignal
from src.analysis.smart_money_strategy import SmartMoneyFollowStrategy, FollowStrategy, FollowRule


class TestSmartMoneyAnalyzer:
    """Smart Money分析器测试"""
    
    @pytest.mark.asyncio
    async def test_analyzer_initialization(self):
        """测试分析器初始化"""
        with patch('src.analysis.smart_money.DataService'):
            async with SmartMoneyAnalyzer() as analyzer:
                assert analyzer.address_repo is not None
                assert analyzer.transaction_repo is not None
                assert analyzer.smart_money_repo is not None
    
    @pytest.mark.asyncio
    async def test_identify_smart_money_addresses(self):
        """测试Smart Money地址识别"""
        with patch('src.analysis.smart_money.DataService'):
            analyzer = SmartMoneyAnalyzer()
            
            # 模拟活跃地址
            with patch.object(analyzer, '_get_active_addresses', return_value=['0x123...', '0x456...']):
                # 模拟地址性能分析
                mock_metrics = SmartMoneyMetrics(
                    address='0x123...',
                    total_transactions=100,
                    total_volume_usd=1000000,
                    win_rate=0.75,
                    avg_holding_period_hours=48,
                    roi_percentage=300.0,
                    sharpe_ratio=2.5,
                    max_drawdown=0.15,
                    profit_factor=3.0,
                    active_days=180,
                    unique_tokens=15,
                    avg_transaction_size=10000,
                    success_score=0.85
                )
                
                with patch.object(analyzer, 'analyze_address_performance', return_value=mock_metrics):
                    with patch.object(analyzer, '_update_smart_money_status'):
                        smart_money_list = await analyzer.identify_smart_money_addresses()
                        
                        assert len(smart_money_list) >= 0
                        if smart_money_list:
                            assert smart_money_list[0].success_score >= 0.7
    
    def test_calculate_profitability_metrics(self):
        """测试盈利指标计算"""
        analyzer = SmartMoneyAnalyzer()
        
        # 创建模拟代币活动
        token_activities = [
            TokenActivity(
                token_address='0xabc...',
                token_symbol='TOKEN1',
                buy_transactions=[],
                sell_transactions=[],
                current_position=100,
                realized_pnl=1000,
                unrealized_pnl=500,
                holding_period_hours=24,
                entry_price=10.0,
                current_price=15.0
            ),
            TokenActivity(
                token_address='0xdef...',
                token_symbol='TOKEN2',
                buy_transactions=[],
                sell_transactions=[],
                current_position=50,
                realized_pnl=-200,
                unrealized_pnl=100,
                holding_period_hours=48,
                entry_price=20.0,
                current_price=18.0
            )
        ]
        
        win_rate, roi_percentage, profit_factor = analyzer._calculate_profitability_metrics(token_activities)
        
        assert 0 <= win_rate <= 1
        assert isinstance(roi_percentage, float)
        assert profit_factor >= 0
    
    def test_calculate_success_score(self):
        """测试成功分数计算"""
        analyzer = SmartMoneyAnalyzer()
        
        success_score = analyzer._calculate_success_score(
            win_rate=0.75,
            roi_percentage=300.0,
            sharpe_ratio=2.5,
            max_drawdown=0.15,
            profit_factor=3.0,
            unique_tokens=15,
            active_days=180
        )
        
        assert 0 <= success_score <= 1
        assert success_score > 0.5  # 应该是一个好的分数


class TestSmartMoneySignalGenerator:
    """Smart Money信号生成器测试"""
    
    @pytest.mark.asyncio
    async def test_signal_generator_initialization(self):
        """测试信号生成器初始化"""
        with patch('src.analysis.smart_money_signals.SmartMoneyAnalyzer'):
            async with SmartMoneySignalGenerator() as generator:
                assert generator.smart_money_repo is not None
                assert generator.signal_repo is not None
    
    @pytest.mark.asyncio
    async def test_generate_signals(self):
        """测试信号生成"""
        with patch('src.analysis.smart_money_signals.SmartMoneyAnalyzer'):
            generator = SmartMoneySignalGenerator()
            
            # 模拟Smart Money活动
            mock_activities = [
                Mock(
                    action_type='buy',
                    token_symbol='BTC',
                    usd_amount=50000,
                    address='0x123...',
                    confidence_score=0.8,
                    created_at=datetime.now(timezone.utc)
                ),
                Mock(
                    action_type='buy',
                    token_symbol='BTC',
                    usd_amount=30000,
                    address='0x456...',
                    confidence_score=0.9,
                    created_at=datetime.now(timezone.utc)
                )
            ]
            
            with patch.object(generator.smart_money_repo, 'get_recent_activities', return_value=mock_activities):
                with patch.object(generator, '_store_signal'):
                    signals = await generator.generate_signals(hours=24)
                    
                    assert isinstance(signals, list)
                    # 如果有信号生成，检查信号格式
                    if signals:
                        signal = signals[0]
                        assert hasattr(signal, 'symbol')
                        assert hasattr(signal, 'action')
                        assert hasattr(signal, 'strength')
                        assert hasattr(signal, 'confidence')
    
    def test_group_activities_by_token(self):
        """测试按代币分组活动"""
        generator = SmartMoneySignalGenerator()
        
        mock_activities = [
            Mock(token_symbol='BTC', action_type='buy'),
            Mock(token_symbol='BTC', action_type='sell'),
            Mock(token_symbol='ETH', action_type='buy')
        ]
        
        grouped = generator._group_activities_by_token(mock_activities)
        
        assert 'BTC' in grouped
        assert 'ETH' in grouped
        assert len(grouped['BTC']) == 2
        assert len(grouped['ETH']) == 1
    
    def test_analyze_time_pattern(self):
        """测试时间模式分析"""
        generator = SmartMoneySignalGenerator()
        
        now = datetime.now(timezone.utc)
        mock_activities = [
            Mock(created_at=now, action_type='buy'),
            Mock(created_at=now - timedelta(hours=1), action_type='buy'),
            Mock(created_at=now - timedelta(hours=2), action_type='sell')
        ]
        
        time_pattern = generator._analyze_time_pattern(mock_activities)
        
        assert 'concentration' in time_pattern
        assert 'trend' in time_pattern
        assert 'activity_span_hours' in time_pattern
        assert time_pattern['activity_span_hours'] >= 0


class TestSmartMoneyFollowStrategy:
    """Smart Money跟随策略测试"""
    
    @pytest.mark.asyncio
    async def test_strategy_initialization(self):
        """测试策略初始化"""
        with patch('src.analysis.smart_money_strategy.SmartMoneySignalGenerator'):
            async with SmartMoneyFollowStrategy() as strategy:
                assert strategy.strategy_type == FollowStrategy.CONSENSUS
                assert strategy.follow_rules is not None
                assert strategy.active_positions == []
    
    @pytest.mark.asyncio
    async def test_execute_strategy(self):
        """测试策略执行"""
        with patch('src.analysis.smart_money_strategy.SmartMoneySignalGenerator'):
            strategy = SmartMoneyFollowStrategy()
            
            # 模拟信号
            mock_signal = SmartMoneySignal(
                signal_type='smart_money_follow',
                symbol='BTC',
                action='buy',
                strength=0.8,
                confidence=0.9,
                price_target=50000,
                stop_loss=45000,
                time_horizon='medium',
                source_addresses=['0x123...', '0x456...', '0x789...'],
                supporting_data={
                    'buy_volume_usd': 200000,
                    'sell_volume_usd': 50000,
                    'unique_addresses': 3
                },
                generated_at=datetime.now(timezone.utc)
            )
            
            with patch.object(strategy.signal_generator, 'generate_signals', return_value=[mock_signal]):
                with patch.object(strategy, '_get_top_smart_money_addresses', return_value=['0x123...']):
                    with patch.object(strategy, '_execute_follow_decision', return_value=None):
                        results = await strategy.execute_strategy(hours=4)
                        
                        assert isinstance(results, list)
    
    def test_follow_rules(self):
        """测试跟随规则"""
        rules = FollowRule(
            min_smart_money_count=5,
            min_total_volume=50000,
            min_confidence=0.8,
            max_follow_amount=5000
        )
        
        assert rules.min_smart_money_count == 5
        assert rules.min_total_volume == 50000
        assert rules.min_confidence == 0.8
        assert rules.max_follow_amount == 5000
    
    @pytest.mark.asyncio
    async def test_filter_signals_by_strategy(self):
        """测试按策略过滤信号"""
        strategy = SmartMoneyFollowStrategy(strategy_type=FollowStrategy.CONSENSUS)
        
        # 创建测试信号
        good_signal = SmartMoneySignal(
            signal_type='smart_money_follow',
            symbol='BTC',
            action='buy',
            strength=0.8,
            confidence=0.9,
            price_target=None,
            stop_loss=None,
            time_horizon='medium',
            source_addresses=['0x1...', '0x2...', '0x3...', '0x4...', '0x5...'],  # 5个地址
            supporting_data={
                'buy_volume_usd': 200000,
                'sell_volume_usd': 50000
            },
            generated_at=datetime.now(timezone.utc)
        )
        
        bad_signal = SmartMoneySignal(
            signal_type='smart_money_follow',
            symbol='ETH',
            action='buy',
            strength=0.5,
            confidence=0.6,  # 置信度太低
            price_target=None,
            stop_loss=None,
            time_horizon='short',
            source_addresses=['0x1...'],  # 只有1个地址
            supporting_data={
                'buy_volume_usd': 10000,  # 交易量太小
                'sell_volume_usd': 5000
            },
            generated_at=datetime.now(timezone.utc)
        )
        
        filtered = await strategy._filter_signals_by_strategy([good_signal, bad_signal])
        
        # 好信号应该通过过滤，坏信号应该被过滤掉
        assert len(filtered) <= 2
        if filtered:
            assert all(signal.confidence >= strategy.follow_rules.min_confidence for signal in filtered)


class TestIntegration:
    """集成测试"""
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_full_smart_money_workflow(self):
        """测试完整的Smart Money工作流程"""
        # 这个测试需要真实的数据库连接，通常在开发环境中跳过
        pytest.skip("Integration test requires real database connection")
        
        # 1. 识别Smart Money地址
        async with SmartMoneyAnalyzer() as analyzer:
            smart_money_addresses = await analyzer.identify_smart_money_addresses(
                min_transactions=10,
                min_volume_usd=10000,
                min_roi=100.0,
                min_win_rate=0.6
            )
            
            assert isinstance(smart_money_addresses, list)
        
        # 2. 生成交易信号
        async with SmartMoneySignalGenerator() as generator:
            signals = await generator.generate_signals(hours=24)
            assert isinstance(signals, list)
        
        # 3. 执行跟随策略
        async with SmartMoneyFollowStrategy() as strategy:
            results = await strategy.execute_strategy(hours=4)
            assert isinstance(results, list)


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v", "-m", "not integration"])
