"""
Smart Money分析演示脚本
演示Smart Money识别、信号生成和跟随策略功能
"""
import asyncio
import sys
from pathlib import Path
from datetime import datetime, timezone

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.analysis.smart_money import SmartMoneyAnalyzer
from src.analysis.smart_money_signals import SmartMoneySignalGenerator
from src.analysis.smart_money_strategy import SmartMoneyFollowStrategy, FollowStrategy, FollowRule
from src.utils.logger import get_logger

logger = get_logger(__name__)


async def demo_smart_money_identification():
    """演示Smart Money识别"""
    print("\n=== Smart Money识别演示 ===")
    
    try:
        async with SmartMoneyAnalyzer() as analyzer:
            print("--- 识别Smart Money地址 ---")
            
            # 使用较低的门槛进行演示
            smart_money_addresses = await analyzer.identify_smart_money_addresses(
                min_transactions=20,
                min_volume_usd=50000,
                min_roi=150.0,
                min_win_rate=0.55
            )
            
            print(f"识别到 {len(smart_money_addresses)} 个Smart Money地址")
            
            # 显示前5个最优秀的Smart Money地址
            for i, metrics in enumerate(smart_money_addresses[:5], 1):
                print(f"\n{i}. 地址: {metrics.address}")
                print(f"   成功分数: {metrics.success_score:.3f}")
                print(f"   ROI: {metrics.roi_percentage:.2f}%")
                print(f"   胜率: {metrics.win_rate:.2%}")
                print(f"   夏普比率: {metrics.sharpe_ratio:.2f}")
                print(f"   最大回撤: {metrics.max_drawdown:.2%}")
                print(f"   交易数量: {metrics.total_transactions}")
                print(f"   交易量: ${metrics.total_volume_usd:,.0f}")
                print(f"   活跃天数: {metrics.active_days}")
                print(f"   涉及代币数: {metrics.unique_tokens}")
            
            if smart_money_addresses:
                # 分析单个地址的详细表现
                print(f"\n--- 详细分析地址: {smart_money_addresses[0].address} ---")
                detailed_metrics = await analyzer.analyze_address_performance(smart_money_addresses[0].address)
                
                print(f"平均持有期: {detailed_metrics.avg_holding_period_hours:.1f} 小时")
                print(f"平均交易规模: ${detailed_metrics.avg_transaction_size:,.0f}")
                print(f"盈利因子: {detailed_metrics.profit_factor:.2f}")
    
    except Exception as e:
        print(f"Smart Money识别演示失败: {e}")


async def demo_smart_money_tracking():
    """演示Smart Money追踪"""
    print("\n=== Smart Money追踪演示 ===")
    
    try:
        async with SmartMoneyAnalyzer() as analyzer:
            print("--- 追踪Smart Money活动 ---")
            
            # 模拟一些Smart Money地址
            demo_addresses = [
                '0xd8dA6BF26964aF9D7eEd9e03E53415D37aA96045',  # Vitalik
                '0x3f5CE5FBFe3E9af3971dD833D26bA9b5C936f0bE',  # Binance
            ]
            
            activities = await analyzer.track_smart_money_activities(demo_addresses, hours=24)
            
            print(f"过去24小时Smart Money活动: {len(activities)} 个")
            
            # 按代币分组显示活动
            from collections import defaultdict
            token_activities = defaultdict(list)
            for activity in activities:
                token_activities[activity['token_symbol']].append(activity)
            
            for token_symbol, token_acts in list(token_activities.items())[:5]:
                print(f"\n{token_symbol}:")
                buy_count = sum(1 for act in token_acts if act['action_type'] == 'buy')
                sell_count = sum(1 for act in token_acts if act['action_type'] == 'sell')
                total_volume = sum(act['usd_amount'] for act in token_acts)
                
                print(f"  买入: {buy_count} 次")
                print(f"  卖出: {sell_count} 次")
                print(f"  总交易量: ${total_volume:,.0f}")
                print(f"  参与地址: {len(set(act['address'] for act in token_acts))}")
                
                # 显示最大的几笔交易
                sorted_acts = sorted(token_acts, key=lambda x: x['usd_amount'], reverse=True)
                for act in sorted_acts[:2]:
                    print(f"    {act['action_type'].upper()}: ${act['usd_amount']:,.0f} "
                          f"({act['address'][:10]}...)")
    
    except Exception as e:
        print(f"Smart Money追踪演示失败: {e}")


async def demo_signal_generation():
    """演示信号生成"""
    print("\n=== Smart Money信号生成演示 ===")
    
    try:
        async with SmartMoneySignalGenerator() as generator:
            print("--- 生成Smart Money信号 ---")
            
            signals = await generator.generate_signals(hours=24)
            
            print(f"生成 {len(signals)} 个交易信号")
            
            # 显示前5个最强的信号
            for i, signal in enumerate(signals[:5], 1):
                print(f"\n{i}. {signal.symbol} - {signal.action.upper()}")
                print(f"   信号强度: {signal.strength:.3f}")
                print(f"   置信度: {signal.confidence:.3f}")
                print(f"   时间范围: {signal.time_horizon}")
                print(f"   参与地址数: {len(signal.source_addresses)}")
                
                supporting_data = signal.supporting_data
                print(f"   买入量: ${supporting_data.get('buy_volume_usd', 0):,.0f}")
                print(f"   卖出量: ${supporting_data.get('sell_volume_usd', 0):,.0f}")
                print(f"   买入比例: {supporting_data.get('buy_ratio', 0):.2%}")
                
                if signal.price_target:
                    print(f"   目标价格: ${signal.price_target:,.2f}")
                if signal.stop_loss:
                    print(f"   止损价格: ${signal.stop_loss:,.2f}")
            
            # 获取顶级Smart Money动向
            print("\n--- 顶级Smart Money动向 ---")
            top_moves = await generator.get_top_smart_money_moves(hours=24, limit=5)
            
            for i, move in enumerate(top_moves, 1):
                print(f"\n{i}. {move['token_symbol']} - {move['direction'].upper()}")
                print(f"   影响分数: {move['impact_score']:.3f}")
                print(f"   总交易量: ${move['total_volume_usd']:,.0f}")
                print(f"   买入比例: {move['buy_ratio']:.2%}")
                print(f"   参与地址: {move['unique_addresses']} 个")
                print(f"   活动次数: {move['total_activities']}")
    
    except Exception as e:
        print(f"信号生成演示失败: {e}")


async def demo_follow_strategy():
    """演示跟随策略"""
    print("\n=== Smart Money跟随策略演示 ===")
    
    try:
        # 配置跟随规则
        follow_rules = FollowRule(
            min_smart_money_count=2,  # 降低门槛用于演示
            min_total_volume=20000,
            min_confidence=0.6,
            max_follow_amount=5000,
            follow_ratio=0.005,  # 跟随0.5%
            time_window_hours=6,
            stop_loss_pct=0.03,
            take_profit_pct=0.10
        )
        
        # 测试不同的跟随策略
        strategies = [
            (FollowStrategy.IMMEDIATE, "立即跟随"),
            (FollowStrategy.CONSENSUS, "共识跟随"),
            (FollowStrategy.SELECTIVE, "选择性跟随")
        ]
        
        for strategy_type, strategy_name in strategies:
            print(f"\n--- {strategy_name}策略 ---")
            
            async with SmartMoneyFollowStrategy(strategy_type, follow_rules) as strategy:
                # 执行策略
                positions = await strategy.execute_strategy(hours=6)
                
                print(f"执行了 {len(positions)} 个跟随仓位")
                
                for pos in positions[:3]:  # 显示前3个仓位
                    print(f"  {pos['action'].upper()} {pos['token_symbol']}")
                    print(f"    金额: ${pos['amount_usd']:,.0f}")
                    print(f"    入场价: ${pos['entry_price']:,.4f}")
                    print(f"    止损: ${pos['stop_loss']:,.4f}")
                    print(f"    止盈: ${pos['take_profit']:,.4f}")
                    print(f"    信号强度: {pos['signal_strength']:.3f}")
                    print(f"    信号置信度: {pos['signal_confidence']:.3f}")
                
                # 监控仓位
                if positions:
                    print(f"\n  --- 仓位监控 ---")
                    position_updates = await strategy.monitor_positions()
                    
                    for update in position_updates[:3]:
                        print(f"  {update['token_symbol']} ({update['action'].upper()})")
                        print(f"    状态: {update['status']}")
                        print(f"    当前盈亏: {update['current_pnl']:.2%}")
                        if update.get('close_reason'):
                            print(f"    关闭原因: {update['close_reason']}")
                
                # 获取策略表现
                performance = await strategy.get_strategy_performance()
                print(f"\n  策略表现:")
                print(f"    总交易数: {performance['total_trades']}")
                print(f"    胜率: {performance['win_rate']:.2%}")
                print(f"    平均收益: {performance['avg_return']:.2%}")
                print(f"    总收益: {performance['total_return']:.2%}")
                print(f"    最大回撤: {performance['max_drawdown']:.2%}")
                print(f"    夏普比率: {performance['sharpe_ratio']:.2f}")
                print(f"    活跃仓位: {performance['active_positions']}")
    
    except Exception as e:
        print(f"跟随策略演示失败: {e}")


async def demo_risk_analysis():
    """演示风险分析"""
    print("\n=== Smart Money风险分析演示 ===")
    
    try:
        print("--- Smart Money地址风险评估 ---")
        
        # 模拟风险分析结果
        risk_categories = {
            'low_risk': {
                'count': 15,
                'characteristics': ['胜率>70%', '最大回撤<10%', '夏普比率>2.0'],
                'avg_roi': 250.0
            },
            'medium_risk': {
                'count': 25,
                'characteristics': ['胜率50-70%', '最大回撤10-20%', '夏普比率1.0-2.0'],
                'avg_roi': 180.0
            },
            'high_risk': {
                'count': 10,
                'characteristics': ['胜率<50%', '最大回撤>20%', '夏普比率<1.0'],
                'avg_roi': 120.0
            }
        }
        
        for risk_level, data in risk_categories.items():
            print(f"\n{risk_level.replace('_', ' ').title()}:")
            print(f"  地址数量: {data['count']}")
            print(f"  平均ROI: {data['avg_roi']:.1f}%")
            print(f"  特征: {', '.join(data['characteristics'])}")
        
        print("\n--- 跟随策略风险建议 ---")
        recommendations = [
            "优先跟随低风险Smart Money地址",
            "对高风险地址降低跟随金额",
            "设置严格的止损和仓位管理",
            "分散跟随多个不同风格的Smart Money",
            "定期评估和调整跟随策略"
        ]
        
        for i, rec in enumerate(recommendations, 1):
            print(f"{i}. {rec}")
    
    except Exception as e:
        print(f"风险分析演示失败: {e}")


async def main():
    """主演示函数"""
    print("🧠 Smart Money分析系统演示")
    print("=" * 50)
    
    # 检查配置
    try:
        from config.settings import API_CONFIG, TRADING_CONFIG
        print("✅ 配置文件加载成功")
        
        # 检查Smart Money配置
        smart_money_config = TRADING_CONFIG.get('smart_money', {})
        print(f"Smart Money配置:")
        print(f"  最小ROI: {smart_money_config.get('min_roi', 'N/A')}%")
        print(f"  最小交易数: {smart_money_config.get('min_trades', 'N/A')}")
        print(f"  跟随金额: {smart_money_config.get('follow_amount', 'N/A')}%")
    
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        print("某些功能可能无法正常工作")
    
    # 运行演示
    demos = [
        ("Smart Money识别", demo_smart_money_identification),
        ("Smart Money追踪", demo_smart_money_tracking),
        ("信号生成", demo_signal_generation),
        ("跟随策略", demo_follow_strategy),
        ("风险分析", demo_risk_analysis),
    ]
    
    for demo_name, demo_func in demos:
        try:
            print(f"\n🔄 开始 {demo_name} 演示...")
            await demo_func()
            print(f"✅ {demo_name} 演示完成")
        except Exception as e:
            print(f"❌ {demo_name} 演示失败: {e}")
            logger.error(f"{demo_name} demo failed", exc_info=True)
        
        # 在演示之间添加延迟
        await asyncio.sleep(1)
    
    print("\n🎉 所有演示完成!")
    print("=" * 50)
    print("\n💡 提示:")
    print("- Smart Money分析需要大量历史数据支持")
    print("- 实际使用时需要配置真实的API密钥和数据源")
    print("- 建议结合多种分析方法进行决策")
    print("- 注意风险管理和资金安全")


if __name__ == "__main__":
    # 运行演示
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 演示被用户中断")
    except Exception as e:
        print(f"\n💥 演示过程中发生错误: {e}")
        logger.error("Demo failed", exc_info=True)
