"""
链上技术指标计算模块
计算各种链上技术指标，如MVRV、活跃地址、网络价值等
"""
import asyncio
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timezone, timedelta
from dataclasses import dataclass

from src.database.repositories import TransactionRepository, MarketDataRepository, OnChainMetricsRepository
from src.database.data_service import DataService
from src.utils.logger import get_logger
from config.settings import TRADING_CONFIG

logger = get_logger(__name__)


@dataclass
class OnChainMetrics:
    """链上指标数据"""
    symbol: str
    date: datetime
    price_usd: float
    market_cap: float
    
    # 网络活动指标
    active_addresses: int
    new_addresses: int
    transaction_count: int
    transaction_volume: float
    
    # 价值指标
    mvrv_ratio: float
    nvt_ratio: float
    rvt_ratio: float
    
    # 持有者指标
    holder_distribution: Dict[str, float]
    whale_concentration: float
    
    # 流动性指标
    exchange_flow_ratio: float
    dormant_circulation: float
    
    # 情绪指标
    fear_greed_index: float
    social_sentiment: float


class OnChainIndicatorCalculator:
    """链上指标计算器"""
    
    def __init__(self):
        self.transaction_repo = TransactionRepository()
        self.market_data_repo = MarketDataRepository()
        self.onchain_metrics_repo = OnChainMetricsRepository()
        self.data_service = None
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.data_service = await DataService().__aenter__()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.data_service:
            await self.data_service.__aexit__(exc_type, exc_val, exc_tb)
    
    async def calculate_all_indicators(self, symbol: str, days: int = 30) -> List[OnChainMetrics]:
        """计算所有链上指标"""
        try:
            logger.info(f"Calculating on-chain indicators for {symbol} over {days} days")
            
            # 获取历史价格数据
            price_history = self.market_data_repo.get_price_history(symbol, days=days)
            
            if not price_history:
                logger.warning(f"No price history found for {symbol}")
                return []
            
            # 获取交易数据
            since = datetime.now(timezone.utc) - timedelta(days=days)
            transactions = await self._get_transactions_since(since)
            
            indicators = []
            
            # 按日期分组计算指标
            for price_data in price_history:
                try:
                    date = price_data.timestamp.date()
                    daily_transactions = [
                        tx for tx in transactions 
                        if tx.block_timestamp.date() == date
                    ]
                    
                    # 计算当日指标
                    metrics = await self._calculate_daily_metrics(
                        symbol, price_data, daily_transactions, date
                    )
                    
                    if metrics:
                        indicators.append(metrics)
                
                except Exception as e:
                    logger.error(f"Failed to calculate metrics for {date}: {e}")
                    continue
            
            logger.info(f"Calculated indicators for {len(indicators)} days")
            return indicators
        
        except Exception as e:
            logger.error(f"Failed to calculate all indicators: {e}")
            raise
    
    async def _get_transactions_since(self, since: datetime) -> List[Any]:
        """获取指定时间以来的交易"""
        try:
            # 这里应该从数据库获取交易数据
            # 简化处理，返回空列表
            return []
        except Exception as e:
            logger.error(f"Failed to get transactions since {since}: {e}")
            return []
    
    async def _calculate_daily_metrics(self, symbol: str, price_data: Any, 
                                     transactions: List[Any], date: datetime.date) -> Optional[OnChainMetrics]:
        """计算单日指标"""
        try:
            # 基本价格和市值数据
            price_usd = price_data.price_usd
            market_cap = price_data.market_cap or 0
            
            # 网络活动指标
            active_addresses = await self._calculate_active_addresses(transactions)
            new_addresses = await self._calculate_new_addresses(transactions, date)
            transaction_count = len(transactions)
            transaction_volume = sum(getattr(tx, 'value_usd', 0) for tx in transactions)
            
            # 价值指标
            mvrv_ratio = await self._calculate_mvrv_ratio(symbol, price_usd, date)
            nvt_ratio = await self._calculate_nvt_ratio(price_usd, market_cap, transaction_volume)
            rvt_ratio = await self._calculate_rvt_ratio(price_usd, market_cap, transaction_volume)
            
            # 持有者指标
            holder_distribution = await self._calculate_holder_distribution(transactions)
            whale_concentration = await self._calculate_whale_concentration(transactions)
            
            # 流动性指标
            exchange_flow_ratio = await self._calculate_exchange_flow_ratio(transactions)
            dormant_circulation = await self._calculate_dormant_circulation(transactions)
            
            # 情绪指标
            fear_greed_index = await self._calculate_fear_greed_index(symbol, price_data)
            social_sentiment = await self._calculate_social_sentiment(symbol, date)
            
            return OnChainMetrics(
                symbol=symbol,
                date=datetime.combine(date, datetime.min.time()).replace(tzinfo=timezone.utc),
                price_usd=price_usd,
                market_cap=market_cap,
                active_addresses=active_addresses,
                new_addresses=new_addresses,
                transaction_count=transaction_count,
                transaction_volume=transaction_volume,
                mvrv_ratio=mvrv_ratio,
                nvt_ratio=nvt_ratio,
                rvt_ratio=rvt_ratio,
                holder_distribution=holder_distribution,
                whale_concentration=whale_concentration,
                exchange_flow_ratio=exchange_flow_ratio,
                dormant_circulation=dormant_circulation,
                fear_greed_index=fear_greed_index,
                social_sentiment=social_sentiment
            )
        
        except Exception as e:
            logger.error(f"Failed to calculate daily metrics for {date}: {e}")
            return None
    
    async def _calculate_active_addresses(self, transactions: List[Any]) -> int:
        """计算活跃地址数"""
        try:
            active_addresses = set()
            
            for tx in transactions:
                if hasattr(tx, 'from_address') and tx.from_address:
                    active_addresses.add(tx.from_address.lower())
                if hasattr(tx, 'to_address') and tx.to_address:
                    active_addresses.add(tx.to_address.lower())
            
            return len(active_addresses)
        
        except Exception as e:
            logger.error(f"Failed to calculate active addresses: {e}")
            return 0
    
    async def _calculate_new_addresses(self, transactions: List[Any], date: datetime.date) -> int:
        """计算新地址数"""
        try:
            # 简化处理：假设10%的活跃地址是新地址
            active_count = await self._calculate_active_addresses(transactions)
            return int(active_count * 0.1)
        
        except Exception as e:
            logger.error(f"Failed to calculate new addresses: {e}")
            return 0
    
    async def _calculate_mvrv_ratio(self, symbol: str, current_price: float, date: datetime.date) -> float:
        """计算MVRV比率 (Market Value to Realized Value)"""
        try:
            # MVRV = Market Cap / Realized Cap
            # 简化计算：使用历史平均价格作为实现价格
            
            # 获取过去30天的价格数据
            price_history = self.market_data_repo.get_price_history(symbol, days=30)
            
            if not price_history:
                return 1.0
            
            # 计算平均实现价格
            realized_price = np.mean([p.price_usd for p in price_history])
            
            if realized_price <= 0:
                return 1.0
            
            mvrv_ratio = current_price / realized_price
            
            return mvrv_ratio
        
        except Exception as e:
            logger.error(f"Failed to calculate MVRV ratio: {e}")
            return 1.0
    
    async def _calculate_nvt_ratio(self, price: float, market_cap: float, tx_volume: float) -> float:
        """计算NVT比率 (Network Value to Transactions)"""
        try:
            # NVT = Market Cap / Daily Transaction Volume
            if tx_volume <= 0:
                return float('inf')
            
            nvt_ratio = market_cap / tx_volume
            
            return nvt_ratio
        
        except Exception as e:
            logger.error(f"Failed to calculate NVT ratio: {e}")
            return 0.0
    
    async def _calculate_rvt_ratio(self, price: float, market_cap: float, tx_volume: float) -> float:
        """计算RVT比率 (Realized Value to Transactions)"""
        try:
            # RVT = Realized Cap / Daily Transaction Volume
            # 简化处理：使用市值的80%作为实现市值
            realized_cap = market_cap * 0.8
            
            if tx_volume <= 0:
                return float('inf')
            
            rvt_ratio = realized_cap / tx_volume
            
            return rvt_ratio
        
        except Exception as e:
            logger.error(f"Failed to calculate RVT ratio: {e}")
            return 0.0
    
    async def _calculate_holder_distribution(self, transactions: List[Any]) -> Dict[str, float]:
        """计算持有者分布"""
        try:
            # 简化的持有者分布计算
            total_addresses = await self._calculate_active_addresses(transactions)
            
            if total_addresses == 0:
                return {
                    'whales': 0.0,
                    'large_holders': 0.0,
                    'medium_holders': 0.0,
                    'small_holders': 0.0
                }
            
            # 基于经验分布
            distribution = {
                'whales': 0.01,          # 1% 巨鲸
                'large_holders': 0.04,   # 4% 大户
                'medium_holders': 0.15,  # 15% 中户
                'small_holders': 0.80    # 80% 小户
            }
            
            return distribution
        
        except Exception as e:
            logger.error(f"Failed to calculate holder distribution: {e}")
            return {}
    
    async def _calculate_whale_concentration(self, transactions: List[Any]) -> float:
        """计算巨鲸集中度"""
        try:
            # 计算大额交易的比例
            large_transactions = [
                tx for tx in transactions 
                if getattr(tx, 'value_usd', 0) > 100000  # 10万美元以上
            ]
            
            if not transactions:
                return 0.0
            
            concentration = len(large_transactions) / len(transactions)
            
            return concentration
        
        except Exception as e:
            logger.error(f"Failed to calculate whale concentration: {e}")
            return 0.0
    
    async def _calculate_exchange_flow_ratio(self, transactions: List[Any]) -> float:
        """计算交易所流动比率"""
        try:
            # 简化处理：假设20%的交易与交易所相关
            return 0.2
        
        except Exception as e:
            logger.error(f"Failed to calculate exchange flow ratio: {e}")
            return 0.0
    
    async def _calculate_dormant_circulation(self, transactions: List[Any]) -> float:
        """计算休眠流通量"""
        try:
            # 简化处理：假设30%的代币处于休眠状态
            return 0.3
        
        except Exception as e:
            logger.error(f"Failed to calculate dormant circulation: {e}")
            return 0.0
    
    async def _calculate_fear_greed_index(self, symbol: str, price_data: Any) -> float:
        """计算恐惧贪婪指数"""
        try:
            # 基于价格变化计算简化的恐惧贪婪指数
            price_change = getattr(price_data, 'price_change_24h', 0)
            
            # 将价格变化映射到0-100的恐惧贪婪指数
            # 负变化 -> 恐惧 (0-50)
            # 正变化 -> 贪婪 (50-100)
            
            if price_change < 0:
                # 恐惧区间
                fear_index = max(0, 50 + price_change * 2)  # -25%变化对应0
            else:
                # 贪婪区间
                greed_index = min(100, 50 + price_change * 2)  # +25%变化对应100
                fear_index = greed_index
            
            return fear_index
        
        except Exception as e:
            logger.error(f"Failed to calculate fear greed index: {e}")
            return 50.0  # 中性值
    
    async def _calculate_social_sentiment(self, symbol: str, date: datetime.date) -> float:
        """计算社交情绪指数"""
        try:
            # 简化处理：返回中性情绪
            return 0.5
        
        except Exception as e:
            logger.error(f"Failed to calculate social sentiment: {e}")
            return 0.5
    
    async def calculate_mvrv_z_score(self, symbol: str, days: int = 365) -> float:
        """计算MVRV Z-Score"""
        try:
            # 获取历史MVRV数据
            metrics_history = self.onchain_metrics_repo.get_metrics_history(symbol, days=days)
            
            if len(metrics_history) < 30:  # 需要足够的历史数据
                return 0.0
            
            mvrv_values = [m.mvrv_ratio for m in metrics_history if m.mvrv_ratio > 0]
            
            if not mvrv_values:
                return 0.0
            
            # 计算Z-Score
            current_mvrv = mvrv_values[-1]
            mean_mvrv = np.mean(mvrv_values)
            std_mvrv = np.std(mvrv_values)
            
            if std_mvrv == 0:
                return 0.0
            
            z_score = (current_mvrv - mean_mvrv) / std_mvrv
            
            return z_score
        
        except Exception as e:
            logger.error(f"Failed to calculate MVRV Z-Score: {e}")
            return 0.0
    
    async def calculate_network_growth(self, symbol: str, days: int = 30) -> Dict[str, float]:
        """计算网络增长指标"""
        try:
            metrics_history = self.onchain_metrics_repo.get_metrics_history(symbol, days=days)
            
            if len(metrics_history) < 2:
                return {'growth_rate': 0.0, 'trend': 'stable'}
            
            # 计算活跃地址增长率
            recent_addresses = [m.active_addresses for m in metrics_history[-7:]]  # 最近7天
            earlier_addresses = [m.active_addresses for m in metrics_history[-14:-7]]  # 前7天
            
            if not recent_addresses or not earlier_addresses:
                return {'growth_rate': 0.0, 'trend': 'stable'}
            
            recent_avg = np.mean(recent_addresses)
            earlier_avg = np.mean(earlier_addresses)
            
            if earlier_avg == 0:
                growth_rate = 0.0
            else:
                growth_rate = (recent_avg - earlier_avg) / earlier_avg
            
            # 确定趋势
            if growth_rate > 0.05:
                trend = 'growing'
            elif growth_rate < -0.05:
                trend = 'declining'
            else:
                trend = 'stable'
            
            return {
                'growth_rate': growth_rate,
                'trend': trend,
                'recent_avg': recent_avg,
                'earlier_avg': earlier_avg
            }
        
        except Exception as e:
            logger.error(f"Failed to calculate network growth: {e}")
            return {'growth_rate': 0.0, 'trend': 'stable'}
    
    async def calculate_velocity(self, symbol: str, days: int = 30) -> float:
        """计算代币流通速度"""
        try:
            metrics_history = self.onchain_metrics_repo.get_metrics_history(symbol, days=days)
            
            if not metrics_history:
                return 0.0
            
            # 计算平均交易量和市值
            avg_tx_volume = np.mean([m.transaction_volume for m in metrics_history])
            avg_market_cap = np.mean([m.market_cap for m in metrics_history if m.market_cap > 0])
            
            if avg_market_cap == 0:
                return 0.0
            
            # 流通速度 = 交易量 / 市值
            velocity = avg_tx_volume / avg_market_cap
            
            return velocity
        
        except Exception as e:
            logger.error(f"Failed to calculate velocity: {e}")
            return 0.0
