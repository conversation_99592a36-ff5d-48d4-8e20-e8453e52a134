"""
市场数据获取模块
提供价格数据、市场指标、交易量等信息
"""
import asyncio
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timezone, timedelta
import aiohttp

from config.settings import API_CONFIG
from src.utils.logger import get_logger, log_api_call
from src.utils.helpers import retry_on_failure

logger = get_logger(__name__)


class CoinGeckoAPI:
    """CoinGecko API客户端"""
    
    def __init__(self):
        config = API_CONFIG['coingecko']
        self.api_key = config['api_key']
        self.base_url = config['base_url']
        self.rate_limit_per_second = config['rate_limit']
        self.session = None
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    @retry_on_failure(max_attempts=3, delay=1.0)
    async def _make_request(self, endpoint: str, params: Dict[str, Any] = None) -> Union[Dict[str, Any], List[Any]]:
        """发送API请求"""
        if not self.session:
            raise RuntimeError("Session not initialized. Use async context manager.")
        
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        
        # 添加API密钥到headers（如果有Pro版本）
        headers = {}
        if self.api_key:
            headers['x-cg-pro-api-key'] = self.api_key
        
        try:
            # 应用速率限制
            await asyncio.sleep(1.0 / self.rate_limit_per_second)
            
            async with self.session.get(url, params=params, headers=headers) as response:
                response.raise_for_status()
                return await response.json()
                
        except aiohttp.ClientError as e:
            logger.error(f"HTTP error for {url}: {e}")
            raise
        except Exception as e:
            logger.error(f"Request error for {url}: {e}")
            raise
    
    @log_api_call("CoinGecko", "get_price")
    async def get_price(self, coin_ids: Union[str, List[str]], 
                       vs_currencies: Union[str, List[str]] = 'usd',
                       include_market_cap: bool = True,
                       include_24hr_vol: bool = True,
                       include_24hr_change: bool = True,
                       include_last_updated_at: bool = True) -> Dict[str, Any]:
        """获取代币价格"""
        if isinstance(coin_ids, str):
            coin_ids = [coin_ids]
        if isinstance(vs_currencies, str):
            vs_currencies = [vs_currencies]
        
        params = {
            'ids': ','.join(coin_ids),
            'vs_currencies': ','.join(vs_currencies),
            'include_market_cap': str(include_market_cap).lower(),
            'include_24hr_vol': str(include_24hr_vol).lower(),
            'include_24hr_change': str(include_24hr_change).lower(),
            'include_last_updated_at': str(include_last_updated_at).lower()
        }
        
        response = await self._make_request('simple/price', params)
        
        # 添加时间戳
        for coin_id in response:
            response[coin_id]['timestamp'] = datetime.now(timezone.utc)
        
        return response
    
    @log_api_call("CoinGecko", "get_coin_info")
    async def get_coin_info(self, coin_id: str, localization: bool = False,
                           tickers: bool = False, market_data: bool = True,
                           community_data: bool = False, developer_data: bool = False,
                           sparkline: bool = False) -> Dict[str, Any]:
        """获取代币详细信息"""
        params = {
            'localization': str(localization).lower(),
            'tickers': str(tickers).lower(),
            'market_data': str(market_data).lower(),
            'community_data': str(community_data).lower(),
            'developer_data': str(developer_data).lower(),
            'sparkline': str(sparkline).lower()
        }
        
        response = await self._make_request(f'coins/{coin_id}', params)
        
        # 提取关键信息
        processed_info = {
            'id': response.get('id'),
            'symbol': response.get('symbol'),
            'name': response.get('name'),
            'description': response.get('description', {}).get('en', ''),
            'links': response.get('links', {}),
            'image': response.get('image', {}),
            'country_origin': response.get('country_origin'),
            'genesis_date': response.get('genesis_date'),
            'sentiment_votes_up_percentage': response.get('sentiment_votes_up_percentage'),
            'sentiment_votes_down_percentage': response.get('sentiment_votes_down_percentage'),
            'market_cap_rank': response.get('market_cap_rank'),
            'coingecko_rank': response.get('coingecko_rank'),
            'coingecko_score': response.get('coingecko_score'),
            'developer_score': response.get('developer_score'),
            'community_score': response.get('community_score'),
            'liquidity_score': response.get('liquidity_score'),
            'public_interest_score': response.get('public_interest_score'),
            'categories': response.get('categories', []),
            'platforms': response.get('platforms', {}),
            'timestamp': datetime.now(timezone.utc)
        }
        
        # 添加市场数据
        if market_data and 'market_data' in response:
            market_data = response['market_data']
            processed_info['market_data'] = {
                'current_price': market_data.get('current_price', {}),
                'market_cap': market_data.get('market_cap', {}),
                'market_cap_rank': market_data.get('market_cap_rank'),
                'fully_diluted_valuation': market_data.get('fully_diluted_valuation', {}),
                'total_volume': market_data.get('total_volume', {}),
                'high_24h': market_data.get('high_24h', {}),
                'low_24h': market_data.get('low_24h', {}),
                'price_change_24h': market_data.get('price_change_24h'),
                'price_change_percentage_24h': market_data.get('price_change_percentage_24h'),
                'price_change_percentage_7d': market_data.get('price_change_percentage_7d'),
                'price_change_percentage_14d': market_data.get('price_change_percentage_14d'),
                'price_change_percentage_30d': market_data.get('price_change_percentage_30d'),
                'price_change_percentage_60d': market_data.get('price_change_percentage_60d'),
                'price_change_percentage_200d': market_data.get('price_change_percentage_200d'),
                'price_change_percentage_1y': market_data.get('price_change_percentage_1y'),
                'market_cap_change_24h': market_data.get('market_cap_change_24h'),
                'market_cap_change_percentage_24h': market_data.get('market_cap_change_percentage_24h'),
                'circulating_supply': market_data.get('circulating_supply'),
                'total_supply': market_data.get('total_supply'),
                'max_supply': market_data.get('max_supply'),
                'ath': market_data.get('ath', {}),
                'ath_change_percentage': market_data.get('ath_change_percentage', {}),
                'ath_date': market_data.get('ath_date', {}),
                'atl': market_data.get('atl', {}),
                'atl_change_percentage': market_data.get('atl_change_percentage', {}),
                'atl_date': market_data.get('atl_date', {}),
                'last_updated': market_data.get('last_updated')
            }
        
        return processed_info
    
    @log_api_call("CoinGecko", "get_market_chart")
    async def get_market_chart(self, coin_id: str, vs_currency: str = 'usd',
                              days: Union[int, str] = 30, interval: str = 'daily') -> Dict[str, Any]:
        """获取历史价格图表数据"""
        params = {
            'vs_currency': vs_currency,
            'days': str(days),
            'interval': interval
        }
        
        response = await self._make_request(f'coins/{coin_id}/market_chart', params)
        
        # 处理数据格式
        processed_data = {
            'coin_id': coin_id,
            'vs_currency': vs_currency,
            'days': days,
            'interval': interval,
            'prices': [],
            'market_caps': [],
            'total_volumes': [],
            'timestamp': datetime.now(timezone.utc)
        }
        
        # 转换时间戳和价格数据
        for timestamp, price in response.get('prices', []):
            processed_data['prices'].append({
                'timestamp': datetime.fromtimestamp(timestamp / 1000, timezone.utc),
                'price': price
            })
        
        for timestamp, market_cap in response.get('market_caps', []):
            processed_data['market_caps'].append({
                'timestamp': datetime.fromtimestamp(timestamp / 1000, timezone.utc),
                'market_cap': market_cap
            })
        
        for timestamp, volume in response.get('total_volumes', []):
            processed_data['total_volumes'].append({
                'timestamp': datetime.fromtimestamp(timestamp / 1000, timezone.utc),
                'volume': volume
            })
        
        return processed_data
    
    @log_api_call("CoinGecko", "get_trending")
    async def get_trending(self) -> Dict[str, Any]:
        """获取热门搜索代币"""
        response = await self._make_request('search/trending')
        
        return {
            'coins': response.get('coins', []),
            'nfts': response.get('nfts', []),
            'categories': response.get('categories', []),
            'timestamp': datetime.now(timezone.utc)
        }
    
    @log_api_call("CoinGecko", "get_fear_greed_index")
    async def get_fear_greed_index(self, limit: int = 1, date_format: str = 'us') -> Dict[str, Any]:
        """获取恐惧贪婪指数"""
        # 注意：这个API可能需要不同的端点或第三方服务
        # 这里提供一个示例实现
        try:
            # 使用Alternative.me的恐惧贪婪指数API
            url = f"https://api.alternative.me/fng/?limit={limit}&format=json&date_format={date_format}"
            
            async with self.session.get(url) as response:
                response.raise_for_status()
                data = await response.json()
                
                return {
                    'data': data.get('data', []),
                    'metadata': data.get('metadata', {}),
                    'timestamp': datetime.now(timezone.utc)
                }
        except Exception as e:
            logger.error(f"Failed to get fear greed index: {e}")
            return {
                'data': [],
                'metadata': {},
                'error': str(e),
                'timestamp': datetime.now(timezone.utc)
            }
