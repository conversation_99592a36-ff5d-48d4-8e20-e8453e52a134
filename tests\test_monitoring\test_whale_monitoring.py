"""
巨鲸监控系统测试
"""
import pytest
import asyncio
from datetime import datetime, timezone, timedelta
from unittest.mock import Mock, patch, MagicMock

from src.monitoring.whale_detector import WhaleDetector, WhaleProfile, WhaleType
from src.monitoring.whale_monitor import <PERSON>haleMonitor, WhaleTransaction, AlertLevel
from src.monitoring.alert_system import AlertSystem, NotificationChannel, AlertRule


class TestWhaleDetector:
    """巨鲸检测器测试"""
    
    @pytest.mark.asyncio
    async def test_detector_initialization(self):
        """测试检测器初始化"""
        with patch('src.monitoring.whale_detector.DataService'):
            async with WhaleDetector() as detector:
                assert detector.address_repo is not None
                assert detector.transaction_repo is not None
                assert detector.whale_alert_repo is not None
                assert detector.thresholds is not None
    
    @pytest.mark.asyncio
    async def test_scan_for_whales(self):
        """测试巨鲸扫描"""
        with patch('src.monitoring.whale_detector.DataService'):
            detector = WhaleDetector()
            
            # 模拟大额交易
            mock_transactions = [
                Mock(
                    tx_hash='0xabc...',
                    from_address='0x123...',
                    to_address='0x456...',
                    value_usd=10000000,
                    block_timestamp=datetime.now(timezone.utc)
                )
            ]
            
            with patch.object(detector.transaction_repo, 'get_large_transactions', return_value=mock_transactions):
                with patch.object(detector, '_analyze_whale_candidate') as mock_analyze:
                    mock_profile = WhaleProfile(
                        address='0x123...',
                        whale_type=WhaleType.TRADER,
                        balance_eth=1000,
                        balance_usd=2000000,
                        total_volume_usd=50000000,
                        transaction_count=500,
                        first_seen=datetime.now(timezone.utc) - timedelta(days=365),
                        last_active=datetime.now(timezone.utc),
                        risk_score=0.7,
                        influence_score=0.8,
                        trading_pattern={},
                        associated_addresses=[],
                        labels=['trader', 'whale']
                    )
                    mock_analyze.return_value = mock_profile
                    
                    with patch.object(detector, '_is_whale', return_value=True):
                        with patch.object(detector, '_update_whale_status'):
                            whales = await detector.scan_for_whales(hours=24)
                            
                            assert isinstance(whales, list)
                            if whales:
                                assert whales[0].whale_type == WhaleType.TRADER
    
    def test_classify_whale_type(self):
        """测试巨鲸类型分类"""
        detector = WhaleDetector()
        
        # 测试交易所类型
        exchange_address = '0x3f5CE5FBFe3E9af3971dD833D26bA9b5C936f0bE'  # Binance
        whale_type = detector._classify_whale_type(exchange_address, [], [], 1000)
        assert whale_type == WhaleType.EXCHANGE
        
        # 测试持有型巨鲸
        holder_transactions = []  # 无交易记录
        whale_type = detector._classify_whale_type('0x123...', holder_transactions, [], 50000)
        assert whale_type == WhaleType.HOLDER
    
    def test_calculate_risk_score(self):
        """测试风险分数计算"""
        detector = WhaleDetector()
        
        mock_transactions = [
            {'value_eth': 100, 'from_address': '0x123...', 'to_address': '0x456...'},
            {'value_eth': 200, 'from_address': '0x123...', 'to_address': '0x789...'}
        ]
        
        risk_score = detector._calculate_risk_score(mock_transactions, [], 10000)
        
        assert 0 <= risk_score <= 1
        assert isinstance(risk_score, float)
    
    def test_calculate_influence_score(self):
        """测试影响力分数计算"""
        detector = WhaleDetector()
        
        influence_score = detector._calculate_influence_score(
            balance_usd=50000000,
            total_volume_usd=200000000,
            transaction_count=1000,
            whale_type=WhaleType.INSTITUTION
        )
        
        assert 0 <= influence_score <= 1
        assert isinstance(influence_score, float)


class TestWhaleMonitor:
    """巨鲸监控器测试"""
    
    @pytest.mark.asyncio
    async def test_monitor_initialization(self):
        """测试监控器初始化"""
        with patch('src.monitoring.whale_monitor.WhaleDetector'):
            with patch('src.monitoring.whale_monitor.DataService'):
                async with WhaleMonitor() as monitor:
                    assert monitor.whale_alert_repo is not None
                    assert monitor.transaction_repo is not None
                    assert len(monitor.monitoring_rules) > 0
    
    @pytest.mark.asyncio
    async def test_monitor_whale_transactions(self):
        """测试巨鲸交易监控"""
        with patch('src.monitoring.whale_monitor.WhaleDetector'):
            with patch('src.monitoring.whale_monitor.DataService'):
                monitor = WhaleMonitor()
                
                # 模拟大额交易
                mock_transaction = Mock(
                    tx_hash='0xabc...',
                    from_address='0x123...',
                    to_address='0x456...',
                    value_usd=15000000,
                    token_symbol='ETH',
                    block_timestamp=datetime.now(timezone.utc)
                )
                
                with patch.object(monitor.transaction_repo, 'get_large_transactions', return_value=[mock_transaction]):
                    with patch.object(monitor, '_analyze_whale_transaction') as mock_analyze:
                        mock_whale_tx = WhaleTransaction(
                            tx_hash='0xabc...',
                            from_address='0x123...',
                            to_address='0x456...',
                            value_usd=15000000,
                            token_symbol='ETH',
                            transaction_type='large_transfer',
                            timestamp=datetime.now(timezone.utc),
                            alert_level=AlertLevel.HIGH,
                            impact_score=0.8,
                            market_context={}
                        )
                        mock_analyze.return_value = mock_whale_tx
                        
                        with patch.object(monitor, '_generate_whale_alert'):
                            whale_txs = await monitor.monitor_whale_transactions(hours=1)
                            
                            assert isinstance(whale_txs, list)
                            if whale_txs:
                                assert whale_txs[0].alert_level == AlertLevel.HIGH
    
    def test_classify_transaction_type(self):
        """测试交易类型分类"""
        monitor = WhaleMonitor()
        
        # 模拟交易所提币
        mock_tx = Mock(
            from_address='0x3f5CE5FBFe3E9af3971dD833D26bA9b5C936f0bE',  # Binance
            to_address='0x123...'  # 普通钱包
        )
        
        tx_type = asyncio.run(monitor._classify_transaction_type(mock_tx))
        assert tx_type == 'exchange_withdrawal'
    
    def test_calculate_alert_level(self):
        """测试预警级别计算"""
        monitor = WhaleMonitor()
        
        # 测试超大额交易
        mock_tx = Mock(value_usd=100000000)  # 1亿美元
        alert_level = monitor._calculate_alert_level(mock_tx, 'large_transfer')
        assert alert_level == AlertLevel.CRITICAL
        
        # 测试中等金额交易
        mock_tx = Mock(value_usd=5000000)  # 500万美元
        alert_level = monitor._calculate_alert_level(mock_tx, 'wallet_transfer')
        assert alert_level == AlertLevel.MEDIUM
    
    def test_calculate_impact_score(self):
        """测试影响分数计算"""
        monitor = WhaleMonitor()
        
        mock_tx = Mock(
            value_usd=50000000,
            block_timestamp=datetime.now(timezone.utc).replace(hour=14)  # 工作时间
        )
        
        impact_score = monitor._calculate_impact_score(mock_tx, 'exchange_withdrawal')
        
        assert 0 <= impact_score <= 1
        assert isinstance(impact_score, float)


class TestAlertSystem:
    """预警系统测试"""
    
    def test_alert_system_initialization(self):
        """测试预警系统初始化"""
        alert_system = AlertSystem()
        
        assert alert_system.whale_alert_repo is not None
        assert len(alert_system.alert_rules) > 0
        assert alert_system.notification_history == {}
    
    def test_match_alert_rules(self):
        """测试预警规则匹配"""
        alert_system = AlertSystem()
        
        # 创建模拟预警
        mock_alert = Mock(
            alert_level='critical',
            amount_usd=60000000,
            alert_type='large_transfer'
        )
        
        matching_rules = alert_system._match_alert_rules(mock_alert)
        
        assert isinstance(matching_rules, list)
        # 应该匹配到关键巨鲸活动规则
        critical_rules = [rule for rule in matching_rules if 'critical' in rule.name]
        assert len(critical_rules) > 0
    
    def test_create_notification_message(self):
        """测试通知消息创建"""
        alert_system = AlertSystem()
        
        mock_alert = Mock(
            id=1,
            tx_hash='0xabc...',
            alert_type='exchange_outflow',
            amount_usd=20000000,
            token_symbol='ETH',
            exchange_name='Binance',
            to_address='0x123...',
            from_address='0x456...',
            alert_level='high'
        )
        
        mock_rule = AlertRule(
            name='test_rule',
            description='Test rule',
            conditions={},
            channels=[NotificationChannel.TELEGRAM]
        )
        
        message = alert_system._create_notification_message(
            mock_alert, mock_rule, NotificationChannel.TELEGRAM
        )
        
        assert message is not None
        assert message.title is not None
        assert message.content is not None
        assert message.channel == NotificationChannel.TELEGRAM
    
    def test_format_exchange_outflow_message(self):
        """测试交易所流出消息格式化"""
        alert_system = AlertSystem()
        
        mock_alert = Mock(
            amount_usd=25000000,
            token_symbol='ETH',
            exchange_name='Binance',
            to_address='******************************************',
            tx_hash='0xabcdef123456789abcdef123456789abcdef123456789abcdef123456789abcdef12',
            alert_level='high'
        )
        
        message = alert_system._format_exchange_outflow_message(mock_alert)
        
        assert '交易所大额流出' in message
        assert '$25,000,000' in message
        assert 'Binance' in message
        assert '0x1234567890...56789a' in message
    
    @pytest.mark.asyncio
    async def test_send_notification(self):
        """测试发送通知"""
        alert_system = AlertSystem()
        
        mock_message = Mock(
            title='Test Alert',
            content='Test content',
            channel=NotificationChannel.TELEGRAM,
            alert_level=AlertLevel.HIGH,
            metadata={'test': 'data'},
            timestamp=datetime.now(timezone.utc)
        )
        
        # 测试Telegram通知
        with patch.object(alert_system, '_send_telegram_notification', return_value=True):
            result = await alert_system._send_notification(mock_message)
            assert result is True


class TestIntegration:
    """集成测试"""
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_full_whale_monitoring_workflow(self):
        """测试完整的巨鲸监控工作流程"""
        # 这个测试需要真实的数据库连接，通常在开发环境中跳过
        pytest.skip("Integration test requires real database connection")
        
        # 1. 扫描巨鲸地址
        async with WhaleDetector() as detector:
            whales = await detector.scan_for_whales(hours=24)
            assert isinstance(whales, list)
        
        # 2. 监控巨鲸交易
        async with WhaleMonitor() as monitor:
            whale_txs = await monitor.monitor_whale_transactions(hours=1)
            assert isinstance(whale_txs, list)
        
        # 3. 处理预警
        alert_system = AlertSystem()
        notifications = await alert_system.process_whale_alerts(hours=1)
        assert isinstance(notifications, list)


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v", "-m", "not integration"])
