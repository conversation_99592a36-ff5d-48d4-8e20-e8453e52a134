"""
API密钥配置模板
复制此文件为 api_keys.py 并填入真实的API密钥
"""

# Etherscan API Key
ETHERSCAN_API_KEY = "YOUR_ETHERSCAN_API_KEY_HERE"

# Moralis API Key
MORALIS_API_KEY = "YOUR_MORALIS_API_KEY_HERE"

# CoinGecko API Key (Pro版本)
COINGECKO_API_KEY = "YOUR_COINGECKO_API_KEY_HERE"

# Dune Analytics API Key
DUNE_API_KEY = "YOUR_DUNE_API_KEY_HERE"

# Infura Project ID
INFURA_PROJECT_ID = "YOUR_INFURA_PROJECT_ID_HERE"

# Alchemy API Key
ALCHEMY_API_KEY = "YOUR_ALCHEMY_API_KEY_HERE"

# QuickNode Endpoint
QUICKNODE_URL = "YOUR_QUICKNODE_URL_HERE"

# Telegram Bot配置
TELEGRAM_BOT_TOKEN = "YOUR_TELEGRAM_BOT_TOKEN_HERE"
TELEGRAM_CHAT_ID = "YOUR_TELEGRAM_CHAT_ID_HERE"

# Discord Webhook
DISCORD_WEBHOOK_URL = "YOUR_DISCORD_WEBHOOK_URL_HERE"

# 邮件配置
SMTP_SERVER = "smtp.gmail.com"
SMTP_PORT = 587
EMAIL_USER = "<EMAIL>"
EMAIL_PASSWORD = "your_app_password"
ALERT_EMAIL = "<EMAIL>"

# 数据库配置
DB_HOST = "localhost"
DB_PORT = 5432
DB_NAME = "onchain_analysis"
DB_USER = "postgres"
DB_PASSWORD = "your_db_password"

# Redis配置
REDIS_HOST = "localhost"
REDIS_PORT = 6379
REDIS_PASSWORD = ""

# Web应用配置
SECRET_KEY = "your-very-secret-key-here-change-this-in-production"
DEBUG = True

# RPC节点配置
ETH_RPC_URL = f"https://mainnet.infura.io/v3/{INFURA_PROJECT_ID}"
BSC_RPC_URL = "https://bsc-dataseed.binance.org/"
POLYGON_RPC_URL = "https://polygon-rpc.com/"
