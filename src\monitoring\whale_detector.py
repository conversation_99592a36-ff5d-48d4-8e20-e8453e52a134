"""
巨鲸检测器
识别和监控大额交易和巨鲸地址活动
"""
import asyncio
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timezone, timedelta
from dataclasses import dataclass
from enum import Enum
import numpy as np

from src.database.repositories import (
    AddressRepository, TransactionRepository, WhaleAlertRepository
)
from src.database.data_service import DataService
from src.utils.logger import get_logger
from config.settings import MONITORING_CONFIG

logger = get_logger(__name__)


class WhaleType(Enum):
    """巨鲸类型"""
    HOLDER = "holder"           # 持有型巨鲸
    TRADER = "trader"           # 交易型巨鲸
    EXCHANGE = "exchange"       # 交易所巨鲸
    INSTITUTION = "institution" # 机构巨鲸
    UNKNOWN = "unknown"         # 未知类型


class AlertLevel(Enum):
    """预警级别"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class WhaleProfile:
    """巨鲸档案"""
    address: str
    whale_type: WhaleType
    balance_eth: float
    balance_usd: float
    total_volume_usd: float
    transaction_count: int
    first_seen: datetime
    last_active: datetime
    risk_score: float
    influence_score: float
    trading_pattern: Dict[str, Any]
    associated_addresses: List[str]
    labels: List[str]


@dataclass
class WhaleTransaction:
    """巨鲸交易"""
    tx_hash: str
    from_address: str
    to_address: str
    value_usd: float
    token_symbol: str
    transaction_type: str
    timestamp: datetime
    alert_level: AlertLevel
    impact_score: float
    market_context: Dict[str, Any]


class WhaleDetector:
    """巨鲸检测器"""
    
    def __init__(self):
        self.address_repo = AddressRepository()
        self.transaction_repo = TransactionRepository()
        self.whale_alert_repo = WhaleAlertRepository()
        self.data_service = None
        
        # 巨鲸阈值配置
        self.thresholds = MONITORING_CONFIG['whale_threshold']
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.data_service = await DataService().__aenter__()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.data_service:
            await self.data_service.__aexit__(exc_type, exc_val, exc_tb)
    
    async def scan_for_whales(self, hours: int = 24) -> List[WhaleProfile]:
        """扫描识别巨鲸地址"""
        try:
            logger.info(f"Scanning for whale addresses in the last {hours} hours")
            
            # 获取大额交易
            large_transactions = self.transaction_repo.get_large_transactions(
                min_value_usd=self.thresholds['usdt'],
                hours=hours,
                limit=1000
            )
            
            # 提取涉及的地址
            whale_candidates = set()
            for tx in large_transactions:
                whale_candidates.add(tx.from_address)
                if tx.to_address:
                    whale_candidates.add(tx.to_address)
            
            # 分析每个候选地址
            whale_profiles = []
            for address in whale_candidates:
                try:
                    profile = await self._analyze_whale_candidate(address)
                    if profile and self._is_whale(profile):
                        whale_profiles.append(profile)
                        
                        # 更新数据库中的巨鲸标记
                        await self._update_whale_status(address, True, profile)
                        
                        logger.info(f"Identified whale: {address} "
                                  f"(balance: ${profile.balance_usd:,.0f}, "
                                  f"type: {profile.whale_type.value})")
                
                except Exception as e:
                    logger.error(f"Failed to analyze whale candidate {address}: {e}")
                    continue
            
            # 按影响力分数排序
            whale_profiles.sort(key=lambda x: x.influence_score, reverse=True)
            
            logger.info(f"Identified {len(whale_profiles)} whale addresses")
            return whale_profiles
        
        except Exception as e:
            logger.error(f"Failed to scan for whales: {e}")
            raise
    
    async def _analyze_whale_candidate(self, address: str) -> Optional[WhaleProfile]:
        """分析巨鲸候选地址"""
        try:
            # 获取地址基本信息
            balance_data = await self.data_service.get_address_balance(address)
            transactions = await self.data_service.get_address_transactions(address, limit=500)
            token_transfers = await self.data_service.get_token_transfers(address, limit=500)
            
            if not transactions and not token_transfers:
                return None
            
            # 计算基本指标
            balance_eth = balance_data['balance_eth']
            balance_usd = balance_eth * 2000  # 假设ETH价格
            
            total_volume_usd = sum(tx.get('value_usd', 0) for tx in transactions)
            transaction_count = len(transactions) + len(token_transfers)
            
            # 分析时间范围
            all_txs = transactions + token_transfers
            timestamps = [tx['timestamp'] for tx in all_txs]
            first_seen = min(timestamps) if timestamps else datetime.now(timezone.utc)
            last_active = max(timestamps) if timestamps else datetime.now(timezone.utc)
            
            # 识别巨鲸类型
            whale_type = self._classify_whale_type(address, transactions, token_transfers, balance_eth)
            
            # 计算风险分数
            risk_score = self._calculate_risk_score(transactions, token_transfers, balance_eth)
            
            # 计算影响力分数
            influence_score = self._calculate_influence_score(
                balance_usd, total_volume_usd, transaction_count, whale_type
            )
            
            # 分析交易模式
            trading_pattern = self._analyze_trading_pattern(transactions, token_transfers)
            
            # 查找关联地址
            associated_addresses = await self._find_associated_addresses(address, transactions)
            
            # 生成标签
            labels = self._generate_labels(whale_type, trading_pattern, balance_eth)
            
            return WhaleProfile(
                address=address,
                whale_type=whale_type,
                balance_eth=balance_eth,
                balance_usd=balance_usd,
                total_volume_usd=total_volume_usd,
                transaction_count=transaction_count,
                first_seen=first_seen,
                last_active=last_active,
                risk_score=risk_score,
                influence_score=influence_score,
                trading_pattern=trading_pattern,
                associated_addresses=associated_addresses,
                labels=labels
            )
        
        except Exception as e:
            logger.error(f"Failed to analyze whale candidate {address}: {e}")
            return None
    
    def _classify_whale_type(self, address: str, transactions: List[Dict[str, Any]], 
                           token_transfers: List[Dict[str, Any]], balance_eth: float) -> WhaleType:
        """分类巨鲸类型"""
        try:
            # 检查是否为已知交易所地址
            known_exchanges = [
                '******************************************',  # Binance
                '******************************************',  # Binance 2
                '******************************************',  # FTX
            ]
            
            if address.lower() in [addr.lower() for addr in known_exchanges]:
                return WhaleType.EXCHANGE
            
            # 分析交易模式
            total_txs = len(transactions) + len(token_transfers)
            
            if total_txs == 0:
                return WhaleType.HOLDER
            
            # 计算交易频率
            if transactions:
                time_span = (max(tx['timestamp'] for tx in transactions) - 
                           min(tx['timestamp'] for tx in transactions)).days
                tx_per_day = total_txs / max(time_span, 1)
            else:
                tx_per_day = 0
            
            # 分析交易对手数量
            counterparties = set()
            for tx in transactions:
                if tx['from_address'].lower() == address.lower():
                    counterparties.add(tx['to_address'])
                else:
                    counterparties.add(tx['from_address'])
            
            unique_counterparties = len(counterparties)
            
            # 分类逻辑
            if tx_per_day > 10 and unique_counterparties > 100:
                return WhaleType.EXCHANGE
            elif tx_per_day > 1 and unique_counterparties > 20:
                return WhaleType.TRADER
            elif balance_eth > 10000 and tx_per_day < 0.1:
                return WhaleType.HOLDER
            elif unique_counterparties > 50:
                return WhaleType.INSTITUTION
            else:
                return WhaleType.UNKNOWN
        
        except Exception as e:
            logger.error(f"Failed to classify whale type: {e}")
            return WhaleType.UNKNOWN
    
    def _calculate_risk_score(self, transactions: List[Dict[str, Any]], 
                            token_transfers: List[Dict[str, Any]], balance_eth: float) -> float:
        """计算风险分数"""
        try:
            risk_factors = []
            
            # 余额风险：余额越大风险越高
            balance_risk = min(balance_eth / 100000, 1.0)  # 10万ETH为满分
            risk_factors.append(balance_risk * 0.3)
            
            # 交易量风险
            total_volume = sum(tx.get('value_eth', 0) for tx in transactions)
            volume_risk = min(total_volume / 1000000, 1.0)  # 100万ETH交易量为满分
            risk_factors.append(volume_risk * 0.3)
            
            # 活跃度风险：过于活跃可能是交易所或机器人
            total_txs = len(transactions) + len(token_transfers)
            if total_txs > 0:
                time_span_days = 365  # 假设一年时间范围
                activity_risk = min(total_txs / (time_span_days * 10), 1.0)  # 每天10笔交易为满分
                risk_factors.append(activity_risk * 0.2)
            
            # 集中度风险：交易对手过于集中
            if transactions:
                counterparties = []
                for tx in transactions:
                    if tx['from_address'].lower() != tx['to_address'].lower():
                        counterparties.append(tx['to_address'] if tx['from_address'].lower() == transactions[0]['from_address'].lower() else tx['from_address'])
                
                if counterparties:
                    from collections import Counter
                    counter = Counter(counterparties)
                    top_counterparty_ratio = counter.most_common(1)[0][1] / len(counterparties)
                    concentration_risk = top_counterparty_ratio
                    risk_factors.append(concentration_risk * 0.2)
            
            return sum(risk_factors)
        
        except Exception as e:
            logger.error(f"Failed to calculate risk score: {e}")
            return 0.5
    
    def _calculate_influence_score(self, balance_usd: float, total_volume_usd: float, 
                                 transaction_count: int, whale_type: WhaleType) -> float:
        """计算影响力分数"""
        try:
            # 基础分数基于余额
            balance_score = min(balance_usd / 100000000, 1.0)  # 1亿美元为满分
            
            # 交易量分数
            volume_score = min(total_volume_usd / 1000000000, 1.0)  # 10亿美元为满分
            
            # 活跃度分数
            activity_score = min(transaction_count / 10000, 1.0)  # 1万笔交易为满分
            
            # 类型权重
            type_weights = {
                WhaleType.INSTITUTION: 1.2,
                WhaleType.TRADER: 1.0,
                WhaleType.HOLDER: 0.8,
                WhaleType.EXCHANGE: 1.1,
                WhaleType.UNKNOWN: 0.7
            }
            
            type_weight = type_weights.get(whale_type, 1.0)
            
            # 综合影响力分数
            influence_score = (
                balance_score * 0.4 +
                volume_score * 0.3 +
                activity_score * 0.3
            ) * type_weight
            
            return min(influence_score, 1.0)
        
        except Exception as e:
            logger.error(f"Failed to calculate influence score: {e}")
            return 0.0
    
    def _analyze_trading_pattern(self, transactions: List[Dict[str, Any]], 
                               token_transfers: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析交易模式"""
        try:
            pattern = {
                'avg_transaction_size': 0,
                'transaction_frequency': 0,
                'preferred_hours': [],
                'token_diversity': 0,
                'inflow_outflow_ratio': 0,
                'gas_usage_pattern': 'normal'
            }
            
            if not transactions:
                return pattern
            
            # 平均交易规模
            values = [tx.get('value_usd', 0) for tx in transactions if tx.get('value_usd', 0) > 0]
            pattern['avg_transaction_size'] = np.mean(values) if values else 0
            
            # 交易频率
            if len(transactions) > 1:
                time_span = (max(tx['timestamp'] for tx in transactions) - 
                           min(tx['timestamp'] for tx in transactions)).total_seconds()
                pattern['transaction_frequency'] = len(transactions) / (time_span / 86400)  # 每天交易数
            
            # 偏好时间
            hours = [tx['timestamp'].hour for tx in transactions]
            from collections import Counter
            hour_counter = Counter(hours)
            pattern['preferred_hours'] = [hour for hour, count in hour_counter.most_common(3)]
            
            # 代币多样性
            tokens = set(transfer['token_symbol'] for transfer in token_transfers)
            pattern['token_diversity'] = len(tokens)
            
            # 资金流入流出比例
            address = transactions[0]['from_address'] if transactions else ''
            inflows = [tx for tx in transactions if tx['to_address'].lower() == address.lower()]
            outflows = [tx for tx in transactions if tx['from_address'].lower() == address.lower()]
            
            inflow_volume = sum(tx.get('value_usd', 0) for tx in inflows)
            outflow_volume = sum(tx.get('value_usd', 0) for tx in outflows)
            
            if outflow_volume > 0:
                pattern['inflow_outflow_ratio'] = inflow_volume / outflow_volume
            
            # Gas使用模式
            gas_prices = [tx.get('gas_price', 0) for tx in transactions if tx.get('gas_price', 0) > 0]
            if gas_prices:
                avg_gas = np.mean(gas_prices)
                if avg_gas > 50e9:  # 50 Gwei
                    pattern['gas_usage_pattern'] = 'high'
                elif avg_gas < 10e9:  # 10 Gwei
                    pattern['gas_usage_pattern'] = 'low'
            
            return pattern
        
        except Exception as e:
            logger.error(f"Failed to analyze trading pattern: {e}")
            return {}
    
    async def _find_associated_addresses(self, address: str, 
                                       transactions: List[Dict[str, Any]]) -> List[str]:
        """查找关联地址"""
        try:
            # 分析频繁交互的地址
            counterparties = {}
            
            for tx in transactions:
                if tx['from_address'].lower() == address.lower():
                    counterparty = tx['to_address']
                else:
                    counterparty = tx['from_address']
                
                if counterparty:
                    counterparties[counterparty] = counterparties.get(counterparty, 0) + 1
            
            # 找出交互次数最多的地址
            frequent_counterparties = [
                addr for addr, count in counterparties.items() 
                if count >= 5  # 至少交互5次
            ]
            
            return frequent_counterparties[:10]  # 最多返回10个关联地址
        
        except Exception as e:
            logger.error(f"Failed to find associated addresses: {e}")
            return []
    
    def _generate_labels(self, whale_type: WhaleType, trading_pattern: Dict[str, Any], 
                        balance_eth: float) -> List[str]:
        """生成标签"""
        labels = []
        
        # 类型标签
        labels.append(whale_type.value)
        
        # 余额标签
        if balance_eth > 100000:
            labels.append('mega_whale')
        elif balance_eth > 10000:
            labels.append('large_whale')
        elif balance_eth > 1000:
            labels.append('whale')
        
        # 活跃度标签
        freq = trading_pattern.get('transaction_frequency', 0)
        if freq > 10:
            labels.append('very_active')
        elif freq > 1:
            labels.append('active')
        elif freq < 0.1:
            labels.append('dormant')
        
        # 多样性标签
        diversity = trading_pattern.get('token_diversity', 0)
        if diversity > 20:
            labels.append('diversified')
        elif diversity > 5:
            labels.append('multi_token')
        
        # Gas使用标签
        gas_pattern = trading_pattern.get('gas_usage_pattern', 'normal')
        if gas_pattern != 'normal':
            labels.append(f'{gas_pattern}_gas')
        
        return labels
    
    def _is_whale(self, profile: WhaleProfile) -> bool:
        """判断是否为巨鲸"""
        return (
            profile.balance_usd >= self.thresholds['usdt'] or
            profile.total_volume_usd >= self.thresholds['usdt'] * 10 or
            profile.influence_score >= 0.3
        )
    
    async def _update_whale_status(self, address: str, is_whale: bool, 
                                 profile: WhaleProfile) -> None:
        """更新巨鲸状态"""
        try:
            stats = {
                'is_whale': is_whale,
                'address_type': profile.whale_type.value,
                'total_volume_usd': profile.total_volume_usd,
                'risk_score': profile.risk_score,
                'last_active': profile.last_active,
                'tags': profile.labels
            }
            
            self.address_repo.update_address_stats(address, stats)
            logger.debug(f"Updated whale status for {address}")
        
        except Exception as e:
            logger.error(f"Failed to update whale status for {address}: {e}")
