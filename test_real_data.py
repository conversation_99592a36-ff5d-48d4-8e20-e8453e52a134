"""
测试真实数据获取 - 验证WebSocket和API数据源
"""
import asyncio
import websockets
import json
import requests
import time
from datetime import datetime

async def test_binance_websocket():
    """测试Binance WebSocket实时价格"""
    print("🔌 测试Binance WebSocket连接...")
    
    try:
        uri = "wss://stream.binance.com:9443/ws/btcusdt@ticker"
        
        async with websockets.connect(uri) as websocket:
            print("✅ Binance WebSocket连接成功")
            
            # 接收5条消息进行测试
            for i in range(5):
                try:
                    message = await asyncio.wait_for(websocket.recv(), timeout=10)
                    data = json.loads(message)
                    
                    price = float(data['c'])
                    change = float(data['P'])
                    volume = float(data['v'])
                    
                    print(f"📊 实时价格 #{i+1}: ${price:,.2f} ({change:+.2f}%) 成交量: {volume:,.2f}")
                    
                except asyncio.TimeoutError:
                    print("⏰ WebSocket超时")
                    break
                except Exception as e:
                    print(f"❌ 数据解析错误: {e}")
                    break
                    
    except Exception as e:
        print(f"❌ WebSocket连接失败: {e}")

async def test_coingecko_api():
    """测试CoinGecko API"""
    print("\n🌐 测试CoinGecko API...")
    
    try:
        url = "https://api.coingecko.com/api/v3/simple/price"
        params = {
            'ids': 'bitcoin',
            'vs_currencies': 'usd',
            'include_24hr_change': 'true',
            'include_24hr_vol': 'true',
            'include_market_cap': 'true',
            'include_last_updated_at': 'true'
        }
        
        response = requests.get(url, params=params, timeout=10)
        
        if response.status_code == 200:
            data = response.json()['bitcoin']
            
            print("✅ CoinGecko API连接成功")
            print(f"💰 当前价格: ${data['usd']:,.2f}")
            print(f"📈 24h变化: {data['usd_24h_change']:+.2f}%")
            print(f"📊 24h成交量: ${data['usd_24h_vol']:,.0f}")
            print(f"🏦 市值: ${data['usd_market_cap']:,.0f}")
            print(f"🕐 更新时间: {datetime.fromtimestamp(data['last_updated_at']).strftime('%Y-%m-%d %H:%M:%S')}")
            
            return data
        else:
            print(f"❌ API请求失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ CoinGecko API错误: {e}")
        return None

async def test_binance_api():
    """测试Binance API"""
    print("\n🟡 测试Binance API...")
    
    try:
        url = "https://api.binance.com/api/v3/ticker/24hr"
        params = {'symbol': 'BTCUSDT'}
        
        response = requests.get(url, params=params, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            
            print("✅ Binance API连接成功")
            print(f"💰 当前价格: ${float(data['lastPrice']):,.2f}")
            print(f"📈 24h变化: {float(data['priceChangePercent']):+.2f}%")
            print(f"📊 24h成交量: {float(data['volume']):,.2f} BTC")
            print(f"📊 24h成交额: ${float(data['quoteVolume']):,.0f}")
            print(f"🔺 24h最高: ${float(data['highPrice']):,.2f}")
            print(f"🔻 24h最低: ${float(data['lowPrice']):,.2f}")
            
            return data
        else:
            print(f"❌ API请求失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Binance API错误: {e}")
        return None

async def test_blockchain_info_api():
    """测试Blockchain.info API"""
    print("\n⛓️ 测试Blockchain.info API...")
    
    try:
        url = "https://blockchain.info/stats?format=json"
        
        response = requests.get(url, timeout=15)
        
        if response.status_code == 200:
            data = response.json()
            
            print("✅ Blockchain.info API连接成功")
            print(f"🪙 总供应量: {data['totalbc'] / 1e8:,.2f} BTC")
            print(f"💰 市场价格: ${data['market_price_usd']:,.2f}")
            print(f"⚡ 哈希率: {data['hash_rate']:.2e} H/s")
            print(f"🎯 难度: {data['difficulty']:,.0f}")
            print(f"⏱️ 出块间隔: {data['minutes_between_blocks']:.1f} 分钟")
            print(f"📝 总交易数: {data['n_tx']:,}")
            print(f"🧱 总区块数: {data['n_blocks_mined']:,}")
            
            return data
        else:
            print(f"❌ API请求失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Blockchain.info API错误: {e}")
        return None

async def test_coinbase_api():
    """测试Coinbase API"""
    print("\n🔵 测试Coinbase API...")
    
    try:
        url = "https://api.exchange.coinbase.com/products/BTC-USD/ticker"
        
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            
            print("✅ Coinbase API连接成功")
            print(f"💰 当前价格: ${float(data['price']):,.2f}")
            print(f"📊 24h成交量: {float(data['volume']):,.2f} BTC")
            print(f"🔺 买一价: ${float(data['bid']):,.2f}")
            print(f"🔻 卖一价: ${float(data['ask']):,.2f}")
            
            return data
        else:
            print(f"❌ API请求失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Coinbase API错误: {e}")
        return None

async def compare_price_sources():
    """比较多个价格源"""
    print("\n🔍 比较多个价格源...")
    
    prices = {}
    
    # 获取各个源的价格
    cg_data = await test_coingecko_api()
    if cg_data:
        prices['CoinGecko'] = cg_data['usd']
    
    binance_data = await test_binance_api()
    if binance_data:
        prices['Binance'] = float(binance_data['lastPrice'])
    
    coinbase_data = await test_coinbase_api()
    if coinbase_data:
        prices['Coinbase'] = float(coinbase_data['price'])
    
    # 比较价格
    if len(prices) >= 2:
        print(f"\n📊 价格源比较:")
        price_values = list(prices.values())
        avg_price = sum(price_values) / len(price_values)
        
        for source, price in prices.items():
            diff = (price - avg_price) / avg_price * 100
            print(f"  {source}: ${price:,.2f} ({diff:+.3f}%)")
        
        print(f"  平均价格: ${avg_price:,.2f}")
        
        # 计算最大差异
        max_diff = max(abs(p - avg_price) / avg_price for p in price_values) * 100
        print(f"  最大差异: {max_diff:.3f}%")
        
        if max_diff > 1:
            print("⚠️ 价格源之间差异较大，请注意")
        else:
            print("✅ 价格源数据一致性良好")
    
    return prices

async def test_blockchain_websocket():
    """测试区块链WebSocket"""
    print("\n🔌 测试Blockchain.info WebSocket...")
    
    try:
        uri = "wss://ws.blockchain.info/inv"
        
        async with websockets.connect(uri) as websocket:
            print("✅ Blockchain WebSocket连接成功")
            
            # 订阅新交易
            subscribe_msg = {"op": "unconfirmed_sub"}
            await websocket.send(json.dumps(subscribe_msg))
            print("📝 已订阅新交易")
            
            # 接收几条交易数据
            for i in range(3):
                try:
                    message = await asyncio.wait_for(websocket.recv(), timeout=30)
                    data = json.loads(message)
                    
                    if data.get('op') == 'utx':
                        tx = data['x']
                        print(f"🆕 新交易 #{i+1}: {tx['hash'][:16]}... (费用: {tx['fee']} sat)")
                    
                except asyncio.TimeoutError:
                    print("⏰ 等待新交易超时")
                    break
                except Exception as e:
                    print(f"❌ 数据解析错误: {e}")
                    break
                    
    except Exception as e:
        print(f"❌ Blockchain WebSocket连接失败: {e}")

async def main():
    """主测试函数"""
    print("🚀 OnChain Analytics Platform - 真实数据源测试")
    print("=" * 70)
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🎯 目标: 验证所有免费数据源的可用性")
    
    # 测试所有数据源
    await test_coingecko_api()
    await test_binance_api()
    await test_coinbase_api()
    await test_blockchain_info_api()
    
    # 比较价格源
    prices = await compare_price_sources()
    
    # 测试WebSocket连接
    print(f"\n" + "="*50)
    print("🔌 测试实时数据流")
    print("="*50)
    
    await test_binance_websocket()
    await test_blockchain_websocket()
    
    # 总结
    print(f"\n" + "="*70)
    print("📊 数据源测试总结")
    print("="*70)
    
    if prices:
        print(f"✅ 成功获取 {len(prices)} 个价格源数据")
        print(f"💰 当前BTC价格范围: ${min(prices.values()):,.2f} - ${max(prices.values()):,.2f}")
    else:
        print("❌ 未能获取价格数据")
    
    print(f"\n💡 数据源状态:")
    print(f"  • CoinGecko API: 免费，稳定")
    print(f"  • Binance API: 免费，高频")
    print(f"  • Coinbase API: 免费，可靠")
    print(f"  • Blockchain.info: 免费，链上数据")
    print(f"  • WebSocket流: 实时，低延迟")
    
    print(f"\n🎉 真实数据获取能力验证完成！")

if __name__ == "__main__":
    asyncio.run(main())
