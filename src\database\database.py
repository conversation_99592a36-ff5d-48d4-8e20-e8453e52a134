"""
数据库操作模块
提供数据库连接和基础操作功能
"""
import os
from contextlib import contextmanager
from typing import Optional, List, Dict, Any, Generator
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import QueuePool
from sqlalchemy.exc import SQLAlchemyError
import redis
from redis import Redis

from config.settings import DATABASE_CONFIG, REDIS_CONFIG
from src.utils.logger import get_logger
from .models import Base

logger = get_logger(__name__)


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self.engine = None
        self.SessionLocal = None
        self.redis_client = None
        self._initialize_database()
        self._initialize_redis()
    
    def _initialize_database(self):
        """初始化数据库连接"""
        try:
            # 构建数据库URL
            db_url = (
                f"postgresql://{DATABASE_CONFIG['username']}:"
                f"{DATABASE_CONFIG['password']}@"
                f"{DATABASE_CONFIG['host']}:"
                f"{DATABASE_CONFIG['port']}/"
                f"{DATABASE_CONFIG['database']}"
            )
            
            # 创建引擎
            self.engine = create_engine(
                db_url,
                poolclass=QueuePool,
                pool_size=DATABASE_CONFIG['pool_size'],
                max_overflow=DATABASE_CONFIG['max_overflow'],
                pool_pre_ping=True,
                echo=False  # 设置为True可以看到SQL语句
            )
            
            # 创建会话工厂
            self.SessionLocal = sessionmaker(
                autocommit=False,
                autoflush=False,
                bind=self.engine
            )
            
            logger.info("Database connection initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize database: {e}")
            raise
    
    def _initialize_redis(self):
        """初始化Redis连接"""
        try:
            self.redis_client = redis.Redis(
                host=REDIS_CONFIG['host'],
                port=REDIS_CONFIG['port'],
                db=REDIS_CONFIG['db'],
                password=REDIS_CONFIG['password'],
                decode_responses=REDIS_CONFIG['decode_responses'],
                socket_connect_timeout=5,
                socket_timeout=5,
                retry_on_timeout=True
            )
            
            # 测试连接
            self.redis_client.ping()
            logger.info("Redis connection initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Redis: {e}")
            # Redis连接失败不应该阻止应用启动
            self.redis_client = None
    
    def create_tables(self):
        """创建所有数据表"""
        try:
            Base.metadata.create_all(bind=self.engine)
            logger.info("Database tables created successfully")
        except Exception as e:
            logger.error(f"Failed to create tables: {e}")
            raise
    
    def drop_tables(self):
        """删除所有数据表"""
        try:
            Base.metadata.drop_all(bind=self.engine)
            logger.info("Database tables dropped successfully")
        except Exception as e:
            logger.error(f"Failed to drop tables: {e}")
            raise
    
    @contextmanager
    def get_session(self) -> Generator[Session, None, None]:
        """获取数据库会话上下文管理器"""
        session = self.SessionLocal()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            logger.error(f"Database session error: {e}")
            raise
        finally:
            session.close()
    
    def get_redis(self) -> Optional[Redis]:
        """获取Redis客户端"""
        return self.redis_client
    
    def execute_raw_sql(self, sql: str, params: Dict[str, Any] = None) -> List[Dict]:
        """执行原始SQL查询"""
        try:
            with self.get_session() as session:
                result = session.execute(text(sql), params or {})
                return [dict(row) for row in result.fetchall()]
        except Exception as e:
            logger.error(f"Failed to execute SQL: {sql}, error: {e}")
            raise
    
    def health_check(self) -> Dict[str, bool]:
        """健康检查"""
        health = {
            'database': False,
            'redis': False
        }
        
        # 检查数据库连接
        try:
            with self.get_session() as session:
                session.execute(text("SELECT 1"))
                health['database'] = True
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
        
        # 检查Redis连接
        try:
            if self.redis_client:
                self.redis_client.ping()
                health['redis'] = True
        except Exception as e:
            logger.error(f"Redis health check failed: {e}")
        
        return health


# 全局数据库管理器实例
db_manager = DatabaseManager()


class BaseRepository:
    """基础仓库类"""
    
    def __init__(self, model_class):
        self.model_class = model_class
        self.db_manager = db_manager
    
    def create(self, **kwargs) -> Any:
        """创建记录"""
        try:
            with self.db_manager.get_session() as session:
                instance = self.model_class(**kwargs)
                session.add(instance)
                session.flush()
                session.refresh(instance)
                return instance
        except Exception as e:
            logger.error(f"Failed to create {self.model_class.__name__}: {e}")
            raise
    
    def get_by_id(self, id: int) -> Optional[Any]:
        """根据ID获取记录"""
        try:
            with self.db_manager.get_session() as session:
                return session.query(self.model_class).filter(
                    self.model_class.id == id
                ).first()
        except Exception as e:
            logger.error(f"Failed to get {self.model_class.__name__} by id {id}: {e}")
            raise
    
    def get_all(self, limit: int = 100, offset: int = 0) -> List[Any]:
        """获取所有记录"""
        try:
            with self.db_manager.get_session() as session:
                return session.query(self.model_class).offset(offset).limit(limit).all()
        except Exception as e:
            logger.error(f"Failed to get all {self.model_class.__name__}: {e}")
            raise
    
    def update(self, id: int, **kwargs) -> Optional[Any]:
        """更新记录"""
        try:
            with self.db_manager.get_session() as session:
                instance = session.query(self.model_class).filter(
                    self.model_class.id == id
                ).first()
                
                if instance:
                    for key, value in kwargs.items():
                        if hasattr(instance, key):
                            setattr(instance, key, value)
                    session.flush()
                    session.refresh(instance)
                    return instance
                return None
        except Exception as e:
            logger.error(f"Failed to update {self.model_class.__name__} {id}: {e}")
            raise
    
    def delete(self, id: int) -> bool:
        """删除记录"""
        try:
            with self.db_manager.get_session() as session:
                instance = session.query(self.model_class).filter(
                    self.model_class.id == id
                ).first()
                
                if instance:
                    session.delete(instance)
                    return True
                return False
        except Exception as e:
            logger.error(f"Failed to delete {self.model_class.__name__} {id}: {e}")
            raise
    
    def count(self) -> int:
        """获取记录总数"""
        try:
            with self.db_manager.get_session() as session:
                return session.query(self.model_class).count()
        except Exception as e:
            logger.error(f"Failed to count {self.model_class.__name__}: {e}")
            raise
    
    def bulk_create(self, records: List[Dict[str, Any]]) -> List[Any]:
        """批量创建记录"""
        try:
            with self.db_manager.get_session() as session:
                instances = [self.model_class(**record) for record in records]
                session.add_all(instances)
                session.flush()
                for instance in instances:
                    session.refresh(instance)
                return instances
        except Exception as e:
            logger.error(f"Failed to bulk create {self.model_class.__name__}: {e}")
            raise
    
    def bulk_update(self, updates: List[Dict[str, Any]]) -> int:
        """批量更新记录"""
        try:
            with self.db_manager.get_session() as session:
                count = 0
                for update_data in updates:
                    id_value = update_data.pop('id')
                    result = session.query(self.model_class).filter(
                        self.model_class.id == id_value
                    ).update(update_data)
                    count += result
                return count
        except Exception as e:
            logger.error(f"Failed to bulk update {self.model_class.__name__}: {e}")
            raise


class CacheManager:
    """缓存管理器"""
    
    def __init__(self):
        self.redis_client = db_manager.get_redis()
    
    def get(self, key: str) -> Optional[str]:
        """获取缓存值"""
        if not self.redis_client:
            return None
        
        try:
            return self.redis_client.get(key)
        except Exception as e:
            logger.error(f"Failed to get cache key {key}: {e}")
            return None
    
    def set(self, key: str, value: str, expire: int = 300) -> bool:
        """设置缓存值"""
        if not self.redis_client:
            return False
        
        try:
            return self.redis_client.setex(key, expire, value)
        except Exception as e:
            logger.error(f"Failed to set cache key {key}: {e}")
            return False
    
    def delete(self, key: str) -> bool:
        """删除缓存值"""
        if not self.redis_client:
            return False
        
        try:
            return bool(self.redis_client.delete(key))
        except Exception as e:
            logger.error(f"Failed to delete cache key {key}: {e}")
            return False
    
    def exists(self, key: str) -> bool:
        """检查缓存键是否存在"""
        if not self.redis_client:
            return False
        
        try:
            return bool(self.redis_client.exists(key))
        except Exception as e:
            logger.error(f"Failed to check cache key {key}: {e}")
            return False


# 全局缓存管理器实例
cache_manager = CacheManager()
